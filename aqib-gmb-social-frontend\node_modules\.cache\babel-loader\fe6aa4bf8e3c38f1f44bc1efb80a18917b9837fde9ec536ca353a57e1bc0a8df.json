{"ast": null, "code": "'use strict';\n\nvar functionsHaveNames = function functionsHaveNames() {\n  return typeof function f() {}.name === 'string';\n};\nvar gOPD = Object.getOwnPropertyDescriptor;\nif (gOPD) {\n  try {\n    gOPD([], 'length');\n  } catch (e) {\n    // IE 8 has a broken gOPD\n    gOPD = null;\n  }\n}\nfunctionsHaveNames.functionsHaveConfigurableNames = function functionsHaveConfigurableNames() {\n  if (!functionsHaveNames() || !gOPD) {\n    return false;\n  }\n  var desc = gOPD(function () {}, 'name');\n  return !!desc && !!desc.configurable;\n};\nvar $bind = Function.prototype.bind;\nfunctionsHaveNames.boundFunctionsHaveNames = function boundFunctionsHaveNames() {\n  return functionsHaveNames() && typeof $bind === 'function' && function f() {}.bind().name !== '';\n};\nmodule.exports = functionsHaveNames;", "map": {"version": 3, "names": ["functionsHaveNames", "f", "name", "gOPD", "Object", "getOwnPropertyDescriptor", "e", "functionsHaveConfigurableNames", "desc", "configurable", "$bind", "Function", "prototype", "bind", "boundFunctionsHaveNames", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/functions-have-names/index.js"], "sourcesContent": ["'use strict';\n\nvar functionsHaveNames = function functionsHaveNames() {\n\treturn typeof function f() {}.name === 'string';\n};\n\nvar gOPD = Object.getOwnPropertyDescriptor;\nif (gOPD) {\n\ttry {\n\t\tgOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\tgOPD = null;\n\t}\n}\n\nfunctionsHaveNames.functionsHaveConfigurableNames = function functionsHaveConfigurableNames() {\n\tif (!functionsHaveNames() || !gOPD) {\n\t\treturn false;\n\t}\n\tvar desc = gOPD(function () {}, 'name');\n\treturn !!desc && !!desc.configurable;\n};\n\nvar $bind = Function.prototype.bind;\n\nfunctionsHaveNames.boundFunctionsHaveNames = function boundFunctionsHaveNames() {\n\treturn functionsHaveNames() && typeof $bind === 'function' && function f() {}.bind().name !== '';\n};\n\nmodule.exports = functionsHaveNames;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;EACtD,OAAO,OAAO,SAASC,CAACA,CAAA,EAAG,CAAC,CAAC,CAACC,IAAI,KAAK,QAAQ;AAChD,CAAC;AAED,IAAIC,IAAI,GAAGC,MAAM,CAACC,wBAAwB;AAC1C,IAAIF,IAAI,EAAE;EACT,IAAI;IACHA,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EACnB,CAAC,CAAC,OAAOG,CAAC,EAAE;IACX;IACAH,IAAI,GAAG,IAAI;EACZ;AACD;AAEAH,kBAAkB,CAACO,8BAA8B,GAAG,SAASA,8BAA8BA,CAAA,EAAG;EAC7F,IAAI,CAACP,kBAAkB,CAAC,CAAC,IAAI,CAACG,IAAI,EAAE;IACnC,OAAO,KAAK;EACb;EACA,IAAIK,IAAI,GAAGL,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC;EACvC,OAAO,CAAC,CAACK,IAAI,IAAI,CAAC,CAACA,IAAI,CAACC,YAAY;AACrC,CAAC;AAED,IAAIC,KAAK,GAAGC,QAAQ,CAACC,SAAS,CAACC,IAAI;AAEnCb,kBAAkB,CAACc,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;EAC/E,OAAOd,kBAAkB,CAAC,CAAC,IAAI,OAAOU,KAAK,KAAK,UAAU,IAAI,SAAST,CAACA,CAAA,EAAG,CAAC,CAAC,CAACY,IAAI,CAAC,CAAC,CAACX,IAAI,KAAK,EAAE;AACjG,CAAC;AAEDa,MAAM,CAACC,OAAO,GAAGhB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}