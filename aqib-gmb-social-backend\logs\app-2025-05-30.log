{"timestamp":"2025-05-30T13:41:11.154Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T13:41:24.968Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:11:27.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:41:37.157Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:11:39.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:42:02.896Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:12:05.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:42:41.760Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:12:44.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:43:09.867Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:13:12.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:43:42.338Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:13:44.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:44:03.072Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:14:05.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:44:20.516Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:14:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:44:53.182Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:14:55.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:46:05.938Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:16:08.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:46:22.013Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:16:24.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:51:20.398Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:21:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:51:37.480Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T08:21:40.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T13:52:18.410Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4acf8327-d10c-49c1-9c67-c5b1415fefb7","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-05-30T13:52:18.413Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"4acf8327-d10c-49c1-9c67-c5b1415fefb7","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-05-30T13:52:18.573Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"4acf8327-d10c-49c1-9c67-c5b1415fefb7","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-05-30T16:14:32.987Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T10:44:35.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T16:18:38.643Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T10:48:41.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T16:23:22.267Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"246d48da-7e31-4dc7-b1b9-707a3a70e735","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-05-30T16:23:22.278Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"246d48da-7e31-4dc7-b1b9-707a3a70e735","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-05-30T16:23:22.713Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"246d48da-7e31-4dc7-b1b9-707a3a70e735","userId":131,"email":"<EMAIL>"}
{"timestamp":"2025-05-30T16:47:16.568Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T11:17:19.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T16:47:43.356Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"71b3de90-d825-49a8-98aa-8a9c2ff4ec30","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:47:43.367Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b7d379c4-f1ad-48a7-a202-bdeedeb1a0e0","controller":"business","action":"businessList","userId":"131"}
{"timestamp":"2025-05-30T16:47:43.369Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"b7d379c4-f1ad-48a7-a202-bdeedeb1a0e0","userId":"131"}
{"timestamp":"2025-05-30T16:47:43.749Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"b7d379c4-f1ad-48a7-a202-bdeedeb1a0e0","userId":"131","businessCount":1}
{"timestamp":"2025-05-30T16:47:45.362Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f7f6c346-1b62-46f0-9065-aa65cfc9098a","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:47:45.370Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c61360c1-f127-4991-ba34-6f2d9b8fcda3","controller":"business","action":"businessList","userId":"131"}
{"timestamp":"2025-05-30T16:47:45.373Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"c61360c1-f127-4991-ba34-6f2d9b8fcda3","userId":"131"}
{"timestamp":"2025-05-30T16:47:45.431Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"c61360c1-f127-4991-ba34-6f2d9b8fcda3","userId":"131","businessCount":1}
{"timestamp":"2025-05-30T16:47:46.659Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9688dde7-1d5d-4047-85c6-8e118cbdf783","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:47:46.665Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4c97342f-a246-453f-9377-f590f00276e3","controller":"business","action":"businessList","userId":"131"}
{"timestamp":"2025-05-30T16:47:46.668Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"4c97342f-a246-453f-9377-f590f00276e3","userId":"131"}
{"timestamp":"2025-05-30T16:47:46.728Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"4c97342f-a246-453f-9377-f590f00276e3","userId":"131","businessCount":1}
{"timestamp":"2025-05-30T16:47:48.240Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"83c75189-ef50-4c9d-8a9b-e947c08f09cd","controller":"business","action":"businessList","userId":"131"}
{"timestamp":"2025-05-30T16:47:48.243Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"83c75189-ef50-4c9d-8a9b-e947c08f09cd","userId":"131"}
{"timestamp":"2025-05-30T16:47:48.253Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9c2a267f-d28e-41c1-b260-cbefdcdddde7","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:47:48.296Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"83c75189-ef50-4c9d-8a9b-e947c08f09cd","userId":"131","businessCount":1}
{"timestamp":"2025-05-30T16:47:52.472Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"620369fe-efba-4813-98ed-ffb7a34f9c12","controller":"business","action":"businessList","userId":"131"}
{"timestamp":"2025-05-30T16:47:52.477Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"620369fe-efba-4813-98ed-ffb7a34f9c12","userId":"131"}
{"timestamp":"2025-05-30T16:47:52.484Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"03f843cc-3fbe-4a99-82c3-78c51c7ceac1","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:47:52.528Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"620369fe-efba-4813-98ed-ffb7a34f9c12","userId":"131","businessCount":1}
{"timestamp":"2025-05-30T16:48:36.333Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9cec14ec-6701-449a-a084-5350afaa69ab","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-05-30T16:48:36.336Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"9cec14ec-6701-449a-a084-5350afaa69ab","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-05-30T16:48:36.478Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"9cec14ec-6701-449a-a084-5350afaa69ab","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-05-30T16:49:23.364Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dc1f20a0-4f78-4f90-875a-0e8cc04da6c4","controller":"business","action":"businessList","userId":"52"}
{"timestamp":"2025-05-30T16:49:23.367Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"dc1f20a0-4f78-4f90-875a-0e8cc04da6c4","userId":"52"}
{"timestamp":"2025-05-30T16:49:23.377Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c30fb6a5-2e92-4685-ac99-a5a60295c2f2","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-05-30T16:49:23.420Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"dc1f20a0-4f78-4f90-875a-0e8cc04da6c4","userId":"52","businessCount":5}
{"timestamp":"2025-05-30T17:39:04.566Z","level":"INFO","message":"Starting Geo Grid database initialization...","environment":"development"}
{"timestamp":"2025-05-30T17:39:04.569Z","level":"INFO","message":"Found 3 SQL statements to execute","environment":"development"}
{"timestamp":"2025-05-30T17:39:04.569Z","level":"INFO","message":"Executing statement 1/3","environment":"development"}
{"timestamp":"2025-05-30T17:39:04.600Z","level":"ERROR","message":"Error executing statement 1:","environment":"development","error":"","statement":"-- Create geo_grid_configurations table\nCREATE TABLE IF NOT EXISTS geo_grid_configurations (\n    id ...","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at initializeGeoGridTables (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\scripts\\init-geo-grid-db.js:41:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\scripts\\init-geo-grid-db.js:158:22"}
{"timestamp":"2025-05-30T17:39:04.600Z","level":"ERROR","message":"Failed to initialize Geo Grid database:","environment":"development","error":"","stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-05-30T17:39:21.670Z","level":"INFO","message":"Starting Geo Grid database initialization...","environment":"development"}
{"timestamp":"2025-05-30T17:39:21.672Z","level":"INFO","message":"Found 3 SQL statements to execute","environment":"development"}
{"timestamp":"2025-05-30T17:39:21.672Z","level":"INFO","message":"Executing statement 1/3","environment":"development"}
{"timestamp":"2025-05-30T17:39:21.700Z","level":"ERROR","message":"Error executing statement 1:","environment":"development","error":"","statement":"-- Create geo_grid_configurations table\nCREATE TABLE IF NOT EXISTS geo_grid_configurations (\n    id ...","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:93:72)\n    at initializeGeoGridTables (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\scripts\\init-geo-grid-db.js:41:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\scripts\\init-geo-grid-db.js:158:22"}
{"timestamp":"2025-05-30T17:39:21.700Z","level":"ERROR","message":"Failed to initialize Geo Grid database:","environment":"development","error":"","stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-05-30T17:55:25.009Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:25:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T17:55:31.989Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"6c963910-1d02-473e-9223-05a9934b50ca","userId":"52"}
{"timestamp":"2025-05-30T17:55:52.604Z","level":"INFO","message":"Searching location","environment":"DEVELOPMENT","requestId":"cc766c33-05cf-4575-82fe-0fe7a45faeac","searchType":"name","query":"Bachupally"}
{"timestamp":"2025-05-30T17:55:52.670Z","level":"INFO","message":"Generating grid","environment":"DEVELOPMENT","requestId":"4d5f60fb-401b-4700-aae8-4e8e5e63b7df","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","distance":500,"distanceUnit":"meters"}
{"timestamp":"2025-05-30T17:59:41.424Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"998f33f8-801a-48a8-9fb5-510746db1b73","userId":"52"}
{"timestamp":"2025-05-30T18:01:14.820Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"127464ad-8450-4a37-b06c-aec8f4234f28","userId":"52"}
{"timestamp":"2025-05-30T18:02:14.794Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"6063bd82-9663-4cfc-842f-7df3cf4f95f9","userId":"52"}
{"timestamp":"2025-05-30T18:05:04.448Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"0b1b37f8-9c99-4048-88fd-720bd838cd35","userId":"52"}
{"timestamp":"2025-05-30T18:05:10.369Z","level":"INFO","message":"Searching location","environment":"DEVELOPMENT","requestId":"a932c5e6-deab-49a7-b108-8e5e0c346597","searchType":"coordinates","query":"coordinates provided"}
{"timestamp":"2025-05-30T18:05:10.440Z","level":"INFO","message":"Generating grid","environment":"DEVELOPMENT","requestId":"b5bde46c-2587-4594-9a06-835c9d3beddf","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","distance":500,"distanceUnit":"meters"}
{"timestamp":"2025-05-30T18:11:24.058Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"ee09e77d-95d7-4990-ba51-a60f2329728b","userId":"52"}
{"timestamp":"2025-05-30T18:11:30.840Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"816aa69d-aa50-4124-be3c-95a553b72e2b","userId":"52"}
{"timestamp":"2025-05-30T18:14:30.218Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"07349732-d769-4c78-a964-127c9bf764fe","userId":"52"}
{"timestamp":"2025-05-30T18:15:49.727Z","level":"INFO","message":"Searching location","environment":"DEVELOPMENT","requestId":"dba32403-26e1-427c-8296-ce030626c726","searchType":"coordinates","query":"coordinates provided"}
{"timestamp":"2025-05-30T18:15:49.787Z","level":"INFO","message":"Generating grid","environment":"DEVELOPMENT","requestId":"631536b0-8030-4bc0-af0b-80a845eef272","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","distance":500,"distanceUnit":"meters"}
{"timestamp":"2025-05-30T18:18:32.163Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"9473808a-b145-432d-96d6-ac1b8cf961b5","userId":"52"}
{"timestamp":"2025-05-30T18:18:56.122Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"bc6553c0-623c-482d-aa47-ed2497aa79d2","userId":"52"}
{"timestamp":"2025-05-30T18:19:20.973Z","level":"INFO","message":"Searching location","environment":"DEVELOPMENT","requestId":"163091af-6537-4496-93eb-6646270b27a9","searchType":"coordinates","query":"coordinates provided"}
{"timestamp":"2025-05-30T18:19:21.026Z","level":"INFO","message":"Generating grid","environment":"DEVELOPMENT","requestId":"9cb16c57-3da4-4bbc-865f-2454eb0aba6b","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","distance":500,"distanceUnit":"meters"}
{"timestamp":"2025-05-30T18:19:35.759Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"14e0ebb4-5f93-4479-b848-46e6bec3b489","configName":"My Grid Configuration"}
{"timestamp":"2025-05-30T18:19:35.799Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","code":"ER_BAD_NULL_ERROR","errno":1048,"sqlState":"23000","sqlMessage":"Column 'user_id' cannot be null","sql":"\n        INSERT INTO geo_grid_configurations \n        (user_id, name, center_lat, center_lng, grid_size, distance, distance_unit, \n         search_type, search_query, is_schedule_enabled, settings, created_at, updated_at)\n        VALUES (NULL, 'My Grid Configuration', 40.7128, -74.006, '3x3', 500, 'meters', 'coordinates', '', false, '{}', NOW(), NOW())\n      ","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)\n    at Readable.push (node:internal/streams/readable:286:10)"}
{"timestamp":"2025-05-30T18:19:35.802Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)\n    at Readable.push (node:internal/streams/readable:286:10)","requestId":"14e0ebb4-5f93-4479-b848-46e6bec3b489"}
{"timestamp":"2025-05-30T18:19:40.784Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"feaecd57-401b-4f8c-acc2-3ac43c117d40","configName":"My Grid Configuration"}
{"timestamp":"2025-05-30T18:19:40.820Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","code":"ER_BAD_NULL_ERROR","errno":1048,"sqlState":"23000","sqlMessage":"Column 'user_id' cannot be null","sql":"\n        INSERT INTO geo_grid_configurations \n        (user_id, name, center_lat, center_lng, grid_size, distance, distance_unit, \n         search_type, search_query, is_schedule_enabled, settings, created_at, updated_at)\n        VALUES (NULL, 'My Grid Configuration', 40.7128, -74.006, '3x3', 500, 'meters', 'coordinates', '', false, '{}', NOW(), NOW())\n      ","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)\n    at Readable.push (node:internal/streams/readable:286:10)"}
{"timestamp":"2025-05-30T18:19:40.823Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","error":"Column 'user_id' cannot be null","stack":"Error: Column 'user_id' cannot be null\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:728:17)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:481:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:97:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\connection.js:104:25)\n    at Socket.emit (node:events:514:28)\n    at addChunk (node:internal/streams/readable:376:12)\n    at readableAddChunk (node:internal/streams/readable:349:9)\n    at Readable.push (node:internal/streams/readable:286:10)","requestId":"feaecd57-401b-4f8c-acc2-3ac43c117d40"}
{"timestamp":"2025-05-30T18:20:32.626Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:50:32.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:20:51.178Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:50:51.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:21:03.156Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:51:03.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:21:12.524Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:51:12.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:22:03.345Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"4536f093-edfd-46a9-875d-8594ca1aaae7","userId":"52","configName":"My Grid Configuration"}
{"timestamp":"2025-05-30T18:22:03.513Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"aafb2709-e976-48f1-bb42-a51d80b2f251","userId":"52"}
{"timestamp":"2025-05-30T18:22:03.553Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)"}
{"timestamp":"2025-05-30T18:22:03.555Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)","requestId":"aafb2709-e976-48f1-bb42-a51d80b2f251"}
{"timestamp":"2025-05-30T18:22:28.084Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"9cf74f7e-30a0-4498-bc33-a96384af51fb","userId":"52","configName":"My Grid Configuration"}
{"timestamp":"2025-05-30T18:22:28.227Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"0df83a35-0ce4-4d63-8cc7-1da688a53ad0","userId":"52"}
{"timestamp":"2025-05-30T18:22:28.282Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)"}
{"timestamp":"2025-05-30T18:22:28.284Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)","requestId":"0df83a35-0ce4-4d63-8cc7-1da688a53ad0"}
{"timestamp":"2025-05-30T18:22:40.276Z","level":"INFO","message":"Fetching grid configurations","environment":"DEVELOPMENT","requestId":"0c3f03b2-3a21-4ccc-a170-6d04a04e9630","userId":"52"}
{"timestamp":"2025-05-30T18:22:40.312Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)"}
{"timestamp":"2025-05-30T18:22:40.315Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:68:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:233:28)","requestId":"0c3f03b2-3a21-4ccc-a170-6d04a04e9630"}
{"timestamp":"2025-05-30T18:24:28.240Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:54:28.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:26:23.122Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:56:23.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:26:40.537Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-05-30T12:56:41.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-05-30T18:27:16.131Z","level":"INFO","message":"Searching location","environment":"DEVELOPMENT","requestId":"86b4c824-0d48-40a5-8acb-98dbbe671d1f","searchType":"coordinates","query":"coordinates provided"}
{"timestamp":"2025-05-30T18:27:16.172Z","level":"INFO","message":"Generating grid","environment":"DEVELOPMENT","requestId":"522c613c-a8c9-4ac5-bfc7-5a208dd1499e","centerLat":40.7128,"centerLng":-74.006,"gridSize":"3x3","distance":500,"distanceUnit":"meters"}
{"timestamp":"2025-05-30T18:27:23.417Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"a4123208-93b2-44fe-a5f1-27e1e3de6fac","userId":"52","configName":"Test COnfiguration"}
{"timestamp":"2025-05-30T18:27:23.459Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)"}
{"timestamp":"2025-05-30T18:27:23.462Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)","requestId":"a4123208-93b2-44fe-a5f1-27e1e3de6fac"}
{"timestamp":"2025-05-30T18:27:25.962Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"9ea0c2e1-045e-4bf9-9bc9-72646af0dd6c","userId":"52","configName":"Test COnfiguration"}
{"timestamp":"2025-05-30T18:27:25.998Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)"}
{"timestamp":"2025-05-30T18:27:26.000Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)","requestId":"9ea0c2e1-045e-4bf9-9bc9-72646af0dd6c"}
{"timestamp":"2025-05-30T18:27:30.058Z","level":"INFO","message":"Saving grid configuration","environment":"DEVELOPMENT","requestId":"ccf889e5-0dd3-4574-b05e-9808e414f4ba","userId":"52","configName":"Test COnfiguration"}
{"timestamp":"2025-05-30T18:27:30.085Z","level":"ERROR","message":"Error fetching grid configurations:","environment":"DEVELOPMENT","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)"}
{"timestamp":"2025-05-30T18:27:30.087Z","level":"ERROR","message":"Error saving grid configuration:","environment":"DEVELOPMENT","error":"\"[object Object]\" is not valid JSON","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:78:42\n    at Array.map (<anonymous>)\n    at GeoGrid.getGridConfigurations (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\geoGrid.models.js:66:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async saveGridConfiguration (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\geoGrid.controller.js:197:29)","requestId":"ccf889e5-0dd3-4574-b05e-9808e414f4ba"}
