{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Gets only the valid children of a component,\n * and ignores any nullish or falsy child.\n *\n * @param children the children\n */\nexport default function getValidReactChildren(children) {\n  return React.Children.toArray(children).filter(child => /*#__PURE__*/React.isValidElement(child));\n}", "map": {"version": 3, "names": ["React", "getValidReactChildren", "children", "Children", "toArray", "filter", "child", "isValidElement"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/node_modules/@mui/utils/esm/getValidReactChildren/getValidReactChildren.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Gets only the valid children of a component,\n * and ignores any nullish or falsy child.\n *\n * @param children the children\n */\nexport default function getValidReactChildren(children) {\n  return React.Children.toArray(children).filter(child => /*#__PURE__*/React.isValidElement(child));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,QAAQ,EAAE;EACtD,OAAOF,KAAK,CAACG,QAAQ,CAACC,OAAO,CAACF,QAAQ,CAAC,CAACG,MAAM,CAACC,KAAK,IAAI,aAAaN,KAAK,CAACO,cAAc,CAACD,KAAK,CAAC,CAAC;AACnG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}