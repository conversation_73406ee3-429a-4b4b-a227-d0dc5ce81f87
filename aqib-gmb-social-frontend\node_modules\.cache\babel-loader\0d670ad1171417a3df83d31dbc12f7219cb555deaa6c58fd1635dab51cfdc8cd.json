{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\geoGrid\\\\GeoGridControls.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { Box, Typography, TextField, Button, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio, Divider, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Chip, InputAdornment } from \"@mui/material\";\nimport { Search as SearchIcon, LocationOn as LocationOnIcon, Delete as DeleteIcon, Download as DownloadIcon, Save as SaveIcon, Refresh as RefreshIcon } from \"@mui/icons-material\";\nimport { LoadingButton } from \"@mui/lab\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GeoGridControls = ({\n  onLocationSearch,\n  onGenerateGrid,\n  onSaveConfiguration,\n  loading,\n  currentLocation,\n  savedConfigurations,\n  onLoadConfiguration,\n  onDeleteConfiguration\n}) => {\n  _s();\n  const [searchType, setSearchType] = useState(\"name\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [coordinates, setCoordinates] = useState({\n    lat: \"\",\n    lng: \"\"\n  });\n  const [configName, setConfigName] = useState(\"\");\n\n  // Location suggestions state\n  const [locationSuggestions, setLocationSuggestions] = useState([]);\n  const [loadingSuggestions, setLoadingSuggestions] = useState(false);\n  const [selectedLocation, setSelectedLocation] = useState(null);\n  const searchTimeoutRef = useRef(null);\n\n  // Location suggestions functionality\n  const fetchLocationSuggestions = async query => {\n    if (!query || query.length < 2) {\n      setLocationSuggestions([]);\n      return;\n    }\n    setLoadingSuggestions(true);\n    try {\n      // Try Google Places API first (if available)\n      if (window.google && window.google.maps && window.google.maps.places) {\n        const service = new window.google.maps.places.AutocompleteService();\n        service.getPlacePredictions({\n          input: query,\n          types: [\"(cities)\"],\n          componentRestrictions: {\n            country: \"us\"\n          } // You can modify this or make it configurable\n        }, (predictions, status) => {\n          if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {\n            const suggestions = predictions.map(prediction => ({\n              id: prediction.place_id,\n              name: prediction.description,\n              placeId: prediction.place_id,\n              types: prediction.types\n            }));\n            setLocationSuggestions(suggestions);\n          } else {\n            // Fallback to static suggestions\n            setLocationSuggestions(getStaticSuggestions(query));\n          }\n          setLoadingSuggestions(false);\n        });\n      } else {\n        // Fallback to static suggestions if Google Places API is not available\n        setLocationSuggestions(getStaticSuggestions(query));\n        setLoadingSuggestions(false);\n      }\n    } catch (error) {\n      console.error(\"Error fetching location suggestions:\", error);\n      setLocationSuggestions(getStaticSuggestions(query));\n      setLoadingSuggestions(false);\n    }\n  };\n\n  // Static fallback suggestions\n  const getStaticSuggestions = query => {\n    const staticLocations = [{\n      id: \"1\",\n      name: \"New York, NY, USA\",\n      lat: 40.7128,\n      lng: -74.006\n    }, {\n      id: \"2\",\n      name: \"Los Angeles, CA, USA\",\n      lat: 34.0522,\n      lng: -118.2437\n    }, {\n      id: \"3\",\n      name: \"Chicago, IL, USA\",\n      lat: 41.8781,\n      lng: -87.6298\n    }, {\n      id: \"4\",\n      name: \"Houston, TX, USA\",\n      lat: 29.7604,\n      lng: -95.3698\n    }, {\n      id: \"5\",\n      name: \"Phoenix, AZ, USA\",\n      lat: 33.4484,\n      lng: -112.074\n    }, {\n      id: \"6\",\n      name: \"Philadelphia, PA, USA\",\n      lat: 39.9526,\n      lng: -75.1652\n    }, {\n      id: \"7\",\n      name: \"San Antonio, TX, USA\",\n      lat: 29.4241,\n      lng: -98.4936\n    }, {\n      id: \"8\",\n      name: \"San Diego, CA, USA\",\n      lat: 32.7157,\n      lng: -117.1611\n    }, {\n      id: \"9\",\n      name: \"Dallas, TX, USA\",\n      lat: 32.7767,\n      lng: -96.797\n    }, {\n      id: \"10\",\n      name: \"San Jose, CA, USA\",\n      lat: 37.3382,\n      lng: -121.8863\n    }, {\n      id: \"11\",\n      name: \"Austin, TX, USA\",\n      lat: 30.2672,\n      lng: -97.7431\n    }, {\n      id: \"12\",\n      name: \"Jacksonville, FL, USA\",\n      lat: 30.3322,\n      lng: -81.6557\n    }, {\n      id: \"13\",\n      name: \"Fort Worth, TX, USA\",\n      lat: 32.7555,\n      lng: -97.3308\n    }, {\n      id: \"14\",\n      name: \"Columbus, OH, USA\",\n      lat: 39.9612,\n      lng: -82.9988\n    }, {\n      id: \"15\",\n      name: \"Charlotte, NC, USA\",\n      lat: 35.2271,\n      lng: -80.8431\n    }, {\n      id: \"16\",\n      name: \"San Francisco, CA, USA\",\n      lat: 37.7749,\n      lng: -122.4194\n    }, {\n      id: \"17\",\n      name: \"Indianapolis, IN, USA\",\n      lat: 39.7684,\n      lng: -86.1581\n    }, {\n      id: \"18\",\n      name: \"Seattle, WA, USA\",\n      lat: 47.6062,\n      lng: -122.3321\n    }, {\n      id: \"19\",\n      name: \"Denver, CO, USA\",\n      lat: 39.7392,\n      lng: -104.9903\n    }, {\n      id: \"20\",\n      name: \"Boston, MA, USA\",\n      lat: 42.3601,\n      lng: -71.0589\n    }];\n    return staticLocations.filter(location => location.name.toLowerCase().includes(query.toLowerCase())).slice(0, 5);\n  };\n\n  // Handle input change with debouncing\n  const handleLocationInputChange = value => {\n    setSearchQuery(value);\n\n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // Set new timeout for debouncing\n    searchTimeoutRef.current = setTimeout(() => {\n      fetchLocationSuggestions(value);\n    }, 300);\n  };\n\n  // Handle location selection from autocomplete\n  const handleLocationSelect = location => {\n    if (location) {\n      setSelectedLocation(location);\n      setSearchQuery(location.name);\n\n      // If it's a static suggestion with coordinates, use them directly\n      if (location.lat && location.lng) {\n        const searchRequest = {\n          searchType: \"coordinates\",\n          coordinates: {\n            lat: location.lat,\n            lng: location.lng\n          }\n        };\n        onLocationSearch(searchRequest);\n      } else {\n        // For Google Places API results, search by name\n        const searchRequest = {\n          searchType: \"name\",\n          query: location.name\n        };\n        onLocationSearch(searchRequest);\n      }\n    }\n  };\n  const handleSearch = () => {\n    if (searchType === \"name\" && !searchQuery.trim()) {\n      return;\n    }\n    if (searchType === \"coordinates\" && (!coordinates.lat || !coordinates.lng)) {\n      return;\n    }\n    if (searchType === \"mapUrl\" && !searchQuery.trim()) {\n      return;\n    }\n    const searchRequest = {\n      searchType,\n      query: searchQuery,\n      coordinates: searchType === \"coordinates\" ? {\n        lat: parseFloat(coordinates.lat),\n        lng: parseFloat(coordinates.lng)\n      } : undefined\n    };\n    onLocationSearch(searchRequest);\n  };\n  const handleKeyPress = event => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n  const renderSearchInput = () => {\n    switch (searchType) {\n      case \"name\":\n        return /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Search Location\",\n          placeholder: \"e.g., New York, NY\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          onKeyPress: handleKeyPress,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this);\n      case \"coordinates\":\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Latitude\",\n            placeholder: \"40.7128\",\n            value: coordinates.lat,\n            onChange: e => setCoordinates(prev => ({\n              ...prev,\n              lat: e.target.value\n            })),\n            type: \"number\",\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Longitude\",\n            placeholder: \"-74.0060\",\n            value: coordinates.lng,\n            onChange: e => setCoordinates(prev => ({\n              ...prev,\n              lng: e.target.value\n            })),\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this);\n      case \"mapUrl\":\n        return /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Google Maps URL\",\n          placeholder: \"https://maps.google.com/...\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          onKeyPress: handleKeyPress,\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Search Location\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      component: \"fieldset\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n        component: \"legend\",\n        children: \"Search Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        row: true,\n        value: searchType,\n        onChange: e => setSearchType(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"name\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 51\n          }, this),\n          label: \"By Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"coordinates\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 22\n          }, this),\n          label: \"Coordinates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"mapUrl\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 22\n          }, this),\n          label: \"Map URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), renderSearchInput(), /*#__PURE__*/_jsxDEV(LoadingButton, {\n      fullWidth: true,\n      variant: \"contained\",\n      onClick: handleSearch,\n      loading: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 20\n      }, this),\n      sx: {\n        mb: 3\n      },\n      children: \"Search & Generate Grid\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), currentLocation && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(LocationOnIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 19\n        }, this),\n        label: `${currentLocation.name} (${currentLocation.lat.toFixed(4)}, ${currentLocation.lng.toFixed(4)})`,\n        color: \"primary\",\n        variant: \"outlined\",\n        sx: {\n          mb: 2,\n          maxWidth: \"100%\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          gap: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          onClick: onGenerateGrid,\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Regenerate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Save Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n      fullWidth: true,\n      label: \"Configuration Name\",\n      placeholder: \"My Grid Configuration\",\n      value: configName,\n      onChange: e => setConfigName(e.target.value),\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n      fullWidth: true,\n      variant: \"contained\",\n      onClick: onSaveConfiguration,\n      loading: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 20\n      }, this),\n      disabled: !configName.trim() || !currentLocation,\n      sx: {\n        mb: 3\n      },\n      children: \"Save Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Saved Configurations\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), savedConfigurations.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      children: \"No saved configurations yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: savedConfigurations.map(config => /*#__PURE__*/_jsxDEV(ListItem, {\n        divider: true,\n        children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: config.name,\n          secondary: `${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            onClick: () => onLoadConfiguration(config),\n            disabled: loading,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            onClick: () => config.id && onDeleteConfiguration(config.id),\n            disabled: loading,\n            size: \"small\",\n            color: \"error\",\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this)]\n      }, config.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n_s(GeoGridControls, \"/Fih55C4QJiLJXS2O7PYiqDTMfE=\");\n_c = GeoGridControls;\nexport default GeoGridControls;\nvar _c;\n$RefreshReg$(_c, \"GeoGridControls\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Box", "Typography", "TextField", "<PERSON><PERSON>", "FormControl", "FormLabel", "RadioGroup", "FormControlLabel", "Radio", "Divider", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Chip", "InputAdornment", "Search", "SearchIcon", "LocationOn", "LocationOnIcon", "Delete", "DeleteIcon", "Download", "DownloadIcon", "Save", "SaveIcon", "Refresh", "RefreshIcon", "LoadingButton", "jsxDEV", "_jsxDEV", "GeoGridControls", "onLocationSearch", "onGenerateGrid", "onSaveConfiguration", "loading", "currentLocation", "savedConfigurations", "onLoadConfiguration", "onDeleteConfiguration", "_s", "searchType", "setSearchType", "searchQuery", "setSearch<PERSON>uery", "coordinates", "setCoordinates", "lat", "lng", "config<PERSON><PERSON>", "setConfigName", "locationSuggestions", "setLocationSuggestions", "loadingSuggestions", "setLoadingSuggestions", "selectedLocation", "setSelectedLocation", "searchTimeoutRef", "fetchLocationSuggestions", "query", "length", "window", "google", "maps", "places", "service", "AutocompleteService", "getPlacePredictions", "input", "types", "componentRestrictions", "country", "predictions", "status", "PlacesServiceStatus", "OK", "suggestions", "map", "prediction", "id", "place_id", "name", "description", "placeId", "getStaticSuggestions", "error", "console", "staticLocations", "filter", "location", "toLowerCase", "includes", "slice", "handleLocationInputChange", "value", "current", "clearTimeout", "setTimeout", "handleLocationSelect", "searchRequest", "handleSearch", "trim", "parseFloat", "undefined", "handleKeyPress", "event", "key", "renderSearchInput", "fullWidth", "label", "placeholder", "onChange", "e", "target", "onKeyPress", "InputProps", "startAdornment", "position", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "prev", "type", "variant", "gutterBottom", "component", "row", "control", "onClick", "startIcon", "icon", "toFixed", "color", "max<PERSON><PERSON><PERSON>", "display", "gap", "size", "disabled", "my", "dense", "config", "divider", "primary", "secondary", "gridSize", "distance", "distanceUnit", "edge", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/geoGrid/GeoGridControls.component.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport {\n  <PERSON>,\n  Typography,\n  TextField,\n  Button,\n  FormControl,\n  FormLabel,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Chip,\n  InputAdornment,\n  Autocomplete,\n  CircularProgress,\n} from \"@mui/material\";\nimport {\n  Search as SearchIcon,\n  LocationOn as LocationOnIcon,\n  Delete as DeleteIcon,\n  Download as DownloadIcon,\n  Save as SaveIcon,\n  Refresh as RefreshIcon,\n} from \"@mui/icons-material\";\nimport { LoadingButton } from \"@mui/lab\";\nimport {\n  LocationSearchRequest,\n  GridConfiguration,\n} from \"../../services/geoGrid/geoGrid.service\";\n\ninterface GeoGridControlsProps {\n  onLocationSearch: (searchRequest: LocationSearchRequest) => void;\n  onGenerateGrid: () => void;\n  onSaveConfiguration: () => void;\n  loading: boolean;\n  currentLocation: any;\n  savedConfigurations: GridConfiguration[];\n  onLoadConfiguration: (config: GridConfiguration) => void;\n  onDeleteConfiguration: (configId: number) => void;\n}\n\nconst GeoGridControls: React.FC<GeoGridControlsProps> = ({\n  onLocationSearch,\n  onGenerateGrid,\n  onSaveConfiguration,\n  loading,\n  currentLocation,\n  savedConfigurations,\n  onLoadConfiguration,\n  onDeleteConfiguration,\n}) => {\n  const [searchType, setSearchType] = useState<\n    \"name\" | \"coordinates\" | \"mapUrl\"\n  >(\"name\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [coordinates, setCoordinates] = useState({ lat: \"\", lng: \"\" });\n  const [configName, setConfigName] = useState(\"\");\n\n  // Location suggestions state\n  const [locationSuggestions, setLocationSuggestions] = useState<any[]>([]);\n  const [loadingSuggestions, setLoadingSuggestions] = useState(false);\n  const [selectedLocation, setSelectedLocation] = useState<any>(null);\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Location suggestions functionality\n  const fetchLocationSuggestions = async (query: string) => {\n    if (!query || query.length < 2) {\n      setLocationSuggestions([]);\n      return;\n    }\n\n    setLoadingSuggestions(true);\n\n    try {\n      // Try Google Places API first (if available)\n      if (window.google && window.google.maps && window.google.maps.places) {\n        const service = new window.google.maps.places.AutocompleteService();\n\n        service.getPlacePredictions(\n          {\n            input: query,\n            types: [\"(cities)\"],\n            componentRestrictions: { country: \"us\" }, // You can modify this or make it configurable\n          },\n          (predictions, status) => {\n            if (\n              status === window.google.maps.places.PlacesServiceStatus.OK &&\n              predictions\n            ) {\n              const suggestions = predictions.map((prediction: any) => ({\n                id: prediction.place_id,\n                name: prediction.description,\n                placeId: prediction.place_id,\n                types: prediction.types,\n              }));\n              setLocationSuggestions(suggestions);\n            } else {\n              // Fallback to static suggestions\n              setLocationSuggestions(getStaticSuggestions(query));\n            }\n            setLoadingSuggestions(false);\n          }\n        );\n      } else {\n        // Fallback to static suggestions if Google Places API is not available\n        setLocationSuggestions(getStaticSuggestions(query));\n        setLoadingSuggestions(false);\n      }\n    } catch (error) {\n      console.error(\"Error fetching location suggestions:\", error);\n      setLocationSuggestions(getStaticSuggestions(query));\n      setLoadingSuggestions(false);\n    }\n  };\n\n  // Static fallback suggestions\n  const getStaticSuggestions = (query: string) => {\n    const staticLocations = [\n      { id: \"1\", name: \"New York, NY, USA\", lat: 40.7128, lng: -74.006 },\n      { id: \"2\", name: \"Los Angeles, CA, USA\", lat: 34.0522, lng: -118.2437 },\n      { id: \"3\", name: \"Chicago, IL, USA\", lat: 41.8781, lng: -87.6298 },\n      { id: \"4\", name: \"Houston, TX, USA\", lat: 29.7604, lng: -95.3698 },\n      { id: \"5\", name: \"Phoenix, AZ, USA\", lat: 33.4484, lng: -112.074 },\n      { id: \"6\", name: \"Philadelphia, PA, USA\", lat: 39.9526, lng: -75.1652 },\n      { id: \"7\", name: \"San Antonio, TX, USA\", lat: 29.4241, lng: -98.4936 },\n      { id: \"8\", name: \"San Diego, CA, USA\", lat: 32.7157, lng: -117.1611 },\n      { id: \"9\", name: \"Dallas, TX, USA\", lat: 32.7767, lng: -96.797 },\n      { id: \"10\", name: \"San Jose, CA, USA\", lat: 37.3382, lng: -121.8863 },\n      { id: \"11\", name: \"Austin, TX, USA\", lat: 30.2672, lng: -97.7431 },\n      { id: \"12\", name: \"Jacksonville, FL, USA\", lat: 30.3322, lng: -81.6557 },\n      { id: \"13\", name: \"Fort Worth, TX, USA\", lat: 32.7555, lng: -97.3308 },\n      { id: \"14\", name: \"Columbus, OH, USA\", lat: 39.9612, lng: -82.9988 },\n      { id: \"15\", name: \"Charlotte, NC, USA\", lat: 35.2271, lng: -80.8431 },\n      {\n        id: \"16\",\n        name: \"San Francisco, CA, USA\",\n        lat: 37.7749,\n        lng: -122.4194,\n      },\n      { id: \"17\", name: \"Indianapolis, IN, USA\", lat: 39.7684, lng: -86.1581 },\n      { id: \"18\", name: \"Seattle, WA, USA\", lat: 47.6062, lng: -122.3321 },\n      { id: \"19\", name: \"Denver, CO, USA\", lat: 39.7392, lng: -104.9903 },\n      { id: \"20\", name: \"Boston, MA, USA\", lat: 42.3601, lng: -71.0589 },\n    ];\n\n    return staticLocations\n      .filter((location) =>\n        location.name.toLowerCase().includes(query.toLowerCase())\n      )\n      .slice(0, 5);\n  };\n\n  // Handle input change with debouncing\n  const handleLocationInputChange = (value: string) => {\n    setSearchQuery(value);\n\n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // Set new timeout for debouncing\n    searchTimeoutRef.current = setTimeout(() => {\n      fetchLocationSuggestions(value);\n    }, 300);\n  };\n\n  // Handle location selection from autocomplete\n  const handleLocationSelect = (location: any) => {\n    if (location) {\n      setSelectedLocation(location);\n      setSearchQuery(location.name);\n\n      // If it's a static suggestion with coordinates, use them directly\n      if (location.lat && location.lng) {\n        const searchRequest: LocationSearchRequest = {\n          searchType: \"coordinates\",\n          coordinates: {\n            lat: location.lat,\n            lng: location.lng,\n          },\n        };\n        onLocationSearch(searchRequest);\n      } else {\n        // For Google Places API results, search by name\n        const searchRequest: LocationSearchRequest = {\n          searchType: \"name\",\n          query: location.name,\n        };\n        onLocationSearch(searchRequest);\n      }\n    }\n  };\n\n  const handleSearch = () => {\n    if (searchType === \"name\" && !searchQuery.trim()) {\n      return;\n    }\n\n    if (\n      searchType === \"coordinates\" &&\n      (!coordinates.lat || !coordinates.lng)\n    ) {\n      return;\n    }\n\n    if (searchType === \"mapUrl\" && !searchQuery.trim()) {\n      return;\n    }\n\n    const searchRequest: LocationSearchRequest = {\n      searchType,\n      query: searchQuery,\n      coordinates:\n        searchType === \"coordinates\"\n          ? {\n              lat: parseFloat(coordinates.lat),\n              lng: parseFloat(coordinates.lng),\n            }\n          : undefined,\n    };\n\n    onLocationSearch(searchRequest);\n  };\n\n  const handleKeyPress = (event: React.KeyboardEvent) => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n\n  const renderSearchInput = () => {\n    switch (searchType) {\n      case \"name\":\n        return (\n          <TextField\n            fullWidth\n            label=\"Search Location\"\n            placeholder=\"e.g., New York, NY\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={handleKeyPress}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n            }}\n            sx={{ mb: 2 }}\n          />\n        );\n\n      case \"coordinates\":\n        return (\n          <Box sx={{ mb: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Latitude\"\n              placeholder=\"40.7128\"\n              value={coordinates.lat}\n              onChange={(e) =>\n                setCoordinates((prev) => ({ ...prev, lat: e.target.value }))\n              }\n              type=\"number\"\n              sx={{ mb: 1 }}\n            />\n            <TextField\n              fullWidth\n              label=\"Longitude\"\n              placeholder=\"-74.0060\"\n              value={coordinates.lng}\n              onChange={(e) =>\n                setCoordinates((prev) => ({ ...prev, lng: e.target.value }))\n              }\n              type=\"number\"\n            />\n          </Box>\n        );\n\n      case \"mapUrl\":\n        return (\n          <TextField\n            fullWidth\n            label=\"Google Maps URL\"\n            placeholder=\"https://maps.google.com/...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={handleKeyPress}\n            sx={{ mb: 2 }}\n          />\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom>\n        Search Location\n      </Typography>\n\n      <FormControl component=\"fieldset\" sx={{ mb: 2 }}>\n        <FormLabel component=\"legend\">Search Method</FormLabel>\n        <RadioGroup\n          row\n          value={searchType}\n          onChange={(e) => setSearchType(e.target.value as any)}\n        >\n          <FormControlLabel value=\"name\" control={<Radio />} label=\"By Name\" />\n          <FormControlLabel\n            value=\"coordinates\"\n            control={<Radio />}\n            label=\"Coordinates\"\n          />\n          <FormControlLabel\n            value=\"mapUrl\"\n            control={<Radio />}\n            label=\"Map URL\"\n          />\n        </RadioGroup>\n      </FormControl>\n\n      {renderSearchInput()}\n\n      <LoadingButton\n        fullWidth\n        variant=\"contained\"\n        onClick={handleSearch}\n        loading={loading}\n        startIcon={<SearchIcon />}\n        sx={{ mb: 3 }}\n      >\n        Search & Generate Grid\n      </LoadingButton>\n\n      {currentLocation && (\n        <Box sx={{ mb: 3 }}>\n          <Chip\n            icon={<LocationOnIcon />}\n            label={`${currentLocation.name} (${currentLocation.lat.toFixed(\n              4\n            )}, ${currentLocation.lng.toFixed(4)})`}\n            color=\"primary\"\n            variant=\"outlined\"\n            sx={{ mb: 2, maxWidth: \"100%\" }}\n          />\n\n          <Box sx={{ display: \"flex\", gap: 1 }}>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={onGenerateGrid}\n              startIcon={<RefreshIcon />}\n              disabled={loading}\n            >\n              Regenerate\n            </Button>\n          </Box>\n        </Box>\n      )}\n\n      <Divider sx={{ my: 2 }} />\n\n      <Typography variant=\"h6\" gutterBottom>\n        Save Configuration\n      </Typography>\n\n      <TextField\n        fullWidth\n        label=\"Configuration Name\"\n        placeholder=\"My Grid Configuration\"\n        value={configName}\n        onChange={(e) => setConfigName(e.target.value)}\n        sx={{ mb: 2 }}\n      />\n\n      <LoadingButton\n        fullWidth\n        variant=\"contained\"\n        onClick={onSaveConfiguration}\n        loading={loading}\n        startIcon={<SaveIcon />}\n        disabled={!configName.trim() || !currentLocation}\n        sx={{ mb: 3 }}\n      >\n        Save Configuration\n      </LoadingButton>\n\n      <Divider sx={{ my: 2 }} />\n\n      <Typography variant=\"h6\" gutterBottom>\n        Saved Configurations\n      </Typography>\n\n      {savedConfigurations.length === 0 ? (\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          No saved configurations yet\n        </Typography>\n      ) : (\n        <List dense>\n          {savedConfigurations.map((config) => (\n            <ListItem key={config.id} divider>\n              <ListItemText\n                primary={config.name}\n                secondary={`${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`}\n              />\n              <ListItemSecondaryAction>\n                <IconButton\n                  edge=\"end\"\n                  onClick={() => onLoadConfiguration(config)}\n                  disabled={loading}\n                  size=\"small\"\n                >\n                  <DownloadIcon />\n                </IconButton>\n                <IconButton\n                  edge=\"end\"\n                  onClick={() => config.id && onDeleteConfiguration(config.id)}\n                  disabled={loading}\n                  size=\"small\"\n                  color=\"error\"\n                >\n                  <DeleteIcon />\n                </IconButton>\n              </ListItemSecondaryAction>\n            </ListItem>\n          ))}\n        </List>\n      )}\n    </Box>\n  );\n};\n\nexport default GeoGridControls;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAaC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,UAAU,EACVC,IAAI,EACJC,cAAc,QAGT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,aAAa,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBzC,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,gBAAgB;EAChBC,cAAc;EACdC,mBAAmB;EACnBC,OAAO;EACPC,eAAe;EACfC,mBAAmB;EACnBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAE1C,MAAM,CAAC;EACT,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC;IAAEkD,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC;EACpE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACsD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvD,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACwD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAM,IAAI,CAAC;EACnE,MAAM4D,gBAAgB,GAAG3D,MAAM,CAAwB,IAAI,CAAC;;EAE5D;EACA,MAAM4D,wBAAwB,GAAG,MAAOC,KAAa,IAAK;IACxD,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9BR,sBAAsB,CAAC,EAAE,CAAC;MAC1B;IACF;IAEAE,qBAAqB,CAAC,IAAI,CAAC;IAE3B,IAAI;MACF;MACA,IAAIO,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIF,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE;QACpE,MAAMC,OAAO,GAAG,IAAIJ,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,CAACE,mBAAmB,CAAC,CAAC;QAEnED,OAAO,CAACE,mBAAmB,CACzB;UACEC,KAAK,EAAET,KAAK;UACZU,KAAK,EAAE,CAAC,UAAU,CAAC;UACnBC,qBAAqB,EAAE;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAE;QAC5C,CAAC,EACD,CAACC,WAAW,EAAEC,MAAM,KAAK;UACvB,IACEA,MAAM,KAAKZ,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,CAACU,mBAAmB,CAACC,EAAE,IAC3DH,WAAW,EACX;YACA,MAAMI,WAAW,GAAGJ,WAAW,CAACK,GAAG,CAAEC,UAAe,KAAM;cACxDC,EAAE,EAAED,UAAU,CAACE,QAAQ;cACvBC,IAAI,EAAEH,UAAU,CAACI,WAAW;cAC5BC,OAAO,EAAEL,UAAU,CAACE,QAAQ;cAC5BX,KAAK,EAAES,UAAU,CAACT;YACpB,CAAC,CAAC,CAAC;YACHjB,sBAAsB,CAACwB,WAAW,CAAC;UACrC,CAAC,MAAM;YACL;YACAxB,sBAAsB,CAACgC,oBAAoB,CAACzB,KAAK,CAAC,CAAC;UACrD;UACAL,qBAAqB,CAAC,KAAK,CAAC;QAC9B,CACF,CAAC;MACH,CAAC,MAAM;QACL;QACAF,sBAAsB,CAACgC,oBAAoB,CAACzB,KAAK,CAAC,CAAC;QACnDL,qBAAqB,CAAC,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjC,sBAAsB,CAACgC,oBAAoB,CAACzB,KAAK,CAAC,CAAC;MACnDL,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM8B,oBAAoB,GAAIzB,KAAa,IAAK;IAC9C,MAAM4B,eAAe,GAAG,CACtB;MAAER,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,mBAAmB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAO,CAAC,EAClE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,sBAAsB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAS,CAAC,EACvE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,kBAAkB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EAClE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,kBAAkB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EAClE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,kBAAkB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EAClE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,uBAAuB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACvE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,sBAAsB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACtE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,oBAAoB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAS,CAAC,EACrE;MAAE+B,EAAE,EAAE,GAAG;MAAEE,IAAI,EAAE,iBAAiB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAO,CAAC,EAChE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,mBAAmB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAS,CAAC,EACrE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,iBAAiB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EAClE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,uBAAuB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACxE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,qBAAqB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACtE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,mBAAmB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACpE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,oBAAoB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACrE;MACE+B,EAAE,EAAE,IAAI;MACRE,IAAI,EAAE,wBAAwB;MAC9BlC,GAAG,EAAE,OAAO;MACZC,GAAG,EAAE,CAAC;IACR,CAAC,EACD;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,uBAAuB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,EACxE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,kBAAkB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAS,CAAC,EACpE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,iBAAiB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAS,CAAC,EACnE;MAAE+B,EAAE,EAAE,IAAI;MAAEE,IAAI,EAAE,iBAAiB;MAAElC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE,CAAC;IAAQ,CAAC,CACnE;IAED,OAAOuC,eAAe,CACnBC,MAAM,CAAEC,QAAQ,IACfA,QAAQ,CAACR,IAAI,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,KAAK,CAAC+B,WAAW,CAAC,CAAC,CAC1D,CAAC,CACAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAIC,KAAa,IAAK;IACnDlD,cAAc,CAACkD,KAAK,CAAC;;IAErB;IACA,IAAIrC,gBAAgB,CAACsC,OAAO,EAAE;MAC5BC,YAAY,CAACvC,gBAAgB,CAACsC,OAAO,CAAC;IACxC;;IAEA;IACAtC,gBAAgB,CAACsC,OAAO,GAAGE,UAAU,CAAC,MAAM;MAC1CvC,wBAAwB,CAACoC,KAAK,CAAC;IACjC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAIT,QAAa,IAAK;IAC9C,IAAIA,QAAQ,EAAE;MACZjC,mBAAmB,CAACiC,QAAQ,CAAC;MAC7B7C,cAAc,CAAC6C,QAAQ,CAACR,IAAI,CAAC;;MAE7B;MACA,IAAIQ,QAAQ,CAAC1C,GAAG,IAAI0C,QAAQ,CAACzC,GAAG,EAAE;QAChC,MAAMmD,aAAoC,GAAG;UAC3C1D,UAAU,EAAE,aAAa;UACzBI,WAAW,EAAE;YACXE,GAAG,EAAE0C,QAAQ,CAAC1C,GAAG;YACjBC,GAAG,EAAEyC,QAAQ,CAACzC;UAChB;QACF,CAAC;QACDhB,gBAAgB,CAACmE,aAAa,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMA,aAAoC,GAAG;UAC3C1D,UAAU,EAAE,MAAM;UAClBkB,KAAK,EAAE8B,QAAQ,CAACR;QAClB,CAAC;QACDjD,gBAAgB,CAACmE,aAAa,CAAC;MACjC;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI3D,UAAU,KAAK,MAAM,IAAI,CAACE,WAAW,CAAC0D,IAAI,CAAC,CAAC,EAAE;MAChD;IACF;IAEA,IACE5D,UAAU,KAAK,aAAa,KAC3B,CAACI,WAAW,CAACE,GAAG,IAAI,CAACF,WAAW,CAACG,GAAG,CAAC,EACtC;MACA;IACF;IAEA,IAAIP,UAAU,KAAK,QAAQ,IAAI,CAACE,WAAW,CAAC0D,IAAI,CAAC,CAAC,EAAE;MAClD;IACF;IAEA,MAAMF,aAAoC,GAAG;MAC3C1D,UAAU;MACVkB,KAAK,EAAEhB,WAAW;MAClBE,WAAW,EACTJ,UAAU,KAAK,aAAa,GACxB;QACEM,GAAG,EAAEuD,UAAU,CAACzD,WAAW,CAACE,GAAG,CAAC;QAChCC,GAAG,EAAEsD,UAAU,CAACzD,WAAW,CAACG,GAAG;MACjC,CAAC,GACDuD;IACR,CAAC;IAEDvE,gBAAgB,CAACmE,aAAa,CAAC;EACjC,CAAC;EAED,MAAMK,cAAc,GAAIC,KAA0B,IAAK;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBN,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQlE,UAAU;MAChB,KAAK,MAAM;QACT,oBACEX,OAAA,CAAC7B,SAAS;UACR2G,SAAS;UACTC,KAAK,EAAC,iBAAiB;UACvBC,WAAW,EAAC,oBAAoB;UAChChB,KAAK,EAAEnD,WAAY;UACnBoE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;UAChDoB,UAAU,EAAEV,cAAe;UAC3BW,UAAU,EAAE;YACVC,cAAc,eACZtF,OAAA,CAACf,cAAc;cAACsG,QAAQ,EAAC,OAAO;cAAAC,QAAA,eAC9BxF,OAAA,CAACb,UAAU;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAGN,KAAK,aAAa;QAChB,oBACE5F,OAAA,CAAC/B,GAAG;UAAC4H,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACjBxF,OAAA,CAAC7B,SAAS;YACR2G,SAAS;YACTC,KAAK,EAAC,UAAU;YAChBC,WAAW,EAAC,SAAS;YACrBhB,KAAK,EAAEjD,WAAW,CAACE,GAAI;YACvBgE,QAAQ,EAAGC,CAAC,IACVlE,cAAc,CAAE+E,IAAI,KAAM;cAAE,GAAGA,IAAI;cAAE9E,GAAG,EAAEiE,CAAC,CAACC,MAAM,CAACnB;YAAM,CAAC,CAAC,CAC5D;YACDgC,IAAI,EAAC,QAAQ;YACbH,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF5F,OAAA,CAAC7B,SAAS;YACR2G,SAAS;YACTC,KAAK,EAAC,WAAW;YACjBC,WAAW,EAAC,UAAU;YACtBhB,KAAK,EAAEjD,WAAW,CAACG,GAAI;YACvB+D,QAAQ,EAAGC,CAAC,IACVlE,cAAc,CAAE+E,IAAI,KAAM;cAAE,GAAGA,IAAI;cAAE7E,GAAG,EAAEgE,CAAC,CAACC,MAAM,CAACnB;YAAM,CAAC,CAAC,CAC5D;YACDgC,IAAI,EAAC;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAGV,KAAK,QAAQ;QACX,oBACE5F,OAAA,CAAC7B,SAAS;UACR2G,SAAS;UACTC,KAAK,EAAC,iBAAiB;UACvBC,WAAW,EAAC,6BAA6B;UACzChB,KAAK,EAAEnD,WAAY;UACnBoE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;UAChDoB,UAAU,EAAEV,cAAe;UAC3BmB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAGN;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE5F,OAAA,CAAC/B,GAAG;IAAAuH,QAAA,gBACFxF,OAAA,CAAC9B,UAAU;MAAC+H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb5F,OAAA,CAAC3B,WAAW;MAAC8H,SAAS,EAAC,UAAU;MAACN,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAC9CxF,OAAA,CAAC1B,SAAS;QAAC6H,SAAS,EAAC,QAAQ;QAAAX,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACvD5F,OAAA,CAACzB,UAAU;QACT6H,GAAG;QACHpC,KAAK,EAAErD,UAAW;QAClBsE,QAAQ,EAAGC,CAAC,IAAKtE,aAAa,CAACsE,CAAC,CAACC,MAAM,CAACnB,KAAY,CAAE;QAAAwB,QAAA,gBAEtDxF,OAAA,CAACxB,gBAAgB;UAACwF,KAAK,EAAC,MAAM;UAACqC,OAAO,eAAErG,OAAA,CAACvB,KAAK;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrE5F,OAAA,CAACxB,gBAAgB;UACfwF,KAAK,EAAC,aAAa;UACnBqC,OAAO,eAAErG,OAAA,CAACvB,KAAK;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBb,KAAK,EAAC;QAAa;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACF5F,OAAA,CAACxB,gBAAgB;UACfwF,KAAK,EAAC,QAAQ;UACdqC,OAAO,eAAErG,OAAA,CAACvB,KAAK;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBb,KAAK,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEbf,iBAAiB,CAAC,CAAC,eAEpB7E,OAAA,CAACF,aAAa;MACZgF,SAAS;MACTmB,OAAO,EAAC,WAAW;MACnBK,OAAO,EAAEhC,YAAa;MACtBjE,OAAO,EAAEA,OAAQ;MACjBkG,SAAS,eAAEvG,OAAA,CAACb,UAAU;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,EACf;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,EAEftF,eAAe,iBACdN,OAAA,CAAC/B,GAAG;MAAC4H,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACjBxF,OAAA,CAAChB,IAAI;QACHwH,IAAI,eAAExG,OAAA,CAACX,cAAc;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBb,KAAK,EAAE,GAAGzE,eAAe,CAAC6C,IAAI,KAAK7C,eAAe,CAACW,GAAG,CAACwF,OAAO,CAC5D,CACF,CAAC,KAAKnG,eAAe,CAACY,GAAG,CAACuF,OAAO,CAAC,CAAC,CAAC,GAAI;QACxCC,KAAK,EAAC,SAAS;QACfT,OAAO,EAAC,UAAU;QAClBJ,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEa,QAAQ,EAAE;QAAO;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEF5F,OAAA,CAAC/B,GAAG;QAAC4H,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAArB,QAAA,eACnCxF,OAAA,CAAC5B,MAAM;UACL6H,OAAO,EAAC,UAAU;UAClBa,IAAI,EAAC,OAAO;UACZR,OAAO,EAAEnG,cAAe;UACxBoG,SAAS,eAAEvG,OAAA,CAACH,WAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BmB,QAAQ,EAAE1G,OAAQ;UAAAmF,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED5F,OAAA,CAACtB,OAAO;MAACmH,EAAE,EAAE;QAAEmB,EAAE,EAAE;MAAE;IAAE;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B5F,OAAA,CAAC9B,UAAU;MAAC+H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb5F,OAAA,CAAC7B,SAAS;MACR2G,SAAS;MACTC,KAAK,EAAC,oBAAoB;MAC1BC,WAAW,EAAC,uBAAuB;MACnChB,KAAK,EAAE7C,UAAW;MAClB8D,QAAQ,EAAGC,CAAC,IAAK9D,aAAa,CAAC8D,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;MAC/C6B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEF5F,OAAA,CAACF,aAAa;MACZgF,SAAS;MACTmB,OAAO,EAAC,WAAW;MACnBK,OAAO,EAAElG,mBAAoB;MAC7BC,OAAO,EAAEA,OAAQ;MACjBkG,SAAS,eAAEvG,OAAA,CAACL,QAAQ;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBmB,QAAQ,EAAE,CAAC5F,UAAU,CAACoD,IAAI,CAAC,CAAC,IAAI,CAACjE,eAAgB;MACjDuF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,EACf;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAEhB5F,OAAA,CAACtB,OAAO;MAACmH,EAAE,EAAE;QAAEmB,EAAE,EAAE;MAAE;IAAE;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B5F,OAAA,CAAC9B,UAAU;MAAC+H,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZrF,mBAAmB,CAACuB,MAAM,KAAK,CAAC,gBAC/B9B,OAAA,CAAC9B,UAAU;MAAC+H,OAAO,EAAC,OAAO;MAACS,KAAK,EAAC,gBAAgB;MAAAlB,QAAA,EAAC;IAEnD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEb5F,OAAA,CAACrB,IAAI;MAACsI,KAAK;MAAAzB,QAAA,EACRjF,mBAAmB,CAACwC,GAAG,CAAEmE,MAAM,iBAC9BlH,OAAA,CAACpB,QAAQ;QAAiBuI,OAAO;QAAA3B,QAAA,gBAC/BxF,OAAA,CAACnB,YAAY;UACXuI,OAAO,EAAEF,MAAM,CAAC/D,IAAK;UACrBkE,SAAS,EAAE,GAAGH,MAAM,CAACI,QAAQ,WAAWJ,MAAM,CAACK,QAAQ,IAAIL,MAAM,CAACM,YAAY;QAAG;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC,eACF5F,OAAA,CAAClB,uBAAuB;UAAA0G,QAAA,gBACtBxF,OAAA,CAACjB,UAAU;YACT0I,IAAI,EAAC,KAAK;YACVnB,OAAO,EAAEA,CAAA,KAAM9F,mBAAmB,CAAC0G,MAAM,CAAE;YAC3CH,QAAQ,EAAE1G,OAAQ;YAClByG,IAAI,EAAC,OAAO;YAAAtB,QAAA,eAEZxF,OAAA,CAACP,YAAY;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACb5F,OAAA,CAACjB,UAAU;YACT0I,IAAI,EAAC,KAAK;YACVnB,OAAO,EAAEA,CAAA,KAAMY,MAAM,CAACjE,EAAE,IAAIxC,qBAAqB,CAACyG,MAAM,CAACjE,EAAE,CAAE;YAC7D8D,QAAQ,EAAE1G,OAAQ;YAClByG,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAC,OAAO;YAAAlB,QAAA,eAEbxF,OAAA,CAACT,UAAU;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA,GAvBbsB,MAAM,CAACjE,EAAE;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBd,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClF,EAAA,CAzYIT,eAA+C;AAAAyH,EAAA,GAA/CzH,eAA+C;AA2YrD,eAAeA,eAAe;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}