# Location Suggestions Feature

## Overview

The location suggestions feature provides real-time autocomplete functionality when users type location names in the "Search by Name" field. It uses Google Places API for accurate, up-to-date location suggestions with fallback to static suggestions.

## Features

### ✅ **Implemented Features**

- **Real-time Autocomplete**: Shows suggestions as user types (with 300ms debouncing)
- **Google Places API Integration**: Uses Google's comprehensive location database
- **Fallback System**: Static suggestions for major US cities when API is unavailable
- **Smart Loading**: Dynamically loads Google Maps API only when needed
- **Loading States**: Shows loading spinner while fetching suggestions
- **Error Handling**: Graceful fallback when API fails or is unavailable
- **TypeScript Support**: Full type safety with custom type declarations

### 🎯 **User Experience**

- **Minimum 2 characters** required before showing suggestions
- **Debounced search** (300ms) to avoid excessive API calls
- **Visual indicators** with location icons and loading states
- **Keyboard navigation** support through Material-UI Autocomplete
- **Automatic selection** triggers grid generation immediately

## Setup Instructions

### 1. **Get Google Maps API Key**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:

   - **Places API** (for location suggestions)
   - **Maps JavaScript API** (for future map features)
   - **Geocoding API** (for coordinate conversion)

4. Create credentials (API Key)
5. Restrict the API key to your domain for security

### 2. **Configure API Key**

**Option A: Environment Variable (Recommended)**

```bash
# In aqib-gmb-social-frontend/.env.development
REACT_APP_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

**Option B: Direct Configuration**
Update the API key in `src/utils/googleMaps.utils.ts` if needed.

### 3. **Test the Feature**

1. Start the frontend: `npm start`
2. Navigate to `/geo-grid`
3. Select "By Name" search method
4. Type a city name (e.g., "New York")
5. See real-time suggestions appear

## Technical Implementation

### **Components Modified**

- `GeoGridControls.component.tsx` - Added autocomplete functionality
- `googleMaps.utils.ts` - Utility functions for Google Maps API
- `google-maps.d.ts` - TypeScript declarations

### **Key Functions**

#### **fetchLocationSuggestions()**

```typescript
// Fetches suggestions from Google Places API or fallback
const fetchLocationSuggestions = async (query: string) => {
  // Uses Google Places AutocompleteService
  // Falls back to static suggestions if API unavailable
};
```

#### **loadGoogleMapsAPI()**

```typescript
// Dynamically loads Google Maps API
export const loadGoogleMapsAPI = (): Promise<void> => {
  // Loads script with API key from environment
  // Handles loading states and errors
};
```

### **Fallback System**

When Google Places API is unavailable, the system uses static suggestions for 20 major US cities:

- New York, Los Angeles, Chicago, Houston, Phoenix
- Philadelphia, San Antonio, San Diego, Dallas, San Jose
- Austin, Jacksonville, Fort Worth, Columbus, Charlotte
- San Francisco, Indianapolis, Seattle, Denver, Boston

## API Usage & Costs

### **Google Places API Pricing**

- **Autocomplete (per session)**: $2.83 per 1000 sessions
- **Place Details**: $17 per 1000 requests
- **Free tier**: $200 credit per month

### **Optimization Features**

- **Debouncing**: Reduces API calls by waiting 300ms between keystrokes
- **Minimum length**: Only searches after 2+ characters
- **Session-based**: Uses session tokens for cost optimization
- **Caching**: Browser caches recent suggestions
- **Fallback**: Reduces API dependency

## Configuration Options

### **Search Restrictions**

```typescript
// In fetchLocationSuggestions()
componentRestrictions: {
  country: "us";
} // Restrict to US only
types: ["(cities)"]; // Only show cities, not addresses
```

### **Customization**

You can modify:

- **Country restrictions**: Change `country: "us"` to other countries
- **Search types**: Add `"establishment"`, `"address"`, etc.
- **Fallback locations**: Update static suggestions list
- **Debounce timing**: Adjust 300ms delay in `handleLocationInputChange`

## Troubleshooting

### **Common Issues**

1. **No suggestions appearing**

   - Check if API key is configured correctly
   - Verify API key has Places API enabled
   - Check browser console for errors

2. **Only static suggestions showing**

   - Google Maps API might not be loaded
   - API key might be invalid or restricted
   - Check network connectivity

3. **API quota exceeded**
   - Monitor usage in Google Cloud Console
   - Implement additional caching if needed
   - Consider upgrading billing plan

### **Debug Mode**

Enable debug logging by adding to browser console:

```javascript
localStorage.setItem("debug", "geoGrid:*");
```

## Future Enhancements

### **Planned Features**

- **International support**: Remove US-only restriction
- **Recent searches**: Cache and show recent location searches
- **Favorites**: Allow users to save favorite locations
- **Business search**: Add support for business/establishment search
- **Offline mode**: Enhanced offline fallback with more locations

### **Performance Improvements**

- **Request batching**: Combine multiple API calls
- **Advanced caching**: Implement service worker caching
- **Predictive loading**: Pre-load popular locations

## Security Considerations

### **API Key Security**

- ✅ Use environment variables (not hardcoded)
- ✅ Restrict API key to specific domains
- ✅ Enable only required APIs
- ✅ Monitor usage regularly
- ✅ Set up billing alerts

### **Best Practices**

- Never commit API keys to version control
- Use different keys for development/production
- Implement rate limiting on your backend
- Monitor for unusual usage patterns

## Support

For issues or questions:

1. Check browser console for error messages
2. Verify API key configuration
3. Test with fallback mode (disable API key)
4. Contact development team with specific error details
