{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\geoGrid\\\\geoGrid.screen.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Box, Typography, Paper, Grid, Button, Alert } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService from \"../../services/geoGrid/geoGrid.service\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GeoGridScreen = ({\n  title\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n\n  // Toast context for notifications\n  const {\n    setToastConfig\n  } = useContext(ToastContext);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [gridPoints, setGridPoints] = useState([]);\n  const [gridConfiguration, setGridConfiguration] = useState({\n    name: \"\",\n    centerLat: 0,\n    centerLng: 0,\n    gridSize: \"3x3\",\n    distance: 500,\n    distanceUnit: \"meters\",\n    searchType: \"name\",\n    searchQuery: \"\",\n    isScheduleEnabled: false,\n    settings: {}\n  });\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState([]);\n  const user = useSelector(state => {\n    var _state$authReducer;\n    return (_state$authReducer = state.authReducer) === null || _state$authReducer === void 0 ? void 0 : _state$authReducer.userInfo;\n  });\n  useEffect(() => {\n    document.title = title;\n    if (user !== null && user !== void 0 && user.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n  const loadSavedConfigurations = async () => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error) {\n      setToastConfig(ToastSeverity.Error, \"Failed to load saved configurations\", true);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLocationSearch = async searchRequest => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.searchLocation(searchRequest);\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration(prev => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\",\n          name: \"\" // Clear the name for new searches to avoid overwriting existing configs\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateGrid = async (centerLat, centerLng) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit\n      });\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleConfigurationChange = updates => {\n    setGridConfiguration(prev => ({\n      ...prev,\n      ...updates\n    }));\n  };\n  const handleConfigurationNameChange = name => {\n    setGridConfiguration(prev => ({\n      ...prev,\n      name\n    }));\n  };\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (!(user !== null && user !== void 0 && user.id)) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints\n      };\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoadConfiguration = async config => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const {\n            configuration,\n            gridPoints: loadedPoints\n          } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\"\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteConfiguration = async configId => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.deleteGridConfiguration(configId.toString());\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!(user !== null && user !== void 0 && user.id)) {\n    return /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            marginBottom: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"pageTitle\",\n            children: \"Google Geo Grid\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            className: \"subtitle2\",\n            children: \"Create and manage location-based grids for your business analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            size: \"small\",\n            onClick: () => navigate(\"/\"),\n            variant: \"outlined\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this),\n          children: \"Please log in to access the Google Geo Grid functionality. You can still explore the basic features, but saving configurations requires authentication.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: \"fit-content\"\n              },\n              children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n                onLocationSearch: handleLocationSearch,\n                onGenerateGrid: generateGrid,\n                onSaveConfiguration: () => setError(\"Please log in to save configurations\"),\n                loading: loading,\n                currentLocation: currentLocation,\n                savedConfigurations: [],\n                onLoadConfiguration: () => {},\n                onDeleteConfiguration: () => {},\n                configurationName: gridConfiguration.name,\n                onConfigurationNameChange: handleConfigurationNameChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 600\n              },\n              children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n                center: currentLocation ? {\n                  lat: currentLocation.lat,\n                  lng: currentLocation.lng\n                } : null,\n                gridPoints: gridPoints,\n                loading: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: !!error,\n          autoHideDuration: 6000,\n          onClose: handleCloseSnackbar,\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleCloseSnackbar,\n            severity: \"error\",\n            sx: {\n              width: \"100%\"\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: !!success,\n          autoHideDuration: 4000,\n          onClose: handleCloseSnackbar,\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleCloseSnackbar,\n            severity: \"success\",\n            sx: {\n              width: \"100%\"\n            },\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          marginBottom: \"5px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"pageTitle\",\n          children: \"Google Geo Grid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          className: \"subtitle2\",\n          children: \"Create and manage location-based grids for your business analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: \"fit-content\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n              onLocationSearch: handleLocationSearch,\n              onGenerateGrid: generateGrid,\n              onSaveConfiguration: handleSaveConfiguration,\n              loading: loading,\n              currentLocation: currentLocation,\n              savedConfigurations: savedConfigurations,\n              onLoadConfiguration: handleLoadConfiguration,\n              onDeleteConfiguration: handleDeleteConfiguration,\n              configurationName: gridConfiguration.name,\n              onConfigurationNameChange: handleConfigurationNameChange\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 600\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n              center: currentLocation ? {\n                lat: currentLocation.lat,\n                lng: currentLocation.lng\n              } : null,\n              gridPoints: gridPoints,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: \"fit-content\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridSettings, {\n              configuration: gridConfiguration,\n              onConfigurationChange: handleConfigurationChange,\n              onGenerateGrid: generateGrid,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!error,\n        autoHideDuration: 6000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"error\",\n          sx: {\n            width: \"100%\"\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!success,\n        autoHideDuration: 4000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"success\",\n          sx: {\n            width: \"100%\"\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 358,\n    columnNumber: 5\n  }, this);\n};\n_s(GeoGridScreen, \"+9eztr/gWsfAY4MNSOkE+9Gu1PQ=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = GeoGridScreen;\nexport default GeoGridScreen;\nvar _c;\n$RefreshReg$(_c, \"GeoGridScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Box", "Typography", "Paper", "Grid", "<PERSON><PERSON>", "<PERSON><PERSON>", "useSelector", "useDispatch", "useNavigate", "LeftMenuComponent", "GeoGridControls", "GeoGridMap", "GeoGridSettings", "GeoGridService", "ToastContext", "ToastSeverity", "jsxDEV", "_jsxDEV", "GeoGridScreen", "title", "_s", "dispatch", "navigate", "geoGridService", "loading", "setLoading", "setToastConfig", "currentLocation", "setCurrentLocation", "gridPoints", "setGridPoints", "gridConfiguration", "setGridConfiguration", "name", "centerLat", "centerLng", "gridSize", "distance", "distanceUnit", "searchType", "searchQuery", "isScheduleEnabled", "settings", "activeTab", "setActiveTab", "savedConfigurations", "setSavedConfigurations", "user", "state", "_state$authReducer", "authReducer", "userInfo", "document", "id", "loadSavedConfigurations", "response", "getGridConfigurations", "success", "data", "error", "Error", "handleLocationSearch", "searchRequest", "setError", "searchLocation", "locationData", "prev", "lat", "lng", "query", "generateGrid", "setSuccess", "message", "handleConfigurationChange", "updates", "handleConfigurationNameChange", "handleSaveConfiguration", "trim", "configToSave", "userId", "saveGridConfiguration", "handleLoadConfiguration", "config", "getGridData", "toString", "configuration", "loadedPoints", "address", "placeId", "handleDeleteConfiguration", "configId", "deleteGridConfiguration", "handleCloseSnackbar", "children", "sx", "marginBottom", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "severity", "mt", "action", "color", "size", "onClick", "container", "spacing", "item", "xs", "md", "p", "height", "onLocationSearch", "onGenerateGrid", "onSaveConfiguration", "onLoadConfiguration", "onDeleteConfiguration", "configurationName", "onConfigurationNameChange", "center", "Snackbar", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "onConfigurationChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/geoGrid/geoGrid.screen.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useContext } from \"react\";\nimport {\n  Box,\n  Container,\n  Typography,\n  Paper,\n  Grid,\n  Button,\n  Alert,\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService, {\n  GridConfiguration,\n  GridPoint,\n  LocationSearchRequest,\n} from \"../../services/geoGrid/geoGrid.service\";\nimport { ToastContext } from \"../../context/toast.context\";\nimport { ToastSeverity } from \"../../constants/toastSeverity.constant\";\n\ninterface GeoGridScreenProps {\n  title: string;\n}\n\ninterface LocationData {\n  name: string;\n  lat: number;\n  lng: number;\n  address: string;\n  placeId: string;\n}\n\nconst GeoGridScreen: React.FC<GeoGridScreenProps> = ({ title }) => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n\n  // Toast context for notifications\n  const { setToastConfig } = useContext(ToastContext);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(\n    null\n  );\n  const [gridPoints, setGridPoints] = useState<GridPoint[]>([]);\n  const [gridConfiguration, setGridConfiguration] = useState<GridConfiguration>(\n    {\n      name: \"\",\n      centerLat: 0,\n      centerLng: 0,\n      gridSize: \"3x3\",\n      distance: 500,\n      distanceUnit: \"meters\",\n      searchType: \"name\",\n      searchQuery: \"\",\n      isScheduleEnabled: false,\n      settings: {},\n    }\n  );\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState<\n    GridConfiguration[]\n  >([]);\n\n  const user = useSelector((state: any) => state.authReducer?.userInfo);\n\n  useEffect(() => {\n    document.title = title;\n    if (user?.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n\n  const loadSavedConfigurations = async () => {\n    if (!user?.id) return;\n\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error: any) {\n      setToastConfig(\n        ToastSeverity.Error,\n        \"Failed to load saved configurations\",\n        true\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLocationSearch = async (searchRequest: LocationSearchRequest) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.searchLocation(searchRequest);\n\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration((prev) => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\",\n          name: \"\", // Clear the name for new searches to avoid overwriting existing configs\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateGrid = async (centerLat?: number, centerLng?: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit,\n      });\n\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleConfigurationChange = (updates: Partial<GridConfiguration>) => {\n    setGridConfiguration((prev) => ({ ...prev, ...updates }));\n  };\n\n  const handleConfigurationNameChange = (name: string) => {\n    setGridConfiguration((prev) => ({ ...prev, name }));\n  };\n\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (!user?.id) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints,\n      };\n\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoadConfiguration = async (config: GridConfiguration) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const { configuration, gridPoints: loadedPoints } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\",\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteConfiguration = async (configId: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.deleteGridConfiguration(\n        configId.toString()\n      );\n\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!user?.id) {\n    return (\n      <LeftMenuComponent>\n        <Box>\n          <Box sx={{ marginBottom: \"5px\" }}>\n            <h3 className=\"pageTitle\">Google Geo Grid</h3>\n            <Typography variant=\"subtitle2\" className=\"subtitle2\">\n              Create and manage location-based grids for your business analysis\n            </Typography>\n          </Box>\n\n          <Alert\n            severity=\"warning\"\n            sx={{ mt: 2 }}\n            action={\n              <Button\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => navigate(\"/\")}\n                variant=\"outlined\"\n              >\n                Login\n              </Button>\n            }\n          >\n            Please log in to access the Google Geo Grid functionality. You can\n            still explore the basic features, but saving configurations requires\n            authentication.\n          </Alert>\n\n          {/* Show limited functionality for non-authenticated users */}\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Paper sx={{ p: 2, height: \"fit-content\" }}>\n                <GeoGridControls\n                  onLocationSearch={handleLocationSearch}\n                  onGenerateGrid={generateGrid}\n                  onSaveConfiguration={() =>\n                    setError(\"Please log in to save configurations\")\n                  }\n                  loading={loading}\n                  currentLocation={currentLocation}\n                  savedConfigurations={[]}\n                  onLoadConfiguration={() => {}}\n                  onDeleteConfiguration={() => {}}\n                  configurationName={gridConfiguration.name}\n                  onConfigurationNameChange={handleConfigurationNameChange}\n                />\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Paper sx={{ p: 2, height: 600 }}>\n                <GeoGridMap\n                  center={\n                    currentLocation\n                      ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                      : null\n                  }\n                  gridPoints={gridPoints}\n                  loading={loading}\n                />\n              </Paper>\n            </Grid>\n          </Grid>\n\n          {/* Snackbar for notifications */}\n          <Snackbar\n            open={!!error}\n            autoHideDuration={6000}\n            onClose={handleCloseSnackbar}\n            anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n          >\n            <Alert\n              onClose={handleCloseSnackbar}\n              severity=\"error\"\n              sx={{ width: \"100%\" }}\n            >\n              {error}\n            </Alert>\n          </Snackbar>\n\n          <Snackbar\n            open={!!success}\n            autoHideDuration={4000}\n            onClose={handleCloseSnackbar}\n            anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n          >\n            <Alert\n              onClose={handleCloseSnackbar}\n              severity=\"success\"\n              sx={{ width: \"100%\" }}\n            >\n              {success}\n            </Alert>\n          </Snackbar>\n        </Box>\n      </LeftMenuComponent>\n    );\n  }\n\n  return (\n    <LeftMenuComponent>\n      <Box>\n        <Box sx={{ marginBottom: \"5px\" }}>\n          <h3 className=\"pageTitle\">Google Geo Grid</h3>\n          <Typography variant=\"subtitle2\" className=\"subtitle2\">\n            Create and manage location-based grids for your business analysis\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Controls Panel */}\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: \"fit-content\" }}>\n              <GeoGridControls\n                onLocationSearch={handleLocationSearch}\n                onGenerateGrid={generateGrid}\n                onSaveConfiguration={handleSaveConfiguration}\n                loading={loading}\n                currentLocation={currentLocation}\n                savedConfigurations={savedConfigurations}\n                onLoadConfiguration={handleLoadConfiguration}\n                onDeleteConfiguration={handleDeleteConfiguration}\n                configurationName={gridConfiguration.name}\n                onConfigurationNameChange={handleConfigurationNameChange}\n              />\n            </Paper>\n          </Grid>\n\n          {/* Map Panel */}\n          <Grid item xs={12} md={5}>\n            <Paper sx={{ p: 2, height: 600 }}>\n              <GeoGridMap\n                center={\n                  currentLocation\n                    ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                    : null\n                }\n                gridPoints={gridPoints}\n                loading={loading}\n              />\n            </Paper>\n          </Grid>\n\n          {/* Settings Panel */}\n          <Grid item xs={12} md={3}>\n            <Paper sx={{ p: 2, height: \"fit-content\" }}>\n              <GeoGridSettings\n                configuration={gridConfiguration}\n                onConfigurationChange={handleConfigurationChange}\n                onGenerateGrid={generateGrid}\n                loading={loading}\n              />\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Snackbar for notifications */}\n        <Snackbar\n          open={!!error}\n          autoHideDuration={6000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"error\"\n            sx={{ width: \"100%\" }}\n          >\n            {error}\n          </Alert>\n        </Snackbar>\n\n        <Snackbar\n          open={!!success}\n          autoHideDuration={4000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"success\"\n            sx={{ width: \"100%\" }}\n          >\n            {success}\n          </Alert>\n        </Snackbar>\n      </Box>\n    </LeftMenuComponent>\n  );\n};\n\nexport default GeoGridScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAeC,UAAU,QAAQ,OAAO;AAC3E,SACEC,GAAG,EAEHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,UAAU,MAAM,+CAA+C;AACtE,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,cAAc,MAId,wCAAwC;AAC/C,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcvE,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,cAAc,CAAC,GAAG1B,QAAQ,CAAC,IAAIgB,cAAc,CAACQ,QAAQ,CAAC,CAAC;EAC/D,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM;IAAE6B;EAAe,CAAC,GAAG3B,UAAU,CAACe,YAAY,CAAC;;EAEnD;EACA,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CACpD,IACF,CAAC;EACD,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CACxD;IACEoC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,QAAQ,EAAE,CAAC;EACb,CACF,CAAC;;EAED;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAE5D,EAAE,CAAC;EAEL,MAAMkD,IAAI,GAAGzC,WAAW,CAAE0C,KAAU;IAAA,IAAAC,kBAAA;IAAA,QAAAA,kBAAA,GAAKD,KAAK,CAACE,WAAW,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,QAAQ;EAAA,EAAC;EAErErD,SAAS,CAAC,MAAM;IACdsD,QAAQ,CAACjC,KAAK,GAAGA,KAAK;IACtB,IAAI4B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,EAAE;MACZC,uBAAuB,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACnC,KAAK,EAAE4B,IAAI,CAAC,CAAC;EAEjB,MAAMO,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,EAACP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IAEf,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,QAAQ,GAAG,MAAMhC,cAAc,CAACiC,qBAAqB,CAACT,IAAI,CAACM,EAAE,CAAC;MACpE,IAAIE,QAAQ,CAACE,OAAO,EAAE;QACpBX,sBAAsB,CAACS,QAAQ,CAACG,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBjC,cAAc,CACZX,aAAa,CAAC6C,KAAK,EACnB,qCAAqC,EACrC,IACF,CAAC;IACH,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAG,MAAOC,aAAoC,IAAK;IAC3E,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBsC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMR,QAAQ,GAAG,MAAMhC,cAAc,CAACyC,cAAc,CAACF,aAAa,CAAC;MAEnE,IAAIP,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMQ,YAAY,GAAGV,QAAQ,CAACG,IAAI;QAClC9B,kBAAkB,CAACqC,YAAY,CAAC;QAChCjC,oBAAoB,CAAEkC,IAAI,KAAM;UAC9B,GAAGA,IAAI;UACPhC,SAAS,EAAE+B,YAAY,CAACE,GAAG;UAC3BhC,SAAS,EAAE8B,YAAY,CAACG,GAAG;UAC3B7B,UAAU,EAAEuB,aAAa,CAACvB,UAAU;UACpCC,WAAW,EAAEsB,aAAa,CAACO,KAAK,IAAI,EAAE;UACtCpC,IAAI,EAAE,EAAE,CAAE;QACZ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMqC,YAAY,CAACL,YAAY,CAACE,GAAG,EAAEF,YAAY,CAACG,GAAG,CAAC;QACtDG,UAAU,CAAC,iDAAiD,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MACnBI,QAAQ,CAACJ,KAAK,CAACa,OAAO,IAAI,2BAA2B,CAAC;IACxD,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAOpC,SAAkB,EAAEC,SAAkB,KAAK;IACrE,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChBsC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMI,GAAG,GAAGjC,SAAS,IAAIH,iBAAiB,CAACG,SAAS;MACpD,MAAMkC,GAAG,GAAGjC,SAAS,IAAIJ,iBAAiB,CAACI,SAAS;MAEpD,IAAI,CAACgC,GAAG,IAAI,CAACC,GAAG,EAAE;QAChB,MAAM,IAAIR,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAML,QAAQ,GAAG,MAAMhC,cAAc,CAAC+C,YAAY,CAAC;QACjDpC,SAAS,EAAEiC,GAAG;QACdhC,SAAS,EAAEiC,GAAG;QACdhC,QAAQ,EAAEL,iBAAiB,CAACK,QAAQ;QACpCC,QAAQ,EAAEN,iBAAiB,CAACM,QAAQ;QACpCC,YAAY,EAAEP,iBAAiB,CAACO;MAClC,CAAC,CAAC;MAEF,IAAIiB,QAAQ,CAACE,OAAO,EAAE;QACpB3B,aAAa,CAACyB,QAAQ,CAACG,IAAI,CAAC7B,UAAU,CAAC;QACvC0C,UAAU,CAAC,8BAA8B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MACnBI,QAAQ,CAACJ,KAAK,CAACa,OAAO,IAAI,yBAAyB,CAAC;IACtD,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgD,yBAAyB,GAAIC,OAAmC,IAAK;IACzE1C,oBAAoB,CAAEkC,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,GAAGQ;IAAQ,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,6BAA6B,GAAI1C,IAAY,IAAK;IACtDD,oBAAoB,CAAEkC,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjC;IAAK,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAM2C,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACFnD,UAAU,CAAC,IAAI,CAAC;MAChBsC,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,EAAChB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;QACb,MAAM,IAAIO,KAAK,CAAC,sCAAsC,CAAC;MACzD;MAEA,IAAI,CAAC7B,iBAAiB,CAACE,IAAI,CAAC4C,IAAI,CAAC,CAAC,EAAE;QAClC,MAAM,IAAIjB,KAAK,CAAC,gCAAgC,CAAC;MACnD;MAEA,MAAMkB,YAAY,GAAG;QACnB,GAAG/C,iBAAiB;QACpBgD,MAAM,EAAEhC,IAAI,CAACM,EAAE;QACfxB;MACF,CAAC;MAED,MAAM0B,QAAQ,GAAG,MAAMhC,cAAc,CAACyD,qBAAqB,CAACF,YAAY,CAAC;MAEzE,IAAIvB,QAAQ,CAACE,OAAO,EAAE;QACpBc,UAAU,CAAC,mCAAmC,CAAC;QAC/C,MAAMjB,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBI,QAAQ,CAACJ,KAAK,CAACa,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,uBAAuB,GAAG,MAAOC,MAAyB,IAAK;IACnE,IAAI;MACFzD,UAAU,CAAC,IAAI,CAAC;MAChBsC,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAImB,MAAM,CAAC7B,EAAE,EAAE;QACb,MAAME,QAAQ,GAAG,MAAMhC,cAAc,CAAC4D,WAAW,CAACD,MAAM,CAAC7B,EAAE,CAAC+B,QAAQ,CAAC,CAAC,CAAC;QACvE,IAAI7B,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM;YAAE4B,aAAa;YAAExD,UAAU,EAAEyD;UAAa,CAAC,GAAG/B,QAAQ,CAACG,IAAI;UACjE1B,oBAAoB,CAACqD,aAAa,CAAC;UACnCvD,aAAa,CAACwD,YAAY,IAAI,EAAE,CAAC;UACjC1D,kBAAkB,CAAC;YACjBK,IAAI,EAAEoD,aAAa,CAAC7C,WAAW,IAAI,iBAAiB;YACpD2B,GAAG,EAAEkB,aAAa,CAACnD,SAAS;YAC5BkC,GAAG,EAAEiB,aAAa,CAAClD,SAAS;YAC5BoD,OAAO,EAAE,EAAE;YACXC,OAAO,EAAE;UACX,CAAC,CAAC;UACFjB,UAAU,CAAC,oCAAoC,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOZ,KAAU,EAAE;MACnBI,QAAQ,CAACJ,KAAK,CAACa,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,yBAAyB,GAAG,MAAOC,QAAgB,IAAK;IAC5D,IAAI;MACFjE,UAAU,CAAC,IAAI,CAAC;MAChBsC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMR,QAAQ,GAAG,MAAMhC,cAAc,CAACoE,uBAAuB,CAC3DD,QAAQ,CAACN,QAAQ,CAAC,CACpB,CAAC;MAED,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QACpBc,UAAU,CAAC,qCAAqC,CAAC;QACjD,MAAMjB,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBI,QAAQ,CAACJ,KAAK,CAACa,OAAO,IAAI,gCAAgC,CAAC;IAC7D,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmE,mBAAmB,GAAGA,CAAA,KAAM;IAChC7B,QAAQ,CAAC,IAAI,CAAC;IACdQ,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;;EAED;EACA,IAAI,EAACxB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IACb,oBACEpC,OAAA,CAACR,iBAAiB;MAAAoF,QAAA,eAChB5E,OAAA,CAACjB,GAAG;QAAA6F,QAAA,gBACF5E,OAAA,CAACjB,GAAG;UAAC8F,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBAC/B5E,OAAA;YAAI+E,SAAS,EAAC,WAAW;YAAAH,QAAA,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CnF,OAAA,CAAChB,UAAU;YAACoG,OAAO,EAAC,WAAW;YAACL,SAAS,EAAC,WAAW;YAAAH,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENnF,OAAA,CAACZ,KAAK;UACJiG,QAAQ,EAAC,SAAS;UAClBR,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UACdC,MAAM,eACJvF,OAAA,CAACb,MAAM;YACLqG,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,GAAG,CAAE;YAC7B+E,OAAO,EAAC,UAAU;YAAAR,QAAA,EACnB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAP,QAAA,EACF;QAID;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAGRnF,OAAA,CAACd,IAAI;UAACyG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzB5E,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvB5E,OAAA,CAACf,KAAK;cAAC4F,EAAE,EAAE;gBAAEmB,CAAC,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAc,CAAE;cAAArB,QAAA,eACzC5E,OAAA,CAACP,eAAe;gBACdyG,gBAAgB,EAAEtD,oBAAqB;gBACvCuD,cAAc,EAAE9C,YAAa;gBAC7B+C,mBAAmB,EAAEA,CAAA,KACnBtD,QAAQ,CAAC,sCAAsC,CAChD;gBACDvC,OAAO,EAAEA,OAAQ;gBACjBG,eAAe,EAAEA,eAAgB;gBACjCkB,mBAAmB,EAAE,EAAG;gBACxByE,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAE;gBAC9BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAE;gBAChCC,iBAAiB,EAAEzF,iBAAiB,CAACE,IAAK;gBAC1CwF,yBAAyB,EAAE9C;cAA8B;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEPnF,OAAA,CAACd,IAAI;YAAC2G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvB5E,OAAA,CAACf,KAAK;cAAC4F,EAAE,EAAE;gBAAEmB,CAAC,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAAArB,QAAA,eAC/B5E,OAAA,CAACN,UAAU;gBACT+G,MAAM,EACJ/F,eAAe,GACX;kBAAEwC,GAAG,EAAExC,eAAe,CAACwC,GAAG;kBAAEC,GAAG,EAAEzC,eAAe,CAACyC;gBAAI,CAAC,GACtD,IACL;gBACDvC,UAAU,EAAEA,UAAW;gBACvBL,OAAO,EAAEA;cAAQ;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPnF,OAAA,CAAC0G,QAAQ;UACPC,IAAI,EAAE,CAAC,CAACjE,KAAM;UACdkE,gBAAgB,EAAE,IAAK;UACvBC,OAAO,EAAElC,mBAAoB;UAC7BmC,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAE;UAAApC,QAAA,eAE1D5E,OAAA,CAACZ,KAAK;YACJyH,OAAO,EAAElC,mBAAoB;YAC7BU,QAAQ,EAAC,OAAO;YAChBR,EAAE,EAAE;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAArC,QAAA,EAErBlC;UAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEXnF,OAAA,CAAC0G,QAAQ;UACPC,IAAI,EAAE,CAAC,CAACnE,OAAQ;UAChBoE,gBAAgB,EAAE,IAAK;UACvBC,OAAO,EAAElC,mBAAoB;UAC7BmC,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAE;UAAApC,QAAA,eAE1D5E,OAAA,CAACZ,KAAK;YACJyH,OAAO,EAAElC,mBAAoB;YAC7BU,QAAQ,EAAC,SAAS;YAClBR,EAAE,EAAE;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAArC,QAAA,EAErBpC;UAAO;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAExB;EAEA,oBACEnF,OAAA,CAACR,iBAAiB;IAAAoF,QAAA,eAChB5E,OAAA,CAACjB,GAAG;MAAA6F,QAAA,gBACF5E,OAAA,CAACjB,GAAG;QAAC8F,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAF,QAAA,gBAC/B5E,OAAA;UAAI+E,SAAS,EAAC,WAAW;UAAAH,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9CnF,OAAA,CAAChB,UAAU;UAACoG,OAAO,EAAC,WAAW;UAACL,SAAS,EAAC,WAAW;UAAAH,QAAA,EAAC;QAEtD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENnF,OAAA,CAACd,IAAI;QAACyG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhB,QAAA,gBAEzB5E,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB5E,OAAA,CAACf,KAAK;YAAC4F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAc,CAAE;YAAArB,QAAA,eACzC5E,OAAA,CAACP,eAAe;cACdyG,gBAAgB,EAAEtD,oBAAqB;cACvCuD,cAAc,EAAE9C,YAAa;cAC7B+C,mBAAmB,EAAEzC,uBAAwB;cAC7CpD,OAAO,EAAEA,OAAQ;cACjBG,eAAe,EAAEA,eAAgB;cACjCkB,mBAAmB,EAAEA,mBAAoB;cACzCyE,mBAAmB,EAAErC,uBAAwB;cAC7CsC,qBAAqB,EAAE9B,yBAA0B;cACjD+B,iBAAiB,EAAEzF,iBAAiB,CAACE,IAAK;cAC1CwF,yBAAyB,EAAE9C;YAA8B;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPnF,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB5E,OAAA,CAACf,KAAK;YAAC4F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAI,CAAE;YAAArB,QAAA,eAC/B5E,OAAA,CAACN,UAAU;cACT+G,MAAM,EACJ/F,eAAe,GACX;gBAAEwC,GAAG,EAAExC,eAAe,CAACwC,GAAG;gBAAEC,GAAG,EAAEzC,eAAe,CAACyC;cAAI,CAAC,GACtD,IACL;cACDvC,UAAU,EAAEA,UAAW;cACvBL,OAAO,EAAEA;YAAQ;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPnF,OAAA,CAACd,IAAI;UAAC2G,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB5E,OAAA,CAACf,KAAK;YAAC4F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAc,CAAE;YAAArB,QAAA,eACzC5E,OAAA,CAACL,eAAe;cACdyE,aAAa,EAAEtD,iBAAkB;cACjCoG,qBAAqB,EAAE1D,yBAA0B;cACjD2C,cAAc,EAAE9C,YAAa;cAC7B9C,OAAO,EAAEA;YAAQ;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPnF,OAAA,CAAC0G,QAAQ;QACPC,IAAI,EAAE,CAAC,CAACjE,KAAM;QACdkE,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAElC,mBAAoB;QAC7BmC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAApC,QAAA,eAE1D5E,OAAA,CAACZ,KAAK;UACJyH,OAAO,EAAElC,mBAAoB;UAC7BU,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEoC,KAAK,EAAE;UAAO,CAAE;UAAArC,QAAA,EAErBlC;QAAK;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEXnF,OAAA,CAAC0G,QAAQ;QACPC,IAAI,EAAE,CAAC,CAACnE,OAAQ;QAChBoE,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAElC,mBAAoB;QAC7BmC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAApC,QAAA,eAE1D5E,OAAA,CAACZ,KAAK;UACJyH,OAAO,EAAElC,mBAAoB;UAC7BU,QAAQ,EAAC,SAAS;UAClBR,EAAE,EAAE;YAAEoC,KAAK,EAAE;UAAO,CAAE;UAAArC,QAAA,EAErBpC;QAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAExB,CAAC;AAAChF,EAAA,CA1ZIF,aAA2C;EAAA,QAC9BX,WAAW,EACXC,WAAW,EAiCfF,WAAW;AAAA;AAAA8H,EAAA,GAnCpBlH,aAA2C;AA4ZjD,eAAeA,aAAa;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}