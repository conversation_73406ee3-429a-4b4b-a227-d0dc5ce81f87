{"ast": null, "code": "'use client';\n\n/* eslint-disable no-constant-condition */\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\n\n// To replace with .findIndex() once we stop IE11 support.\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) == null ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nexport function useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return findIndex(filteredOptions, option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      var _getOptionKey;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey == null ? void 0 : getOptionKey(option)) != null ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_setRef", "setRef", "unstable_useEventCallback", "useEventCallback", "unstable_useControlled", "useControlled", "unstable_useId", "useId", "usePreviousProps", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "indexOf", "slice", "findIndex", "array", "comp", "i", "length", "defaultFilterOptions", "pageSize", "defaultIsActiveElementInListbox", "listboxRef", "_listboxRef$current$p", "current", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "useAutocomplete", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "multiple", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "_option$label", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "value", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "optionLabel", "process", "env", "NODE_ENV", "erroneousReturn", "undefined", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "useState", "focusedTag", "setFocusedTag", "defaultHighlighted", "highlightedIndexRef", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "isOptionSelected", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "useEffect", "valueChange", "listboxAvailable", "missing<PERSON><PERSON><PERSON>", "warn", "join", "focusTag", "tagToFocus", "focus", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "reason", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "setSelectionRange", "getPreviousHighlightedOptionIndex", "isSameValue", "value1", "label1", "label2", "every", "val", "previousHighlightedOption", "syncHighlightedIndex", "previousHighlightedOptionIndex", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validTagIndex", "handleFocusTag", "nextTag", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleTagDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getPopupIndicatorProps", "getTagProps", "onDelete", "getListboxProps", "getOptionProps", "_getO<PERSON><PERSON>ey", "selected", "onMouseMove", "onTouchStart", "expanded"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable no-constant-condition */\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\n\n// To replace with .findIndex() once we stop IE11 support.\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) == null ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nexport function useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return findIndex(filteredOptions, option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      var _getOptionKey;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey == null ? void 0 : getOptionKey(option)) != null ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,IAAIC,MAAM,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,IAAIC,KAAK,EAAEC,gBAAgB,QAAQ,YAAY;;AAEzL;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAO,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,GAAGD,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,GAAGF,MAAM;AACnH;AACA,OAAO,SAASG,mBAAmBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IACJC,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGN,MAAM;EACV,OAAO,CAACO,OAAO,EAAE;IACfC,UAAU;IACVC;EACF,CAAC,KAAK;IACJ,IAAIC,KAAK,GAAGJ,IAAI,GAAGE,UAAU,CAACF,IAAI,CAAC,CAAC,GAAGE,UAAU;IACjD,IAAIN,UAAU,EAAE;MACdQ,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IACA,IAAIV,aAAa,EAAE;MACjBS,KAAK,GAAGf,eAAe,CAACe,KAAK,CAAC;IAChC;IACA,MAAME,eAAe,GAAG,CAACF,KAAK,GAAGH,OAAO,GAAGA,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;MAClE,IAAIC,SAAS,GAAG,CAACV,SAAS,IAAII,cAAc,EAAEK,MAAM,CAAC;MACrD,IAAIZ,UAAU,EAAE;QACda,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MACA,IAAIV,aAAa,EAAE;QACjBc,SAAS,GAAGpB,eAAe,CAACoB,SAAS,CAAC;MACxC;MACA,OAAOX,SAAS,KAAK,OAAO,GAAGW,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/F,CAAC,CAAC;IACF,OAAO,OAAOP,KAAK,KAAK,QAAQ,GAAGS,eAAe,CAACK,KAAK,CAAC,CAAC,EAAEd,KAAK,CAAC,GAAGS,eAAe;EACtF,CAAC;AACH;;AAEA;AACA,SAASM,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACxC,IAAID,IAAI,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;MAClB,OAAOA,CAAC;IACV;EACF;EACA,OAAO,CAAC,CAAC;AACX;AACA,MAAME,oBAAoB,GAAGxB,mBAAmB,CAAC,CAAC;;AAElD;AACA,MAAMyB,QAAQ,GAAG,CAAC;AAClB,MAAMC,+BAA+B,GAAGC,UAAU,IAAI;EACpD,IAAIC,qBAAqB;EACzB,OAAOD,UAAU,CAACE,OAAO,KAAK,IAAI,KAAK,CAACD,qBAAqB,GAAGD,UAAU,CAACE,OAAO,CAACC,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACG,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,CAAC;AAC9K,CAAC;AACD,MAAMC,sBAAsB,GAAG,EAAE;AACjC,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,MAAM;IACJ;IACAC,iCAAiC,GAAGX,+BAA+B;IACnE;IACAY,wBAAwB,GAAG,KAAK;IAChCC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACP,KAAK,CAACQ,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGX,KAAK,CAACY,QAAQ,GAAGd,sBAAsB,GAAG,IAAI;IAC7De,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAG/B,oBAAoB;IACpCgC,qBAAqB,GAAG,KAAK;IAC7BZ,QAAQ,GAAG,KAAK;IAChBa,iBAAiB;IACjBC,YAAY;IACZhD,cAAc,EAAEiD,kBAAkB,GAAG5C,MAAM,IAAI;MAC7C,IAAI6C,aAAa;MACjB,OAAO,CAACA,aAAa,GAAG7C,MAAM,CAAC8C,KAAK,KAAK,IAAI,GAAGD,aAAa,GAAG7C,MAAM;IACxE,CAAC;IACD+C,OAAO;IACPC,iBAAiB,GAAG,CAAC3B,KAAK,CAACQ,QAAQ;IACnCoB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BzD,UAAU,EAAE0D,cAAc;IAC1BC,oBAAoB,GAAGA,CAACrD,MAAM,EAAEsD,KAAK,KAAKtD,MAAM,KAAKsD,KAAK;IAC1DrB,QAAQ,GAAG,KAAK;IAChBsB,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBrE,OAAO;IACPsE,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAAC3C,KAAK,CAACQ,QAAQ;IAC/ByB,KAAK,EAAEW;EACT,CAAC,GAAG5C,KAAK;EACT,MAAM4B,EAAE,GAAGtE,KAAK,CAACuE,MAAM,CAAC;EACxB,IAAIvD,cAAc,GAAGiD,kBAAkB;EACvCjD,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMkE,WAAW,GAAGtB,kBAAkB,CAAC5C,MAAM,CAAC;IAC9C,IAAI,OAAOkE,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAGJ,WAAW,KAAKK,SAAS,GAAG,WAAW,GAAG,GAAG,OAAOL,WAAW,KAAKA,WAAW,GAAG;QAC1GM,OAAO,CAACC,KAAK,CAAC,yCAAyC1C,aAAa,aAAauC,eAAe,4BAA4BI,IAAI,CAACnF,SAAS,CAACS,MAAM,CAAC,GAAG,CAAC;MACxJ;MACA,OAAO2E,MAAM,CAACT,WAAW,CAAC;IAC5B;IACA,OAAOA,WAAW;EACpB,CAAC;EACD,MAAMU,WAAW,GAAGzG,KAAK,CAAC0G,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAG3G,KAAK,CAAC0G,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG5G,KAAK,CAAC0G,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMjE,UAAU,GAAGzC,KAAK,CAAC0G,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG9G,KAAK,CAAC+G,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjH,KAAK,CAAC+G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMG,kBAAkB,GAAG5D,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM6D,mBAAmB,GAAGnH,KAAK,CAAC0G,MAAM,CAACQ,kBAAkB,CAAC;EAC5D,MAAM,CAAC/B,KAAK,EAAEiC,aAAa,CAAC,GAAG9G,aAAa,CAAC;IAC3C+G,UAAU,EAAEvB,SAAS;IACrBwB,OAAO,EAAEzD,YAAY;IACrB0D,IAAI,EAAE3D;EACR,CAAC,CAAC;EACF,MAAM,CAACrC,UAAU,EAAEiG,kBAAkB,CAAC,GAAGlH,aAAa,CAAC;IACrD+G,UAAU,EAAEpC,cAAc;IAC1BqC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3H,KAAK,CAAC+G,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,eAAe,GAAG5H,KAAK,CAAC6H,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC7D;IACA;IACA,MAAMC,gBAAgB,GAAGlE,QAAQ,GAAGqB,KAAK,CAAC9C,MAAM,GAAG0F,QAAQ,CAAC1F,MAAM,GAAG0F,QAAQ,KAAK,IAAI;IACtF,IAAI,CAACC,gBAAgB,IAAI,CAACvE,WAAW,EAAE;MACrC;IACF;IACA,IAAIwE,aAAa;IACjB,IAAInE,QAAQ,EAAE;MACZmE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM,IAAIF,QAAQ,IAAI,IAAI,EAAE;MAC3BE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM;MACL,MAAMlC,WAAW,GAAGvE,cAAc,CAACuG,QAAQ,CAAC;MAC5CE,aAAa,GAAG,OAAOlC,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;IACpE;IACA,IAAIxE,UAAU,KAAK0G,aAAa,EAAE;MAChC;IACF;IACAT,kBAAkB,CAACS,aAAa,CAAC;IACjC,IAAI1C,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAEG,aAAa,EAAE,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAACzG,cAAc,EAAED,UAAU,EAAEuC,QAAQ,EAAEyB,aAAa,EAAEiC,kBAAkB,EAAE/D,WAAW,EAAE0B,KAAK,CAAC,CAAC;EACjG,MAAM,CAACM,IAAI,EAAEyC,YAAY,CAAC,GAAG5H,aAAa,CAAC;IACzC+G,UAAU,EAAE3B,QAAQ;IACpB4B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,KAAK,CAAC+G,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMsB,yBAAyB,GAAG,CAACvE,QAAQ,IAAIqB,KAAK,IAAI,IAAI,IAAI5D,UAAU,KAAKC,cAAc,CAAC2D,KAAK,CAAC;EACpG,MAAMmD,SAAS,GAAG7C,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAMjE,eAAe,GAAG2G,SAAS,GAAGjE,aAAa,CAAC/C,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;IACzE,IAAIyC,qBAAqB,IAAI,CAACR,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEoD,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAItD,oBAAoB,CAACrD,MAAM,EAAE2G,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;IACEjH,UAAU,EAAE8G,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAG5G,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAMiH,aAAa,GAAGhI,gBAAgB,CAAC;IACrCkB,eAAe;IACfwD,KAAK;IACL5D;EACF,CAAC,CAAC;EACFvB,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGxD,KAAK,KAAKsD,aAAa,CAACtD,KAAK;IACjD,IAAIuC,OAAO,IAAI,CAACiB,WAAW,EAAE;MAC3B;IACF;;IAEA;IACA,IAAIjF,QAAQ,IAAI,CAACiF,WAAW,EAAE;MAC5B;IACF;IACAf,eAAe,CAAC,IAAI,EAAEzC,KAAK,CAAC;EAC9B,CAAC,EAAE,CAACA,KAAK,EAAEyC,eAAe,EAAEF,OAAO,EAAEe,aAAa,CAACtD,KAAK,EAAEzB,QAAQ,CAAC,CAAC;EACpE,MAAMkF,gBAAgB,GAAGnD,IAAI,IAAI9D,eAAe,CAACU,MAAM,GAAG,CAAC,IAAI,CAACuD,QAAQ;EACxE,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIf,KAAK,KAAK,IAAI,IAAI,CAACzB,QAAQ,IAAIpC,OAAO,CAACe,MAAM,GAAG,CAAC,EAAE;MACrD,MAAMwG,YAAY,GAAG,CAAC/E,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEvD,MAAM,CAAC4G,MAAM,IAAI,CAAClH,OAAO,CAACiH,IAAI,CAAC1G,MAAM,IAAIqD,oBAAoB,CAACrD,MAAM,EAAE2G,MAAM,CAAC,CAAC,CAAC;MACjI,IAAIK,YAAY,CAACxG,MAAM,GAAG,CAAC,EAAE;QAC3BgE,OAAO,CAACyC,IAAI,CAAC,CAAC,8BAA8BlF,aAAa,cAAc,EAAE,oCAAoCiF,YAAY,CAACxG,MAAM,GAAG,CAAC,GAAGkE,IAAI,CAACnF,SAAS,CAACyH,YAAY,CAAC,GAAGtC,IAAI,CAACnF,SAAS,CAACyH,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,6EAA6E,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACxS;IACF;EACF;EACA,MAAMC,QAAQ,GAAG5I,gBAAgB,CAAC6I,UAAU,IAAI;IAC9C,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBrC,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLrC,QAAQ,CAACsC,aAAa,CAAC,oBAAoBF,UAAU,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACpE;EACF,CAAC,CAAC;;EAEF;EACAlJ,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpB,IAAI5E,QAAQ,IAAIkD,UAAU,GAAG7B,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;MAC7C4E,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC7D,KAAK,EAAErB,QAAQ,EAAEkD,UAAU,EAAEgC,QAAQ,CAAC,CAAC;EAC3C,SAASI,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC7G,UAAU,CAACE,OAAO,IAAI0G,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI1H,eAAe,CAACU,MAAM,EAAE;MACvE,OAAO,CAAC,CAAC;IACX;IACA,IAAIkH,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX,MAAMxH,MAAM,GAAGY,UAAU,CAACE,OAAO,CAACwG,aAAa,CAAC,uBAAuBI,SAAS,IAAI,CAAC;;MAErF;MACA,MAAMC,iBAAiB,GAAGrF,sBAAsB,GAAG,KAAK,GAAG,CAACtC,MAAM,IAAIA,MAAM,CAACoC,QAAQ,IAAIpC,MAAM,CAAC4H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MACxI,IAAI5H,MAAM,IAAIA,MAAM,CAAC6H,YAAY,CAAC,UAAU,CAAC,IAAI,CAACF,iBAAiB,EAAE;QACnE;QACA,OAAOD,SAAS;MAClB;;MAEA;MACA;MACA,IAAID,SAAS,KAAK,MAAM,EAAE;QACxBC,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,IAAI5H,eAAe,CAACU,MAAM;MACtD,CAAC,MAAM;QACLkH,SAAS,GAAG,CAACA,SAAS,GAAG,CAAC,GAAG5H,eAAe,CAACU,MAAM,IAAIV,eAAe,CAACU,MAAM;MAC/E;;MAEA;MACA;MACA,IAAIkH,SAAS,KAAKF,KAAK,EAAE;QACvB,OAAO,CAAC,CAAC;MACX;IACF;EACF;EACA,MAAMM,mBAAmB,GAAGvJ,gBAAgB,CAAC,CAAC;IAC5C0H,KAAK;IACLuB,KAAK;IACLO,MAAM,GAAG;EACX,CAAC,KAAK;IACJzC,mBAAmB,CAACxE,OAAO,GAAG0G,KAAK;;IAEnC;IACA,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBzC,QAAQ,CAACjE,OAAO,CAACkH,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLjD,QAAQ,CAACjE,OAAO,CAACmH,YAAY,CAAC,uBAAuB,EAAE,GAAGhF,EAAE,WAAWuE,KAAK,EAAE,CAAC;IACjF;IACA,IAAI/D,iBAAiB,EAAE;MACrBA,iBAAiB,CAACwC,KAAK,EAAEuB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG1H,eAAe,CAAC0H,KAAK,CAAC,EAAEO,MAAM,CAAC;IAChF;IACA,IAAI,CAACnH,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;IACA,MAAMoH,IAAI,GAAGtH,UAAU,CAACE,OAAO,CAACwG,aAAa,CAAC,mBAAmB/F,wBAAwB,UAAU,CAAC;IACpG,IAAI2G,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG7G,wBAAwB,UAAU,CAAC;MAC5D2G,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG7G,wBAAwB,eAAe,CAAC;IACnE;IACA,IAAI8G,WAAW,GAAGzH,UAAU,CAACE,OAAO;IACpC,IAAIF,UAAU,CAACE,OAAO,CAAC8G,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;MACzDS,WAAW,GAAGzH,UAAU,CAACE,OAAO,CAACC,aAAa,CAACuG,aAAa,CAAC,kBAAkB,CAAC;IAClF;;IAEA;IACA,IAAI,CAACe,WAAW,EAAE;MAChB;IACF;IACA,IAAIb,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBa,WAAW,CAACC,SAAS,GAAG,CAAC;MACzB;IACF;IACA,MAAMtI,MAAM,GAAGY,UAAU,CAACE,OAAO,CAACwG,aAAa,CAAC,uBAAuBE,KAAK,IAAI,CAAC;IACjF,IAAI,CAACxH,MAAM,EAAE;MACX;IACF;IACAA,MAAM,CAACmI,SAAS,CAACI,GAAG,CAAC,GAAGhH,wBAAwB,UAAU,CAAC;IAC3D,IAAIwG,MAAM,KAAK,UAAU,EAAE;MACzB/H,MAAM,CAACmI,SAAS,CAACI,GAAG,CAAC,GAAGhH,wBAAwB,eAAe,CAAC;IAClE;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI8G,WAAW,CAACG,YAAY,GAAGH,WAAW,CAACI,YAAY,IAAIV,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;MACnG,MAAMW,OAAO,GAAG1I,MAAM;MACtB,MAAM2I,YAAY,GAAGN,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACC,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAC9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCN,WAAW,CAACC,SAAS,GAAGM,aAAa,GAAGP,WAAW,CAACI,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI/F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGsF,WAAW,CAACC,SAAS,EAAE;QACjGD,WAAW,CAACC,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAI/F,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAMgG,sBAAsB,GAAGxK,gBAAgB,CAAC,CAAC;IAC/C0H,KAAK;IACL+C,IAAI;IACJvB,SAAS,GAAG,MAAM;IAClBM,MAAM,GAAG;EACX,CAAC,KAAK;IACJ,IAAI,CAACtB,SAAS,EAAE;MACd;IACF;IACA,MAAMwC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAGpJ,eAAe,CAACU,MAAM,GAAG,CAAC;MAC3C,IAAIwI,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO3D,kBAAkB;MAC3B;MACA,IAAI2D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MACA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MACA,MAAMC,QAAQ,GAAG7D,mBAAmB,CAACxE,OAAO,GAAGkI,IAAI;MACnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIhG,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI+C,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAIsI,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QACA,OAAOE,QAAQ;MACjB;MACA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAI/F,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QACA,IAAIZ,eAAe,IAAI6G,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QACA,OAAO,CAAC;MACV;MACA,OAAOC,QAAQ;IACjB,CAAC;IACD,MAAMG,SAAS,GAAG/B,gBAAgB,CAAC0B,YAAY,CAAC,CAAC,EAAExB,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAE8B,SAAS;MAChBvB,MAAM;MACN9B;IACF,CAAC,CAAC;;IAEF;IACA,IAAIzE,YAAY,IAAIwH,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBvE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,GAAG5D,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAACwJ,SAAS,CAAC,CAAC;QACzDvE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,GAAGtD,MAAM;;QAE/B;QACA;QACA,MAAMwH,KAAK,GAAGxH,MAAM,CAACH,WAAW,CAAC,CAAC,CAACK,OAAO,CAACR,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QACpE,IAAI2H,KAAK,KAAK,CAAC,IAAI9H,UAAU,CAACc,MAAM,GAAG,CAAC,EAAE;UACxCuE,QAAQ,CAACjE,OAAO,CAACyI,iBAAiB,CAAC7J,UAAU,CAACc,MAAM,EAAER,MAAM,CAACQ,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMgJ,iCAAiC,GAAGA,CAAA,KAAM;IAC9C,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAE/C,MAAM,KAAK;MACtC,MAAMgD,MAAM,GAAGD,MAAM,GAAG/J,cAAc,CAAC+J,MAAM,CAAC,GAAG,EAAE;MACnD,MAAME,MAAM,GAAGjD,MAAM,GAAGhH,cAAc,CAACgH,MAAM,CAAC,GAAG,EAAE;MACnD,OAAOgD,MAAM,KAAKC,MAAM;IAC1B,CAAC;IACD,IAAItE,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI8F,aAAa,CAAC9G,eAAe,IAAI8G,aAAa,CAAC9G,eAAe,CAACU,MAAM,KAAKV,eAAe,CAACU,MAAM,IAAIoG,aAAa,CAAClH,UAAU,KAAKA,UAAU,KAAKuC,QAAQ,GAAGqB,KAAK,CAAC9C,MAAM,KAAKoG,aAAa,CAACtD,KAAK,CAAC9C,MAAM,IAAIoG,aAAa,CAACtD,KAAK,CAACuG,KAAK,CAAC,CAACC,GAAG,EAAEvJ,CAAC,KAAKZ,cAAc,CAAC2D,KAAK,CAAC/C,CAAC,CAAC,CAAC,KAAKZ,cAAc,CAACmK,GAAG,CAAC,CAAC,GAAGL,WAAW,CAAC7C,aAAa,CAACtD,KAAK,EAAEA,KAAK,CAAC,CAAC,EAAE;MACtX,MAAMyG,yBAAyB,GAAGnD,aAAa,CAAC9G,eAAe,CAACwF,mBAAmB,CAACxE,OAAO,CAAC;MAC5F,IAAIiJ,yBAAyB,EAAE;QAC7B,OAAO3J,SAAS,CAACN,eAAe,EAAEE,MAAM,IAAI;UAC1C,OAAOL,cAAc,CAACK,MAAM,CAAC,KAAKL,cAAc,CAACoK,yBAAyB,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD,MAAMC,oBAAoB,GAAG7L,KAAK,CAAC6H,WAAW,CAAC,MAAM;IACnD,IAAI,CAACS,SAAS,EAAE;MACd;IACF;;IAEA;IACA;IACA,MAAMwD,8BAA8B,GAAGT,iCAAiC,CAAC,CAAC;IAC1E,IAAIS,8BAA8B,KAAK,CAAC,CAAC,EAAE;MACzC3E,mBAAmB,CAACxE,OAAO,GAAGmJ,8BAA8B;MAC5D;IACF;IACA,MAAMC,SAAS,GAAGjI,QAAQ,GAAGqB,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;;IAE7C;IACA,IAAIxD,eAAe,CAACU,MAAM,KAAK,CAAC,IAAI0J,SAAS,IAAI,IAAI,EAAE;MACrDnB,sBAAsB,CAAC;QACrBC,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACpI,UAAU,CAACE,OAAO,EAAE;MACvB;IACF;;IAEA;IACA,IAAIoJ,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGrK,eAAe,CAACwF,mBAAmB,CAACxE,OAAO,CAAC;;MAElE;MACA,IAAImB,QAAQ,IAAIkI,aAAa,IAAI/J,SAAS,CAACkD,KAAK,EAAEwG,GAAG,IAAIzG,oBAAoB,CAAC8G,aAAa,EAAEL,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACzG;MACF;MACA,MAAMM,SAAS,GAAGhK,SAAS,CAACN,eAAe,EAAEuK,UAAU,IAAIhH,oBAAoB,CAACgH,UAAU,EAAEH,SAAS,CAAC,CAAC;MACvG,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBrB,sBAAsB,CAAC;UACrBC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLlB,mBAAmB,CAAC;UAClBN,KAAK,EAAE4C;QACT,CAAC,CAAC;MACJ;MACA;IACF;;IAEA;IACA,IAAI9E,mBAAmB,CAACxE,OAAO,IAAIhB,eAAe,CAACU,MAAM,GAAG,CAAC,EAAE;MAC7DsH,mBAAmB,CAAC;QAClBN,KAAK,EAAE1H,eAAe,CAACU,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF;;IAEA;IACAsH,mBAAmB,CAAC;MAClBN,KAAK,EAAElC,mBAAmB,CAACxE;IAC7B,CAAC,CAAC;IACF;IACA;EACF,CAAC,EAAE;EACH;EACAhB,eAAe,CAACU,MAAM;EACtB;EACA;EACAyB,QAAQ,GAAG,KAAK,GAAGqB,KAAK,EAAEb,qBAAqB,EAAEsG,sBAAsB,EAAEjB,mBAAmB,EAAErB,SAAS,EAAE/G,UAAU,EAAEuC,QAAQ,CAAC,CAAC;EAC/H,MAAMqI,gBAAgB,GAAG/L,gBAAgB,CAACgM,IAAI,IAAI;IAChDlM,MAAM,CAACuC,UAAU,EAAE2J,IAAI,CAAC;IACxB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,IAAI7F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAlG,KAAK,CAAC0I,SAAS,CAAC,MAAM;MACpB,IAAI,CAAC9B,QAAQ,CAACjE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAAC0J,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAIzF,QAAQ,CAACjE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAAC0J,QAAQ,KAAK,UAAU,EAAE;UAChEhG,OAAO,CAACyC,IAAI,CAAC,CAAC,sCAAsClF,aAAa,4BAA4B,EAAE,4EAA4E,EAAE,4GAA4G,EAAE,mFAAmF,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7X,CAAC,MAAM;UACL1C,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6DM,QAAQ,CAACjE,OAAO,0CAA0C,EAAE,YAAYiB,aAAa,4BAA4B,EAAE,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,kHAAkH,GAAG,8DAA8D,CAAC,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3a;MACF;IACF,CAAC,EAAE,CAACnF,aAAa,CAAC,CAAC;EACrB;EACA5D,KAAK,CAAC0I,SAAS,CAAC,MAAM;IACpBmD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAC1B,MAAMS,UAAU,GAAGxE,KAAK,IAAI;IAC1B,IAAIrC,IAAI,EAAE;MACR;IACF;IACAyC,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IACtB,IAAI5C,MAAM,EAAE;MACVA,MAAM,CAACsC,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAMyE,WAAW,GAAGA,CAACzE,KAAK,EAAE8B,MAAM,KAAK;IACrC,IAAI,CAACnE,IAAI,EAAE;MACT;IACF;IACAyC,YAAY,CAAC,KAAK,CAAC;IACnB,IAAI7C,OAAO,EAAE;MACXA,OAAO,CAACyC,KAAK,EAAE8B,MAAM,CAAC;IACxB;EACF,CAAC;EACD,MAAM4C,WAAW,GAAGA,CAAC1E,KAAK,EAAEC,QAAQ,EAAE6B,MAAM,EAAE6C,OAAO,KAAK;IACxD,IAAI3I,QAAQ,EAAE;MACZ,IAAIqB,KAAK,CAAC9C,MAAM,KAAK0F,QAAQ,CAAC1F,MAAM,IAAI8C,KAAK,CAACuG,KAAK,CAAC,CAACC,GAAG,EAAEvJ,CAAC,KAAKuJ,GAAG,KAAK5D,QAAQ,CAAC3F,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAI+C,KAAK,KAAK4C,QAAQ,EAAE;MAC7B;IACF;IACA,IAAI3C,QAAQ,EAAE;MACZA,QAAQ,CAAC0C,KAAK,EAAEC,QAAQ,EAAE6B,MAAM,EAAE6C,OAAO,CAAC;IAC5C;IACArF,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EACD,MAAM2E,OAAO,GAAG1M,KAAK,CAAC0G,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMiG,cAAc,GAAGA,CAAC7E,KAAK,EAAEjG,MAAM,EAAE+K,UAAU,GAAG,cAAc,EAAEC,MAAM,GAAG,SAAS,KAAK;IACzF,IAAIjD,MAAM,GAAGgD,UAAU;IACvB,IAAI7E,QAAQ,GAAGlG,MAAM;IACrB,IAAIiC,QAAQ,EAAE;MACZiE,QAAQ,GAAG+E,KAAK,CAACC,OAAO,CAAC5H,KAAK,CAAC,GAAGA,KAAK,CAACnD,KAAK,CAAC,CAAC,GAAG,EAAE;MACpD,IAAIgE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAM8G,OAAO,GAAGjF,QAAQ,CAACnG,MAAM,CAAC+J,GAAG,IAAIzG,oBAAoB,CAACrD,MAAM,EAAE8J,GAAG,CAAC,CAAC;QACzE,IAAIqB,OAAO,CAAC3K,MAAM,GAAG,CAAC,EAAE;UACtBgE,OAAO,CAACC,KAAK,CAAC,CAAC,+CAA+C1C,aAAa,2CAA2C,EAAE,0EAA0EoJ,OAAO,CAAC3K,MAAM,WAAW,CAAC,CAAC0G,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1O;MACF;MACA,MAAMkD,SAAS,GAAGhK,SAAS,CAAC8F,QAAQ,EAAEgE,SAAS,IAAI7G,oBAAoB,CAACrD,MAAM,EAAEkK,SAAS,CAAC,CAAC;MAC3F,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBlE,QAAQ,CAACkF,IAAI,CAACpL,MAAM,CAAC;MACvB,CAAC,MAAM,IAAIgL,MAAM,KAAK,UAAU,EAAE;QAChC9E,QAAQ,CAACmF,MAAM,CAACjB,SAAS,EAAE,CAAC,CAAC;QAC7BrC,MAAM,GAAG,cAAc;MACzB;IACF;IACAhC,eAAe,CAACE,KAAK,EAAEC,QAAQ,CAAC;IAChCyE,WAAW,CAAC1E,KAAK,EAAEC,QAAQ,EAAE6B,MAAM,EAAE;MACnC/H;IACF,CAAC,CAAC;IACF,IAAI,CAACmC,oBAAoB,KAAK,CAAC8D,KAAK,IAAI,CAACA,KAAK,CAACqF,OAAO,IAAI,CAACrF,KAAK,CAACsF,OAAO,CAAC,EAAE;MACzEb,WAAW,CAACzE,KAAK,EAAE8B,MAAM,CAAC;IAC5B;IACA,IAAIpG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAIkJ,OAAO,CAAC/J,OAAO,IAAIa,YAAY,KAAK,OAAO,IAAI,CAACkJ,OAAO,CAAC/J,OAAO,EAAE;MACxHiE,QAAQ,CAACjE,OAAO,CAAC0K,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EACD,SAASC,aAAaA,CAACjE,KAAK,EAAEC,SAAS,EAAE;IACvC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIE,SAAS,GAAGF,KAAK;IACrB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKpE,KAAK,CAAC9C,MAAM,IAAIiH,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;MACA,MAAM1H,MAAM,GAAGgF,QAAQ,CAACsC,aAAa,CAAC,oBAAoBI,SAAS,IAAI,CAAC;;MAExE;MACA,IAAI,CAAC1H,MAAM,IAAI,CAACA,MAAM,CAAC6H,YAAY,CAAC,UAAU,CAAC,IAAI7H,MAAM,CAACoC,QAAQ,IAAIpC,MAAM,CAAC4H,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EACA,MAAMgE,cAAc,GAAGA,CAACzF,KAAK,EAAEwB,SAAS,KAAK;IAC3C,IAAI,CAACxF,QAAQ,EAAE;MACb;IACF;IACA,IAAIvC,UAAU,KAAK,EAAE,EAAE;MACrBgL,WAAW,CAACzE,KAAK,EAAE,aAAa,CAAC;IACnC;IACA,IAAI0F,OAAO,GAAGxG,UAAU;IACxB,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAIzF,UAAU,KAAK,EAAE,IAAI+H,SAAS,KAAK,UAAU,EAAE;QACjDkE,OAAO,GAAGrI,KAAK,CAAC9C,MAAM,GAAG,CAAC;MAC5B;IACF,CAAC,MAAM;MACLmL,OAAO,IAAIlE,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACxC,IAAIkE,OAAO,GAAG,CAAC,EAAE;QACfA,OAAO,GAAG,CAAC;MACb;MACA,IAAIA,OAAO,KAAKrI,KAAK,CAAC9C,MAAM,EAAE;QAC5BmL,OAAO,GAAG,CAAC,CAAC;MACd;IACF;IACAA,OAAO,GAAGF,aAAa,CAACE,OAAO,EAAElE,SAAS,CAAC;IAC3CrC,aAAa,CAACuG,OAAO,CAAC;IACtBxE,QAAQ,CAACwE,OAAO,CAAC;EACnB,CAAC;EACD,MAAMC,WAAW,GAAG3F,KAAK,IAAI;IAC3BrB,WAAW,CAAC9D,OAAO,GAAG,IAAI;IAC1B6E,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAIjC,aAAa,EAAE;MACjBA,aAAa,CAACuC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IACA0E,WAAW,CAAC1E,KAAK,EAAEhE,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EACD,MAAM4J,aAAa,GAAGC,KAAK,IAAI7F,KAAK,IAAI;IACtC,IAAI6F,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAAC9F,KAAK,CAAC;IACxB;IACA,IAAIA,KAAK,CAAC+F,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAI7G,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACjF,OAAO,CAAC+F,KAAK,CAACgG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9E7G,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA;IACA,IAAIlB,KAAK,CAACiG,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQjG,KAAK,CAACgG,GAAG;QACf,KAAK,MAAM;UACT,IAAIxF,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAiD,KAAK,CAACkG,cAAc,CAAC,CAAC;YACtBpD,sBAAsB,CAAC;cACrBC,IAAI,EAAE,OAAO;cACbvB,SAAS,EAAE,MAAM;cACjBM,MAAM,EAAE,UAAU;cAClB9B;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,KAAK;UACR,IAAIQ,SAAS,IAAIzD,iBAAiB,EAAE;YAClC;YACAiD,KAAK,CAACkG,cAAc,CAAC,CAAC;YACtBpD,sBAAsB,CAAC;cACrBC,IAAI,EAAE,KAAK;cACXvB,SAAS,EAAE,UAAU;cACrBM,MAAM,EAAE,UAAU;cAClB9B;YACF,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACAA,KAAK,CAACkG,cAAc,CAAC,CAAC;UACtBpD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAACtI,QAAQ;YACf+G,SAAS,EAAE,UAAU;YACrBM,MAAM,EAAE,UAAU;YAClB9B;UACF,CAAC,CAAC;UACFwE,UAAU,CAACxE,KAAK,CAAC;UACjB;QACF,KAAK,UAAU;UACb;UACAA,KAAK,CAACkG,cAAc,CAAC,CAAC;UACtBpD,sBAAsB,CAAC;YACrBC,IAAI,EAAEtI,QAAQ;YACd+G,SAAS,EAAE,MAAM;YACjBM,MAAM,EAAE,UAAU;YAClB9B;UACF,CAAC,CAAC;UACFwE,UAAU,CAACxE,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACd;UACAA,KAAK,CAACkG,cAAc,CAAC,CAAC;UACtBpD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC;YACPvB,SAAS,EAAE,MAAM;YACjBM,MAAM,EAAE,UAAU;YAClB9B;UACF,CAAC,CAAC;UACFwE,UAAU,CAACxE,KAAK,CAAC;UACjB;QACF,KAAK,SAAS;UACZ;UACAA,KAAK,CAACkG,cAAc,CAAC,CAAC;UACtBpD,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC,CAAC;YACRvB,SAAS,EAAE,UAAU;YACrBM,MAAM,EAAE,UAAU;YAClB9B;UACF,CAAC,CAAC;UACFwE,UAAU,CAACxE,KAAK,CAAC;UACjB;QACF,KAAK,WAAW;UACdyF,cAAc,CAACzF,KAAK,EAAE,UAAU,CAAC;UACjC;QACF,KAAK,YAAY;UACfyF,cAAc,CAACzF,KAAK,EAAE,MAAM,CAAC;UAC7B;QACF,KAAK,OAAO;UACV,IAAIX,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI2F,SAAS,EAAE;YACnD,MAAMzG,MAAM,GAAGF,eAAe,CAACwF,mBAAmB,CAACxE,OAAO,CAAC;YAC3D,MAAMsB,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC1C,MAAM,CAAC,GAAG,KAAK;;YAEtE;YACAiG,KAAK,CAACkG,cAAc,CAAC,CAAC;YACtB,IAAI/J,QAAQ,EAAE;cACZ;YACF;YACA0I,cAAc,CAAC7E,KAAK,EAAEjG,MAAM,EAAE,cAAc,CAAC;;YAE7C;YACA,IAAIwB,YAAY,EAAE;cAChBuD,QAAQ,CAACjE,OAAO,CAACyI,iBAAiB,CAACxE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,CAAC9C,MAAM,EAAEuE,QAAQ,CAACjE,OAAO,CAACwC,KAAK,CAAC9C,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAIqB,QAAQ,IAAInC,UAAU,KAAK,EAAE,IAAI8G,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAIvE,QAAQ,EAAE;cACZ;cACAgE,KAAK,CAACkG,cAAc,CAAC,CAAC;YACxB;YACArB,cAAc,CAAC7E,KAAK,EAAEvG,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UACA;QACF,KAAK,QAAQ;UACX,IAAI+G,SAAS,EAAE;YACb;YACAR,KAAK,CAACkG,cAAc,CAAC,CAAC;YACtB;YACAlG,KAAK,CAACmG,eAAe,CAAC,CAAC;YACvB1B,WAAW,CAACzE,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAInE,aAAa,KAAKpC,UAAU,KAAK,EAAE,IAAIuC,QAAQ,IAAIqB,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC,EAAE;YAC/E;YACAyF,KAAK,CAACkG,cAAc,CAAC,CAAC;YACtB;YACAlG,KAAK,CAACmG,eAAe,CAAC,CAAC;YACvBR,WAAW,CAAC3F,KAAK,CAAC;UACpB;UACA;QACF,KAAK,WAAW;UACd;UACA,IAAIhE,QAAQ,IAAI,CAAC8B,QAAQ,IAAIrE,UAAU,KAAK,EAAE,IAAI4D,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMgH,KAAK,GAAGrC,UAAU,KAAK,CAAC,CAAC,GAAG7B,KAAK,CAAC9C,MAAM,GAAG,CAAC,GAAG2E,UAAU;YAC/D,MAAMe,QAAQ,GAAG5C,KAAK,CAACnD,KAAK,CAAC,CAAC;YAC9B+F,QAAQ,CAACmF,MAAM,CAAC7D,KAAK,EAAE,CAAC,CAAC;YACzBmD,WAAW,CAAC1E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3ClG,MAAM,EAAEsD,KAAK,CAACkE,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAIvF,QAAQ,IAAI,CAAC8B,QAAQ,IAAIrE,UAAU,KAAK,EAAE,IAAI4D,KAAK,CAAC9C,MAAM,GAAG,CAAC,IAAI2E,UAAU,KAAK,CAAC,CAAC,EAAE;YACvF,MAAMqC,KAAK,GAAGrC,UAAU;YACxB,MAAMe,QAAQ,GAAG5C,KAAK,CAACnD,KAAK,CAAC,CAAC;YAC9B+F,QAAQ,CAACmF,MAAM,CAAC7D,KAAK,EAAE,CAAC,CAAC;YACzBmD,WAAW,CAAC1E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3ClG,MAAM,EAAEsD,KAAK,CAACkE,KAAK;YACrB,CAAC,CAAC;UACJ;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,MAAM6E,WAAW,GAAGpG,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIhC,WAAW,IAAI,CAACc,WAAW,CAAC9D,OAAO,EAAE;MACvC2J,UAAU,CAACxE,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMqG,UAAU,GAAGrG,KAAK,IAAI;IAC1B;IACA,IAAI3E,iCAAiC,CAACV,UAAU,CAAC,EAAE;MACjDmE,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;MACxB;IACF;IACAvB,UAAU,CAAC,KAAK,CAAC;IACjBhB,UAAU,CAAChE,OAAO,GAAG,IAAI;IACzB8D,WAAW,CAAC9D,OAAO,GAAG,KAAK;IAC3B,IAAIY,UAAU,IAAI4D,mBAAmB,CAACxE,OAAO,KAAK,CAAC,CAAC,IAAI2F,SAAS,EAAE;MACjEqE,cAAc,CAAC7E,KAAK,EAAEnG,eAAe,CAACwF,mBAAmB,CAACxE,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAIY,UAAU,IAAIG,QAAQ,IAAInC,UAAU,KAAK,EAAE,EAAE;MACtDoL,cAAc,CAAC7E,KAAK,EAAEvG,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAIkC,WAAW,EAAE;MACtBmE,eAAe,CAACE,KAAK,EAAE3C,KAAK,CAAC;IAC/B;IACAoH,WAAW,CAACzE,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EACD,MAAMsG,iBAAiB,GAAGtG,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACuG,MAAM,CAAClJ,KAAK;IACnC,IAAI5D,UAAU,KAAKwG,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BK,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAI7C,aAAa,EAAE;QACjBA,aAAa,CAACuC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAChE,gBAAgB,IAAI,CAACD,QAAQ,EAAE;QAClC0I,WAAW,CAAC1E,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACLwE,UAAU,CAACxE,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMwG,qBAAqB,GAAGxG,KAAK,IAAI;IACrC,MAAMuB,KAAK,GAAGkF,MAAM,CAACzG,KAAK,CAAC0G,aAAa,CAAC/E,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E,IAAItC,mBAAmB,CAACxE,OAAO,KAAK0G,KAAK,EAAE;MACzCM,mBAAmB,CAAC;QAClB7B,KAAK;QACLuB,KAAK;QACLO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM6E,sBAAsB,GAAG3G,KAAK,IAAI;IACtC6B,mBAAmB,CAAC;MAClB7B,KAAK;MACLuB,KAAK,EAAEkF,MAAM,CAACzG,KAAK,CAAC0G,aAAa,CAAC/E,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpEG,MAAM,EAAE;IACV,CAAC,CAAC;IACF8C,OAAO,CAAC/J,OAAO,GAAG,IAAI;EACxB,CAAC;EACD,MAAM+L,iBAAiB,GAAG5G,KAAK,IAAI;IACjC,MAAMuB,KAAK,GAAGkF,MAAM,CAACzG,KAAK,CAAC0G,aAAa,CAAC/E,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3EkD,cAAc,CAAC7E,KAAK,EAAEnG,eAAe,CAAC0H,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7DqD,OAAO,CAAC/J,OAAO,GAAG,KAAK;EACzB,CAAC;EACD,MAAMgM,eAAe,GAAGtF,KAAK,IAAIvB,KAAK,IAAI;IACxC,MAAMC,QAAQ,GAAG5C,KAAK,CAACnD,KAAK,CAAC,CAAC;IAC9B+F,QAAQ,CAACmF,MAAM,CAAC7D,KAAK,EAAE,CAAC,CAAC;IACzBmD,WAAW,CAAC1E,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3ClG,MAAM,EAAEsD,KAAK,CAACkE,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMuF,oBAAoB,GAAG9G,KAAK,IAAI;IACpC,IAAIrC,IAAI,EAAE;MACR8G,WAAW,CAACzE,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACLwE,UAAU,CAACxE,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+G,eAAe,GAAG/G,KAAK,IAAI;IAC/B;IACA,IAAI,CAACA,KAAK,CAAC0G,aAAa,CAAC3L,QAAQ,CAACiF,KAAK,CAACuG,MAAM,CAAC,EAAE;MAC/C;IACF;IACA,IAAIvG,KAAK,CAACuG,MAAM,CAAC5E,YAAY,CAAC,IAAI,CAAC,KAAK3E,EAAE,EAAE;MAC1CgD,KAAK,CAACkG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMc,WAAW,GAAGhH,KAAK,IAAI;IAC3B;IACA,IAAI,CAACA,KAAK,CAAC0G,aAAa,CAAC3L,QAAQ,CAACiF,KAAK,CAACuG,MAAM,CAAC,EAAE;MAC/C;IACF;IACAzH,QAAQ,CAACjE,OAAO,CAACuG,KAAK,CAAC,CAAC;IACxB,IAAIrD,aAAa,IAAIc,UAAU,CAAChE,OAAO,IAAIiE,QAAQ,CAACjE,OAAO,CAACoM,YAAY,GAAGnI,QAAQ,CAACjE,OAAO,CAACqM,cAAc,KAAK,CAAC,EAAE;MAChHpI,QAAQ,CAACjE,OAAO,CAACsM,MAAM,CAAC,CAAC;IAC3B;IACAtI,UAAU,CAAChE,OAAO,GAAG,KAAK;EAC5B,CAAC;EACD,MAAMuM,oBAAoB,GAAGpH,KAAK,IAAI;IACpC,IAAI,CAAC5D,YAAY,KAAK3C,UAAU,KAAK,EAAE,IAAI,CAACkE,IAAI,CAAC,EAAE;MACjDmJ,oBAAoB,CAAC9G,KAAK,CAAC;IAC7B;EACF,CAAC;EACD,IAAIqH,KAAK,GAAGzL,QAAQ,IAAInC,UAAU,CAACc,MAAM,GAAG,CAAC;EAC7C8M,KAAK,GAAGA,KAAK,KAAKrL,QAAQ,GAAGqB,KAAK,CAAC9C,MAAM,GAAG,CAAC,GAAG8C,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAIiK,cAAc,GAAGzN,eAAe;EACpC,IAAIiD,OAAO,EAAE;IACX;IACA,MAAMyK,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAIxG,IAAI,GAAG,KAAK;IAChBsG,cAAc,GAAGzN,eAAe,CAAC4N,MAAM,CAAC,CAACC,GAAG,EAAE3N,MAAM,EAAEwH,KAAK,KAAK;MAC9D,MAAMoG,KAAK,GAAG7K,OAAO,CAAC/C,MAAM,CAAC;MAC7B,IAAI2N,GAAG,CAACnN,MAAM,GAAG,CAAC,IAAImN,GAAG,CAACA,GAAG,CAACnN,MAAM,GAAG,CAAC,CAAC,CAACoN,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAACnN,MAAM,GAAG,CAAC,CAAC,CAACf,OAAO,CAAC2L,IAAI,CAACpL,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAImE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAImJ,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAAC3G,IAAI,EAAE;YAC/BzC,OAAO,CAACyC,IAAI,CAAC,qEAAqElF,aAAa,8BAA8B,EAAE,8EAA8E,CAAC;YAC9MkF,IAAI,GAAG,IAAI;UACb;UACAuG,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QACAD,GAAG,CAACvC,IAAI,CAAC;UACPa,GAAG,EAAEzE,KAAK;UACVA,KAAK;UACLoG,KAAK;UACLnO,OAAO,EAAE,CAACO,MAAM;QAClB,CAAC,CAAC;MACJ;MACA,OAAO2N,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,IAAItL,YAAY,IAAIwD,OAAO,EAAE;IAC3ByG,UAAU,CAAC,CAAC;EACd;EACA,OAAO;IACLyB,YAAY,EAAEA,CAACjC,KAAK,GAAG,CAAC,CAAC,KAAK5N,QAAQ,CAAC;MACrC,WAAW,EAAE6I,gBAAgB,GAAG,GAAG9D,EAAE,UAAU,GAAG;IACpD,CAAC,EAAE6I,KAAK,EAAE;MACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;MAC/BkC,WAAW,EAAEhB,eAAe;MAC5BiB,OAAO,EAAEhB;IACX,CAAC,CAAC;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBjL,EAAE,EAAE,GAAGA,EAAE,QAAQ;MACjBkL,OAAO,EAAElL;IACX,CAAC,CAAC;IACFmL,aAAa,EAAEA,CAAA,MAAO;MACpBnL,EAAE;MACFK,KAAK,EAAE5D,UAAU;MACjB2O,MAAM,EAAE/B,UAAU;MAClBgC,OAAO,EAAEjC,WAAW;MACpB9I,QAAQ,EAAEgJ,iBAAiB;MAC3ByB,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAE5G,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAEjF,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEuF,gBAAgB,GAAG,GAAG9D,EAAE,UAAU,GAAGsB,SAAS;MAC/D,eAAe,EAAEwC,gBAAgB;MACjC;MACA;MACAvF,YAAY,EAAE,KAAK;MACnB+M,GAAG,EAAExJ,QAAQ;MACbyJ,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE,UAAU;MAChBtM,QAAQ,EAAEC;IACZ,CAAC,CAAC;IACFsM,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAErC;IACX,CAAC,CAAC;IACFkD,sBAAsB,EAAEA,CAAA,MAAO;MAC7BF,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAElB;IACX,CAAC,CAAC;IACFgC,WAAW,EAAEA,CAAC;MACZvH;IACF,CAAC,KAAKtJ,QAAQ,CAAC;MACb+N,GAAG,EAAEzE,KAAK;MACV,gBAAgB,EAAEA,KAAK;MACvBoH,QAAQ,EAAE,CAAC;IACb,CAAC,EAAE,CAAC7K,QAAQ,IAAI;MACdiL,QAAQ,EAAElC,eAAe,CAACtF,KAAK;IACjC,CAAC,CAAC;IACFyH,eAAe,EAAEA,CAAA,MAAO;MACtBP,IAAI,EAAE,SAAS;MACfzL,EAAE,EAAE,GAAGA,EAAE,UAAU;MACnB,iBAAiB,EAAE,GAAGA,EAAE,QAAQ;MAChCsL,GAAG,EAAEjE,gBAAgB;MACrB0D,WAAW,EAAE/H,KAAK,IAAI;QACpB;QACAA,KAAK,CAACkG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACF+C,cAAc,EAAEA,CAAC;MACf1H,KAAK;MACLxH;IACF,CAAC,KAAK;MACJ,IAAImP,aAAa;MACjB,MAAMC,QAAQ,GAAG,CAACnN,QAAQ,GAAGqB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEoD,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAItD,oBAAoB,CAACrD,MAAM,EAAE2G,MAAM,CAAC,CAAC;MACpH,MAAMvE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC1C,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLiM,GAAG,EAAE,CAACkD,aAAa,GAAGxM,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC3C,MAAM,CAAC,KAAK,IAAI,GAAGmP,aAAa,GAAGxP,cAAc,CAACK,MAAM,CAAC;QAC5H4O,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACdzL,EAAE,EAAE,GAAGA,EAAE,WAAWuE,KAAK,EAAE;QAC3B6H,WAAW,EAAE5C,qBAAqB;QAClCwB,OAAO,EAAEpB,iBAAiB;QAC1ByC,YAAY,EAAE1C,sBAAsB;QACpC,mBAAmB,EAAEpF,KAAK;QAC1B,eAAe,EAAEpF,QAAQ;QACzB,eAAe,EAAEgN;MACnB,CAAC;IACH,CAAC;IACDnM,EAAE;IACFvD,UAAU;IACV4D,KAAK;IACLgK,KAAK;IACLiC,QAAQ,EAAE9I,SAAS,IAAIzB,QAAQ;IAC/ByB,SAAS;IACTZ,OAAO,EAAEA,OAAO,IAAIV,UAAU,KAAK,CAAC,CAAC;IACrCH,QAAQ;IACRC,WAAW;IACXE,UAAU;IACVoI;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}