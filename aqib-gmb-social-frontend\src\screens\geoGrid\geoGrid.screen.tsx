import React, { useState, useEffect, useCallback, useContext } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Alert,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import GeoGridControls from "../../components/geoGrid/GeoGridControls.component";
import GeoGridMap from "../../components/geoGrid/GeoGridMap.component";
import GeoGridSettings from "../../components/geoGrid/GeoGridSettings.component";
import GeoGridService, {
  GridConfiguration,
  GridPoint,
  LocationSearchRequest,
} from "../../services/geoGrid/geoGrid.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

interface GeoGridScreenProps {
  title: string;
}

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  address: string;
  placeId: string;
}

const GeoGridScreen: React.FC<GeoGridScreenProps> = ({ title }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [geoGridService] = useState(new GeoGridService(dispatch));
  const [loading, setLoading] = useState(false);

  // Toast context for notifications
  const { setToastConfig } = useContext(ToastContext);

  // Grid state
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [gridPoints, setGridPoints] = useState<GridPoint[]>([]);
  const [gridConfiguration, setGridConfiguration] = useState<GridConfiguration>(
    {
      name: "",
      centerLat: 0,
      centerLng: 0,
      gridSize: "3x3",
      distance: 500,
      distanceUnit: "meters",
      searchType: "name",
      searchQuery: "",
      isScheduleEnabled: false,
      settings: {},
    }
  );

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [savedConfigurations, setSavedConfigurations] = useState<
    GridConfiguration[]
  >([]);

  const user = useSelector((state: any) => state.authReducer?.userInfo);

  useEffect(() => {
    document.title = title;
    if (user?.id) {
      loadSavedConfigurations();
    }
  }, [title, user]);

  const loadSavedConfigurations = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await geoGridService.getGridConfigurations(user.id);
      if (response.success) {
        setSavedConfigurations(response.data);
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to load saved configurations",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLocationSearch = async (searchRequest: LocationSearchRequest) => {
    try {
      setLoading(true);

      const response = await geoGridService.searchLocation(searchRequest);

      if (response.success) {
        const locationData = response.data;
        setCurrentLocation(locationData);
        setGridConfiguration((prev) => ({
          ...prev,
          centerLat: locationData.lat,
          centerLng: locationData.lng,
          searchType: searchRequest.searchType,
          searchQuery: searchRequest.query || "",
          name: "", // Clear the name for new searches to avoid overwriting existing configs
        }));

        // Auto-generate grid when location is found
        await generateGrid(locationData.lat, locationData.lng);
        setToastConfig(
          ToastSeverity.Success,
          "Location found and grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to search location",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const generateGrid = async (centerLat?: number, centerLng?: number) => {
    try {
      setLoading(true);

      const lat = centerLat || gridConfiguration.centerLat;
      const lng = centerLng || gridConfiguration.centerLng;

      if (!lat || !lng) {
        throw new Error("Center coordinates are required");
      }

      const response = await geoGridService.generateGrid({
        centerLat: lat,
        centerLng: lng,
        gridSize: gridConfiguration.gridSize,
        distance: gridConfiguration.distance,
        distanceUnit: gridConfiguration.distanceUnit,
      });

      if (response.success) {
        setGridPoints(response.data.gridPoints);
        setToastConfig(
          ToastSeverity.Success,
          "Grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to generate grid",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfigurationChange = (updates: Partial<GridConfiguration>) => {
    setGridConfiguration((prev) => ({ ...prev, ...updates }));
  };

  const handleConfigurationNameChange = (name: string) => {
    setGridConfiguration((prev) => ({ ...prev, name }));
  };

  const handleSaveConfiguration = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        throw new Error("Please log in to save configurations");
      }

      if (!gridConfiguration.name.trim()) {
        throw new Error("Configuration name is required");
      }

      const configToSave = {
        ...gridConfiguration,
        userId: user.id,
        gridPoints,
      };

      const response = await geoGridService.saveGridConfiguration(configToSave);

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration saved successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to save configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLoadConfiguration = async (config: GridConfiguration) => {
    try {
      setLoading(true);

      if (config.id) {
        const response = await geoGridService.getGridData(config.id.toString());
        if (response.success) {
          const { configuration, gridPoints: loadedPoints } = response.data;
          setGridConfiguration(configuration);
          setGridPoints(loadedPoints || []);
          setCurrentLocation({
            name: configuration.searchQuery || "Loaded Location",
            lat: configuration.centerLat,
            lng: configuration.centerLng,
            address: "",
            placeId: "",
          });
          setToastConfig(
            ToastSeverity.Success,
            "Configuration loaded successfully!",
            true
          );
        }
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to load configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfiguration = async (configId: number) => {
    try {
      setLoading(true);

      const response = await geoGridService.deleteGridConfiguration(
        configId.toString()
      );

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration deleted successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to delete configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Show login message if user is not authenticated
  if (!user?.id) {
    return (
      <LeftMenuComponent>
        <Box>
          <Box sx={{ marginBottom: "5px" }}>
            <h3 className="pageTitle">Google Geo Grid</h3>
            <Typography variant="subtitle2" className="subtitle2">
              Create and manage location-based grids for your business analysis
            </Typography>
          </Box>

          <Alert
            severity="warning"
            sx={{ mt: 2 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => navigate("/")}
                variant="outlined"
              >
                Login
              </Button>
            }
          >
            Please log in to access the Google Geo Grid functionality. You can
            still explore the basic features, but saving configurations requires
            authentication.
          </Alert>

          {/* Show limited functionality for non-authenticated users */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: "fit-content" }}>
                <GeoGridControls
                  onLocationSearch={handleLocationSearch}
                  onGenerateGrid={generateGrid}
                  onSaveConfiguration={() =>
                    setToastConfig(
                      ToastSeverity.Error,
                      "Please log in to save configurations",
                      true
                    )
                  }
                  loading={loading}
                  currentLocation={currentLocation}
                  savedConfigurations={[]}
                  onLoadConfiguration={() => {}}
                  onDeleteConfiguration={() => {}}
                  configurationName={gridConfiguration.name}
                  onConfigurationNameChange={handleConfigurationNameChange}
                />
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, height: 600 }}>
                <GeoGridMap
                  center={
                    currentLocation
                      ? { lat: currentLocation.lat, lng: currentLocation.lng }
                      : null
                  }
                  gridPoints={gridPoints}
                  loading={loading}
                />
              </Paper>
            </Grid>
          </Grid>
        </Box>
      </LeftMenuComponent>
    );
  }

  return (
    <LeftMenuComponent>
      <Box>
        <Box sx={{ marginBottom: "5px" }}>
          <h3 className="pageTitle">Google Geo Grid</h3>
          <Typography variant="subtitle2" className="subtitle2">
            Create and manage location-based grids for your business analysis
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Controls Panel */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 2, height: "fit-content" }}>
              <GeoGridControls
                onLocationSearch={handleLocationSearch}
                onGenerateGrid={generateGrid}
                onSaveConfiguration={handleSaveConfiguration}
                loading={loading}
                currentLocation={currentLocation}
                savedConfigurations={savedConfigurations}
                onLoadConfiguration={handleLoadConfiguration}
                onDeleteConfiguration={handleDeleteConfiguration}
                configurationName={gridConfiguration.name}
                onConfigurationNameChange={handleConfigurationNameChange}
              />
            </Paper>
          </Grid>

          {/* Map Panel */}
          <Grid item xs={12} md={5}>
            <Paper sx={{ p: 2, height: 600 }}>
              <GeoGridMap
                center={
                  currentLocation
                    ? { lat: currentLocation.lat, lng: currentLocation.lng }
                    : null
                }
                gridPoints={gridPoints}
                loading={loading}
              />
            </Paper>
          </Grid>

          {/* Settings Panel */}
          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, height: "fit-content" }}>
              <GeoGridSettings
                configuration={gridConfiguration}
                onConfigurationChange={handleConfigurationChange}
                onGenerateGrid={generateGrid}
                loading={loading}
              />
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </LeftMenuComponent>
  );
};

export default GeoGridScreen;
