import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Alert,
  Snackbar,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import GeoGridControls from "../../components/geoGrid/GeoGridControls.component";
import GeoGridMap from "../../components/geoGrid/GeoGridMap.component";
import GeoGridSettings from "../../components/geoGrid/GeoGridSettings.component";
import GeoGridService, {
  GridConfiguration,
  GridPoint,
  LocationSearchRequest,
} from "../../services/geoGrid/geoGrid.service";

interface GeoGridScreenProps {
  title: string;
}

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  address: string;
  placeId: string;
}

const GeoGridScreen: React.FC<GeoGridScreenProps> = ({ title }) => {
  const dispatch = useDispatch();
  const [geoGridService] = useState(new GeoGridService(dispatch));
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Grid state
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [gridPoints, setGridPoints] = useState<GridPoint[]>([]);
  const [gridConfiguration, setGridConfiguration] = useState<GridConfiguration>(
    {
      name: "",
      centerLat: 0,
      centerLng: 0,
      gridSize: "3x3",
      distance: 500,
      distanceUnit: "meters",
      searchType: "name",
      searchQuery: "",
      isScheduleEnabled: false,
      settings: {},
    }
  );

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [savedConfigurations, setSavedConfigurations] = useState<
    GridConfiguration[]
  >([]);

  const user = useSelector((state: any) => state.auth.user);

  useEffect(() => {
    document.title = title;
    loadSavedConfigurations();
  }, [title]);

  const loadSavedConfigurations = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await geoGridService.getGridConfigurations(user.id);
      if (response.success) {
        setSavedConfigurations(response.data);
      }
    } catch (error: any) {
      setError("Failed to load saved configurations");
    } finally {
      setLoading(false);
    }
  };

  const handleLocationSearch = async (searchRequest: LocationSearchRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await geoGridService.searchLocation(searchRequest);

      if (response.success) {
        const locationData = response.data;
        setCurrentLocation(locationData);
        setGridConfiguration((prev) => ({
          ...prev,
          centerLat: locationData.lat,
          centerLng: locationData.lng,
          searchType: searchRequest.searchType,
          searchQuery: searchRequest.query || "",
        }));

        // Auto-generate grid when location is found
        await generateGrid(locationData.lat, locationData.lng);
        setSuccess("Location found and grid generated successfully!");
      }
    } catch (error: any) {
      setError(error.message || "Failed to search location");
    } finally {
      setLoading(false);
    }
  };

  const generateGrid = async (centerLat?: number, centerLng?: number) => {
    try {
      setLoading(true);
      setError(null);

      const lat = centerLat || gridConfiguration.centerLat;
      const lng = centerLng || gridConfiguration.centerLng;

      if (!lat || !lng) {
        throw new Error("Center coordinates are required");
      }

      const response = await geoGridService.generateGrid({
        centerLat: lat,
        centerLng: lng,
        gridSize: gridConfiguration.gridSize,
        distance: gridConfiguration.distance,
        distanceUnit: gridConfiguration.distanceUnit,
      });

      if (response.success) {
        setGridPoints(response.data.gridPoints);
        setSuccess("Grid generated successfully!");
      }
    } catch (error: any) {
      setError(error.message || "Failed to generate grid");
    } finally {
      setLoading(false);
    }
  };

  const handleConfigurationChange = (updates: Partial<GridConfiguration>) => {
    setGridConfiguration((prev) => ({ ...prev, ...updates }));
  };

  const handleSaveConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!gridConfiguration.name.trim()) {
        throw new Error("Configuration name is required");
      }

      const configToSave = {
        ...gridConfiguration,
        userId: user.id,
        gridPoints,
      };

      const response = await geoGridService.saveGridConfiguration(configToSave);

      if (response.success) {
        setSuccess("Configuration saved successfully!");
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setError(error.message || "Failed to save configuration");
    } finally {
      setLoading(false);
    }
  };

  const handleLoadConfiguration = async (config: GridConfiguration) => {
    try {
      setLoading(true);
      setError(null);

      if (config.id) {
        const response = await geoGridService.getGridData(config.id.toString());
        if (response.success) {
          const { configuration, gridPoints: loadedPoints } = response.data;
          setGridConfiguration(configuration);
          setGridPoints(loadedPoints || []);
          setCurrentLocation({
            name: configuration.searchQuery || "Loaded Location",
            lat: configuration.centerLat,
            lng: configuration.centerLng,
            address: "",
            placeId: "",
          });
          setSuccess("Configuration loaded successfully!");
        }
      }
    } catch (error: any) {
      setError(error.message || "Failed to load configuration");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfiguration = async (configId: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await geoGridService.deleteGridConfiguration(
        configId.toString()
      );

      if (response.success) {
        setSuccess("Configuration deleted successfully!");
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setError(error.message || "Failed to delete configuration");
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Google Geo Grid
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Create and manage location-based grids for your business analysis
      </Typography>

      <Grid container spacing={3}>
        {/* Controls Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: "fit-content" }}>
            <GeoGridControls
              onLocationSearch={handleLocationSearch}
              onGenerateGrid={generateGrid}
              onSaveConfiguration={handleSaveConfiguration}
              loading={loading}
              currentLocation={currentLocation}
              savedConfigurations={savedConfigurations}
              onLoadConfiguration={handleLoadConfiguration}
              onDeleteConfiguration={handleDeleteConfiguration}
            />
          </Paper>
        </Grid>

        {/* Map Panel */}
        <Grid item xs={12} md={5}>
          <Paper sx={{ p: 2, height: 600 }}>
            <GeoGridMap
              center={
                currentLocation
                  ? { lat: currentLocation.lat, lng: currentLocation.lng }
                  : null
              }
              gridPoints={gridPoints}
              loading={loading}
            />
          </Paper>
        </Grid>

        {/* Settings Panel */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, height: "fit-content" }}>
            <GeoGridSettings
              configuration={gridConfiguration}
              onConfigurationChange={handleConfigurationChange}
              onGenerateGrid={generateGrid}
              loading={loading}
            />
          </Paper>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity="error"
          sx={{ width: "100%" }}
        >
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity="success"
          sx={{ width: "100%" }}
        >
          {success}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default GeoGridScreen;
