import { IPaginationResponseModel } from "../IPaginationResponseModel";

export interface IUsersListResponse {
  message: string;
  list: IUser[];
}

export interface IUser {
  id: number;
  roleId: number;
  name: string;
  mobile: string;
  email: string;
  mobileVerified: any;
  emailVerified: any;
  password: string;
  statusId: number;
  createdAt: any;
  updatedAt: string;
  role: string;
  isActive: number;
}

export interface IUsersListPaginatedResponse {
  message: string;
  pagination: IPaginationResponseModel;
  results: IUser[];
}
