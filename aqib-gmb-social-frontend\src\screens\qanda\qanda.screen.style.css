:root {
    --postPreviewBgColor: #5D5DFF
}

.reviewCard {
    flex-direction: column;
}

.reviewContent {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    height: 48px;
}

.reviewContentDefault {
    height: 48px;
    font-weight: 600;
    font-style: italic;
    color: #b5b5b5;
}

.postPreviewEdit {
    height: 100%;
}

.postPreviewEdit .MuiTabs-root {
    height: 48px;
}

.postPreviewEdit .MuiTabs-root+.MuiBox-root {
    height: calc(100% - 142px);
    overflow-y: auto;
}
.qandaReply
{
    display: flex;
    justify-content: end;
}