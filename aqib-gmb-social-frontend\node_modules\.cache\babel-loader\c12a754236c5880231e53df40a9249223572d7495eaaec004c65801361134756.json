{"ast": null, "code": "'use strict';\n\nvar $defineProperty = require('es-define-property');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar gopd = require('gopd');\n\n/** @type {import('.')} */\nmodule.exports = function defineDataProperty(obj, property, value) {\n  if (!obj || typeof obj !== 'object' && typeof obj !== 'function') {\n    throw new $TypeError('`obj` must be an object or a function`');\n  }\n  if (typeof property !== 'string' && typeof property !== 'symbol') {\n    throw new $TypeError('`property` must be a string or a symbol`');\n  }\n  if (arguments.length > 3 && typeof arguments[3] !== 'boolean' && arguments[3] !== null) {\n    throw new $TypeError('`nonEnumerable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 4 && typeof arguments[4] !== 'boolean' && arguments[4] !== null) {\n    throw new $TypeError('`nonWritable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 5 && typeof arguments[5] !== 'boolean' && arguments[5] !== null) {\n    throw new $TypeError('`nonConfigurable`, if provided, must be a boolean or null');\n  }\n  if (arguments.length > 6 && typeof arguments[6] !== 'boolean') {\n    throw new $TypeError('`loose`, if provided, must be a boolean');\n  }\n  var nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n  var nonWritable = arguments.length > 4 ? arguments[4] : null;\n  var nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n  var loose = arguments.length > 6 ? arguments[6] : false;\n\n  /* @type {false | TypedPropertyDescriptor<unknown>} */\n  var desc = !!gopd && gopd(obj, property);\n  if ($defineProperty) {\n    $defineProperty(obj, property, {\n      configurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n      enumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n      value: value,\n      writable: nonWritable === null && desc ? desc.writable : !nonWritable\n    });\n  } else if (loose || !nonEnumerable && !nonWritable && !nonConfigurable) {\n    // must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n    obj[property] = value; // eslint-disable-line no-param-reassign\n  } else {\n    throw new $SyntaxError('This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.');\n  }\n};", "map": {"version": 3, "names": ["$defineProperty", "require", "$SyntaxError", "$TypeError", "gopd", "module", "exports", "defineDataProperty", "obj", "property", "value", "arguments", "length", "nonEnumerable", "nonWritable", "nonConfigurable", "loose", "desc", "configurable", "enumerable", "writable"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/define-data-property/index.js"], "sourcesContent": ["'use strict';\n\nvar $defineProperty = require('es-define-property');\n\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\n\nvar gopd = require('gopd');\n\n/** @type {import('.')} */\nmodule.exports = function defineDataProperty(\n\tobj,\n\tproperty,\n\tvalue\n) {\n\tif (!obj || (typeof obj !== 'object' && typeof obj !== 'function')) {\n\t\tthrow new $TypeError('`obj` must be an object or a function`');\n\t}\n\tif (typeof property !== 'string' && typeof property !== 'symbol') {\n\t\tthrow new $TypeError('`property` must be a string or a symbol`');\n\t}\n\tif (arguments.length > 3 && typeof arguments[3] !== 'boolean' && arguments[3] !== null) {\n\t\tthrow new $TypeError('`nonEnumerable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 4 && typeof arguments[4] !== 'boolean' && arguments[4] !== null) {\n\t\tthrow new $TypeError('`nonWritable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 5 && typeof arguments[5] !== 'boolean' && arguments[5] !== null) {\n\t\tthrow new $TypeError('`nonConfigurable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 6 && typeof arguments[6] !== 'boolean') {\n\t\tthrow new $TypeError('`loose`, if provided, must be a boolean');\n\t}\n\n\tvar nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n\tvar nonWritable = arguments.length > 4 ? arguments[4] : null;\n\tvar nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n\tvar loose = arguments.length > 6 ? arguments[6] : false;\n\n\t/* @type {false | TypedPropertyDescriptor<unknown>} */\n\tvar desc = !!gopd && gopd(obj, property);\n\n\tif ($defineProperty) {\n\t\t$defineProperty(obj, property, {\n\t\t\tconfigurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n\t\t\tenumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n\t\t\tvalue: value,\n\t\t\twritable: nonWritable === null && desc ? desc.writable : !nonWritable\n\t\t});\n\t} else if (loose || (!nonEnumerable && !nonWritable && !nonConfigurable)) {\n\t\t// must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n\t\tobj[property] = value; // eslint-disable-line no-param-reassign\n\t} else {\n\t\tthrow new $SyntaxError('This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.');\n\t}\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAEnD,IAAIC,YAAY,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAC9C,IAAIE,UAAU,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAE1C,IAAIG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;;AAE1B;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAC3CC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACJ;EACD,IAAI,CAACF,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAW,EAAE;IACnE,MAAM,IAAIL,UAAU,CAAC,wCAAwC,CAAC;EAC/D;EACA,IAAI,OAAOM,QAAQ,KAAK,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IACjE,MAAM,IAAIN,UAAU,CAAC,0CAA0C,CAAC;EACjE;EACA,IAAIQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACvF,MAAM,IAAIR,UAAU,CAAC,yDAAyD,CAAC;EAChF;EACA,IAAIQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACvF,MAAM,IAAIR,UAAU,CAAC,uDAAuD,CAAC;EAC9E;EACA,IAAIQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACvF,MAAM,IAAIR,UAAU,CAAC,2DAA2D,CAAC;EAClF;EACA,IAAIQ,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;IAC9D,MAAM,IAAIR,UAAU,CAAC,yCAAyC,CAAC;EAChE;EAEA,IAAIU,aAAa,GAAGF,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9D,IAAIG,WAAW,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC5D,IAAII,eAAe,GAAGJ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAChE,IAAIK,KAAK,GAAGL,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;;EAEvD;EACA,IAAIM,IAAI,GAAG,CAAC,CAACb,IAAI,IAAIA,IAAI,CAACI,GAAG,EAAEC,QAAQ,CAAC;EAExC,IAAIT,eAAe,EAAE;IACpBA,eAAe,CAACQ,GAAG,EAAEC,QAAQ,EAAE;MAC9BS,YAAY,EAAEH,eAAe,KAAK,IAAI,IAAIE,IAAI,GAAGA,IAAI,CAACC,YAAY,GAAG,CAACH,eAAe;MACrFI,UAAU,EAAEN,aAAa,KAAK,IAAI,IAAII,IAAI,GAAGA,IAAI,CAACE,UAAU,GAAG,CAACN,aAAa;MAC7EH,KAAK,EAAEA,KAAK;MACZU,QAAQ,EAAEN,WAAW,KAAK,IAAI,IAAIG,IAAI,GAAGA,IAAI,CAACG,QAAQ,GAAG,CAACN;IAC3D,CAAC,CAAC;EACH,CAAC,MAAM,IAAIE,KAAK,IAAK,CAACH,aAAa,IAAI,CAACC,WAAW,IAAI,CAACC,eAAgB,EAAE;IACzE;IACAP,GAAG,CAACC,QAAQ,CAAC,GAAGC,KAAK,CAAC,CAAC;EACxB,CAAC,MAAM;IACN,MAAM,IAAIR,YAAY,CAAC,6GAA6G,CAAC;EACtI;AACD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}