{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"disabled\", \"onChange\", \"onClick\", \"onFocus\", \"slotProps\", \"slots\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabUtilityClass } from './tabClasses';\nimport { useTab } from '../useTab';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    selected,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', selected && 'selected', disabled && 'disabled']\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [Tab API](https://mui.com/base-ui/react-tabs/components-api/#tab)\n */\nconst Tab = /*#__PURE__*/React.forwardRef(function Tab(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      disabled = false,\n      slotProps = {},\n      slots = {},\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tabRef = React.useRef(null);\n  const handleRef = useForkRef(tabRef, forwardedRef);\n  const {\n    active,\n    highlighted,\n    selected,\n    getRootProps\n  } = useTab(_extends({}, props, {\n    rootRef: handleRef,\n    value\n  }));\n  const ownerState = _extends({}, props, {\n    active,\n    disabled,\n    highlighted,\n    selected\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabRoot = (_slots$root = slots.root) != null ? _slots$root : 'button';\n  const tabRootProps = useSlotProps({\n    elementType: TabRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabRoot, _extends({}, tabRootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tab.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Tab.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Tab.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * You can provide your own value. Otherwise, it falls back to the child position index.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { Tab };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useForkRef", "useForkRef", "unstable_composeClasses", "composeClasses", "getTabUtilityClass", "useTab", "useSlotProps", "useClassNamesOverride", "jsx", "_jsx", "useUtilityClasses", "ownerState", "selected", "disabled", "slots", "root", "Tab", "forwardRef", "props", "forwardedRef", "_slots$root", "children", "slotProps", "value", "other", "tabRef", "useRef", "handleRef", "active", "highlighted", "getRootProps", "rootRef", "classes", "TabRoot", "tabRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "className", "process", "env", "NODE_ENV", "propTypes", "action", "oneOfType", "func", "shape", "current", "focusVisible", "isRequired", "node", "bool", "onChange", "object", "number", "string"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/Tab/Tab.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"disabled\", \"onChange\", \"onClick\", \"onFocus\", \"slotProps\", \"slots\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabUtilityClass } from './tabClasses';\nimport { useTab } from '../useTab';\nimport { useSlotProps } from '../utils';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    selected,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', selected && 'selected', disabled && 'disabled']\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [Tab API](https://mui.com/base-ui/react-tabs/components-api/#tab)\n */\nconst Tab = /*#__PURE__*/React.forwardRef(function Tab(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      disabled = false,\n      slotProps = {},\n      slots = {},\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tabRef = React.useRef(null);\n  const handleRef = useForkRef(tabRef, forwardedRef);\n  const {\n    active,\n    highlighted,\n    selected,\n    getRootProps\n  } = useTab(_extends({}, props, {\n    rootRef: handleRef,\n    value\n  }));\n  const ownerState = _extends({}, props, {\n    active,\n    disabled,\n    highlighted,\n    selected\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabRoot = (_slots$root = slots.root) != null ? _slots$root : 'button';\n  const tabRootProps = useSlotProps({\n    elementType: TabRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabRoot, _extends({}, tabRootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Tab.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The props used for each slot inside the Tab.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Tab.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * You can provide your own value. Otherwise, it falls back to the child position index.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { Tab };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;AACrH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAEC,QAAQ,IAAI,UAAU;EAC/D,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAEP,qBAAqB,CAACH,kBAAkB,CAAC,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,GAAG,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,SAASD,GAAGA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC1E,IAAIC,WAAW;EACf,MAAM;MACFC,QAAQ;MACRR,QAAQ,GAAG,KAAK;MAChBS,SAAS,GAAG,CAAC,CAAC;MACdR,KAAK,GAAG,CAAC,CAAC;MACVS;IACF,CAAC,GAAGL,KAAK;IACTM,KAAK,GAAG5B,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAM4B,MAAM,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMC,SAAS,GAAG1B,UAAU,CAACwB,MAAM,EAAEN,YAAY,CAAC;EAClD,MAAM;IACJS,MAAM;IACNC,WAAW;IACXjB,QAAQ;IACRkB;EACF,CAAC,GAAGzB,MAAM,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IAC7Ba,OAAO,EAAEJ,SAAS;IAClBJ;EACF,CAAC,CAAC,CAAC;EACH,MAAMZ,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACrCU,MAAM;IACNf,QAAQ;IACRgB,WAAW;IACXjB;EACF,CAAC,CAAC;EACF,MAAMoB,OAAO,GAAGtB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsB,OAAO,GAAG,CAACb,WAAW,GAAGN,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGK,WAAW,GAAG,QAAQ;EAC3E,MAAMc,YAAY,GAAG5B,YAAY,CAAC;IAChC6B,WAAW,EAAEF,OAAO;IACpBG,YAAY,EAAEN,YAAY;IAC1BO,iBAAiB,EAAEf,SAAS,CAACP,IAAI;IACjCuB,sBAAsB,EAAEd,KAAK;IAC7Be,eAAe,EAAE;MACfC,GAAG,EAAErB;IACP,CAAC;IACDR,UAAU;IACV8B,SAAS,EAAET,OAAO,CAACjB;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,IAAI,CAACwB,OAAO,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,YAAY,EAAE;IAC3Db,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,GAAG,CAAC6B,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,MAAM,EAAE/C,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAACiD,IAAI,EAAEjD,SAAS,CAACkD,KAAK,CAAC;IAC3DC,OAAO,EAAEnD,SAAS,CAACkD,KAAK,CAAC;MACvBE,YAAY,EAAEpD,SAAS,CAACiD,IAAI,CAACI;IAC/B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACE/B,QAAQ,EAAEtB,SAAS,CAACsD,IAAI;EACxB;AACF;AACA;AACA;EACExC,QAAQ,EAAEd,SAAS,CAACuD,IAAI;EACxB;AACF;AACA;EACEC,QAAQ,EAAExD,SAAS,CAACiD,IAAI;EACxB;AACF;AACA;AACA;EACE1B,SAAS,EAAEvB,SAAS,CAACkD,KAAK,CAAC;IACzBlC,IAAI,EAAEhB,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAACiD,IAAI,EAAEjD,SAAS,CAACyD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE1C,KAAK,EAAEf,SAAS,CAACkD,KAAK,CAAC;IACrBlC,IAAI,EAAEhB,SAAS,CAACoC;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEZ,KAAK,EAAExB,SAAS,CAACgD,SAAS,CAAC,CAAChD,SAAS,CAAC0D,MAAM,EAAE1D,SAAS,CAAC2D,MAAM,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,SAAS1C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}