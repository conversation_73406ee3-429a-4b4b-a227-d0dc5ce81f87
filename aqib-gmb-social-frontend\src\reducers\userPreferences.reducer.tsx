import {
  AUTH_REQUESTED,
  AUTH_SUCCESS,
  AUTH_LOGOUT,
  AUTH_ERROR,
  AUTH_UNAUTHORIZED,
  UPDATE_MENU_STATE,
  TOGGLED_MENU_ITEM,
} from "../constants/reducer.constant";
import { IRoleBasedAccessResponseModel } from "../interfaces/response/IRoleBasedAccessResponseModel";
import { ILoggedInUserResponseModel } from "../interfaces/response/ISignInResponseModel";

interface UserPreferencesState {
  menuOpened: boolean;
  toggledMenuId: string;
}

const initialState: UserPreferencesState = {
  menuOpened: false,
  toggledMenuId: "",
};

const userPreferencesReducer = (prevState = initialState, action: any) => {
  switch (action.type) {
    case UPDATE_MENU_STATE:
      return {
        ...prevState,
        menuOpened: action.payload,
      };
    case TOGGLED_MENU_ITEM:
      return {
        ...prevState,
        toggledMenuId: action.payload,
      };
    default:
      return prevState;
  }
};

export default userPreferencesReducer;
