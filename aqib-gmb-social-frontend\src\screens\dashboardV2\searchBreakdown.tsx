import React, { useEffect, useState, useRef, useContext } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  CircularProgress,
  Grid,
  Chip,
} from "@mui/material";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";
import LocationMetricsService from "../../services/locationMetrics/locationMetrics.service";
import { useDispatch } from "react-redux";
import { LoadingContext } from "../../context/loading.context";
import CloseIcon from "@mui/icons-material/Close";
import IconButton from "@mui/material/IconButton";

interface InsightsValue {
  value?: string;
  threshold?: string;
}

interface SearchKeyword {
  searchKeyword: string;
  insightsValue: InsightsValue;
}

interface Props {
  accountId: any;
  locationId: any;
  from: any;
  to: any;
}

const SearchBreakdown: React.FC<Props> = ({
  accountId,
  locationId,
  from,
  to,
}) => {
  const dispatch = useDispatch();
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<SearchKeyword[]>([]);
  const [nextPageToken, setNextPageToken] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement | null>(null);
  const isFetchingRef = useRef(false);
  const _locationMetricsService = new LocationMetricsService(dispatch);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (accountId && locationId && from && to) {
      fetchSearchKeywords();
    }
  }, [accountId, locationId, from, to]);

  const fetchSearchKeywords = async () => {
    try {
      setLoading(true);
      const requestObj: ILocationMetricsRequestModel = {
        startDate: from,
        endDate: to,
      };
      const getMetricsRequestHeader = {
        "x-gmb-account-id": accountId,
        "x-gmb-location-id": locationId,
      };
      let response: any = await _locationMetricsService.getSearchkeywords(
        requestObj,
        getMetricsRequestHeader
      );

      console.log("Search keywords:", response.data);

      setData(response.data.searchKeywordsCounts);
      setNextPageToken(response.data.nextPageToken || null);
    } catch (error) {
      console.error("Failed to fetch analytics data", error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchMoreData = async (pageToken: string) => {
    const requestObj: ILocationMetricsRequestModel = {
      startDate: from,
      endDate: to,
      pageToken: pageToken,
    };
    const getMetricsRequestHeader = {
      "x-gmb-account-id": accountId,
      "x-gmb-location-id": locationId,
    };
    let response: any = await _locationMetricsService.getSearchkeywords(
      requestObj,
      getMetricsRequestHeader
    );

    const result = response.data;
    return result;
  };

  useEffect(() => {
    const handleScroll = async () => {
      if (
        modalRef.current &&
        modalRef.current.scrollTop + modalRef.current.clientHeight >=
          modalRef.current.scrollHeight - 100 &&
        nextPageToken &&
        !isFetchingRef.current
      ) {
        isFetchingRef.current = true;
        const response = await fetchMoreData(nextPageToken);
        setData((prev) => [...prev, ...response.searchKeywordsCounts]);
        setNextPageToken(response.nextPageToken || null);
        isFetchingRef.current = false;
      }
    };

    let refCurrent: HTMLDivElement | null = null;
    setTimeout(() => {
      refCurrent = modalRef.current;
      if (refCurrent) {
        refCurrent.addEventListener("scroll", handleScroll);
      }
    }, 1000);

    return () => {
      if (refCurrent) {
        refCurrent.removeEventListener("scroll", handleScroll);
      }
    };
  }, [nextPageToken, loading, open]);

  const handleOpen = () => {
    setOpen(true);
    setTimeout(() => {
      modalRef.current?.focus();
    }, 1000);
  };
  const handleClose = () => setOpen(false);

  const getCountValue = (item: SearchKeyword) =>
    item.insightsValue.value || item.insightsValue.threshold;

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Box className=""
        sx={{ p: 2, backgroundColor: "#fff", color: "#000", borderRadius: 2 }}
      >
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          {data &&
            data.reduce(
              (sum, item) => sum + parseInt(getCountValue(item) || "0"),
              0
            )}
        </Typography>
        <Typography variant="subtitle1" gutterBottom>
          Searches showed your Business Profile in the search results
        </Typography>
        <Grid container spacing={2}>
          {data &&
            data.slice(0, 18).map((item, index) => (
              <Grid item xs={12} sm={6} md={6} lg={6} key={index}>
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography variant="body1">
                    {index + 1}. {item.searchKeyword}
                  </Typography>
                  <Chip
                    label={getCountValue(item)}
                    color="primary"
                    variant="outlined"
                    sx={{ ml: 2 }}
                  />
                </Box>
              </Grid>
            ))}
        </Grid>
        <Button
          style={{ textTransform: "capitalize" }}
          variant="outlined"
          onClick={handleOpen}
          sx={{ mt: 2 }}
        >
          See more
        </Button>

        <Dialog open={open} onClose={handleClose} fullWidth maxWidth="sm">
          <DialogTitle
            sx={{
              m: 0,
              p: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="h6">All Search Terms</Typography>
            <IconButton
              aria-label="close"
              onClick={handleClose}
              sx={{
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Box
              ref={modalRef}
              tabIndex={0} // This helps move focus here
              sx={{ maxHeight: 400, overflowY: "auto", outline: "none" }}
            >
              <List>
                {data &&
                  data.map((item, index) => (
                    <ListItem key={index} divider>
                      <Box
                        sx={{
                          width: "100%",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Typography variant="body1">
                          {index + 1}. {item.searchKeyword}
                        </Typography>
                        <Chip
                          label={getCountValue(item)}
                          color="primary"
                          variant="outlined"
                          sx={{ ml: 2 }}
                        />
                      </Box>
                    </ListItem>
                  ))}
              </List>
              {loading && (
                <CircularProgress
                  size={24}
                  sx={{ display: "block", mx: "auto", mt: 2 }}
                />
              )}
            </Box>
          </DialogContent>
        </Dialog>
      </Box>
    </div>
  );
};

export default SearchBreakdown;
