const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class GeoGrid {
  /**
   * Save grid configuration to database
   */
  static async saveGridConfiguration(configData) {
    try {
      const {
        userId,
        name,
        centerLat,
        centerLng,
        gridSize,
        distance,
        distanceUnit,
        searchType,
        searchQuery,
        isScheduleEnabled,
        settings,
      } = configData;

      const query = `
        INSERT INTO geo_grid_configurations
        (user_id, name, center_lat, center_lng, grid_size, distance, distance_unit,
         search_type, search_query, is_schedule_enabled, settings, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      // Safely handle settings JSON serialization
      let settingsJson;
      try {
        if (typeof settings === "string") {
          // If it's already a string, try to parse and re-stringify to validate
          JSON.parse(settings);
          settingsJson = settings;
        } else if (settings === null || settings === undefined) {
          settingsJson = "{}";
        } else {
          settingsJson = JSON.stringify(settings);
        }
      } catch (error) {
        logger.warn("Invalid settings object, using empty object:", error);
        settingsJson = "{}";
      }

      const values = [
        userId,
        name,
        centerLat,
        centerLng,
        gridSize,
        distance,
        distanceUnit,
        searchType,
        searchQuery,
        isScheduleEnabled,
        settingsJson,
      ];

      const result = await pool.query(query, values);
      return { id: result.insertId, ...configData };
    } catch (error) {
      logger.error("Error saving grid configuration:", error);
      throw error;
    }
  }

  /**
   * Get all grid configurations for a user
   */
  static async getGridConfigurations(userId) {
    try {
      const query = `
        SELECT * FROM geo_grid_configurations
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;

      const results = await pool.query(query, [userId]);

      return results.map((config) => ({
        id: config.id,
        userId: config.user_id,
        name: config.name,
        centerLat: config.center_lat,
        centerLng: config.center_lng,
        gridSize: config.grid_size,
        distance: config.distance,
        distanceUnit: config.distance_unit,
        searchType: config.search_type,
        searchQuery: config.search_query,
        isScheduleEnabled: config.is_schedule_enabled,
        settings: config.settings ? JSON.parse(config.settings) : {},
        createdAt: config.created_at,
        updatedAt: config.updated_at,
      }));
    } catch (error) {
      logger.error("Error fetching grid configurations:", error);
      throw error;
    }
  }

  /**
   * Get specific grid configuration by ID
   */
  static async getGridConfigurationById(gridId, userId) {
    try {
      const query = `
        SELECT * FROM geo_grid_configurations
        WHERE id = ? AND user_id = ?
      `;

      const results = await pool.query(query, [gridId, userId]);

      if (results.length === 0) {
        return null;
      }

      const config = results[0];
      return {
        id: config.id,
        userId: config.user_id,
        name: config.name,
        centerLat: config.center_lat,
        centerLng: config.center_lng,
        gridSize: config.grid_size,
        distance: config.distance,
        distanceUnit: config.distance_unit,
        searchType: config.search_type,
        searchQuery: config.search_query,
        isScheduleEnabled: config.is_schedule_enabled,
        settings: config.settings ? JSON.parse(config.settings) : {},
        createdAt: config.created_at,
        updatedAt: config.updated_at,
      };
    } catch (error) {
      logger.error("Error fetching grid configuration by ID:", error);
      throw error;
    }
  }

  /**
   * Update grid configuration
   */
  static async updateGridConfiguration(gridId, userId, updateData) {
    try {
      const {
        name,
        centerLat,
        centerLng,
        gridSize,
        distance,
        distanceUnit,
        searchType,
        searchQuery,
        isScheduleEnabled,
        settings,
      } = updateData;

      const query = `
        UPDATE geo_grid_configurations
        SET name = ?, center_lat = ?, center_lng = ?, grid_size = ?,
            distance = ?, distance_unit = ?, search_type = ?, search_query = ?,
            is_schedule_enabled = ?, settings = ?, updated_at = NOW()
        WHERE id = ? AND user_id = ?
      `;

      // Safely handle settings JSON serialization
      let settingsJson;
      try {
        if (typeof settings === "string") {
          // If it's already a string, try to parse and re-stringify to validate
          JSON.parse(settings);
          settingsJson = settings;
        } else if (settings === null || settings === undefined) {
          settingsJson = "{}";
        } else {
          settingsJson = JSON.stringify(settings);
        }
      } catch (error) {
        logger.warn("Invalid settings object, using empty object:", error);
        settingsJson = "{}";
      }

      const values = [
        name,
        centerLat,
        centerLng,
        gridSize,
        distance,
        distanceUnit,
        searchType,
        searchQuery,
        isScheduleEnabled,
        settingsJson,
        gridId,
        userId,
      ];

      const result = await pool.query(query, values);

      if (result.affectedRows === 0) {
        throw new Error("Grid configuration not found or unauthorized");
      }

      return { id: gridId, ...updateData };
    } catch (error) {
      logger.error("Error updating grid configuration:", error);
      throw error;
    }
  }

  /**
   * Delete grid configuration
   */
  static async deleteGridConfiguration(gridId, userId) {
    try {
      const query = `
        DELETE FROM geo_grid_configurations
        WHERE id = ? AND user_id = ?
      `;

      const result = await pool.query(query, [gridId, userId]);

      if (result.affectedRows === 0) {
        throw new Error("Grid configuration not found or unauthorized");
      }

      return { success: true };
    } catch (error) {
      logger.error("Error deleting grid configuration:", error);
      throw error;
    }
  }

  /**
   * Save grid points data
   */
  static async saveGridPoints(gridId, gridPoints) {
    try {
      // First, delete existing grid points for this configuration
      await pool.query("DELETE FROM geo_grid_points WHERE grid_config_id = ?", [
        gridId,
      ]);

      // Insert new grid points
      if (gridPoints && gridPoints.length > 0) {
        const insertQuery = `
          INSERT INTO geo_grid_points
          (grid_config_id, point_index, lat, lng, address, place_id, created_at)
          VALUES ?
        `;

        const values = gridPoints.map((point, index) => [
          gridId,
          index,
          point.lat,
          point.lng,
          point.address || null,
          point.placeId || null,
          new Date(),
        ]);

        await pool.query(insertQuery, [values]);
      }

      return { success: true };
    } catch (error) {
      logger.error("Error saving grid points:", error);
      throw error;
    }
  }

  /**
   * Get grid points for a configuration
   */
  static async getGridPoints(gridId) {
    try {
      const query = `
        SELECT * FROM geo_grid_points
        WHERE grid_config_id = ?
        ORDER BY point_index
      `;

      const results = await pool.query(query, [gridId]);
      return results;
    } catch (error) {
      logger.error("Error fetching grid points:", error);
      throw error;
    }
  }
};
