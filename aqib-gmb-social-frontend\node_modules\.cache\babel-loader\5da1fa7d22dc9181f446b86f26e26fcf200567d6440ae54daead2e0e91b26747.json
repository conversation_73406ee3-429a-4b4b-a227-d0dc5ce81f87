{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useControllableReducer } from '../utils/useControllableReducer';\nimport { DropdownActionTypes } from './useDropdown.types';\nimport { dropdownReducer } from './dropdownReducer';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useDropdown API](https://mui.com/base-ui/react-menu/hooks-api/#use-dropdown)\n */\nexport function useDropdown(parameters = {}) {\n  const {\n    defaultOpen,\n    onOpenChange,\n    open: openProp,\n    componentName = 'useDropdown'\n  } = parameters;\n  const [popupId, setPopupId] = React.useState('');\n  const [triggerElement, setTriggerElement] = React.useState(null);\n  const lastActionType = React.useRef(null);\n  const handleStateChange = React.useCallback((event, field, value, reason) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(event, value);\n    }\n    lastActionType.current = reason;\n  }, [onOpenChange]);\n  const controlledProps = React.useMemo(() => openProp !== undefined ? {\n    open: openProp\n  } : {}, [openProp]);\n  const [state, dispatch] = useControllableReducer({\n    controlledProps,\n    initialState: defaultOpen ? {\n      open: true,\n      changeReason: null\n    } : {\n      open: false,\n      changeReason: null\n    },\n    onStateChange: handleStateChange,\n    reducer: dropdownReducer,\n    componentName\n  });\n  React.useEffect(() => {\n    if (!state.open && lastActionType.current !== null && lastActionType.current !== DropdownActionTypes.blur) {\n      triggerElement == null || triggerElement.focus();\n    }\n  }, [state.open, triggerElement]);\n  const contextValue = {\n    state,\n    dispatch,\n    popupId,\n    registerPopup: setPopupId,\n    registerTrigger: setTriggerElement,\n    triggerElement\n  };\n  return {\n    contextValue,\n    open: state.open\n  };\n}", "map": {"version": 3, "names": ["React", "useControllableReducer", "DropdownActionTypes", "dropdownReducer", "useDropdown", "parameters", "defaultOpen", "onOpenChange", "open", "openProp", "componentName", "popupId", "setPopupId", "useState", "triggerElement", "setTriggerElement", "lastActionType", "useRef", "handleStateChange", "useCallback", "event", "field", "value", "reason", "current", "controlledProps", "useMemo", "undefined", "state", "dispatch", "initialState", "changeReason", "onStateChange", "reducer", "useEffect", "blur", "focus", "contextValue", "registerPopup", "registerTrigger"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useDropdown/useDropdown.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useControllableReducer } from '../utils/useControllableReducer';\nimport { DropdownActionTypes } from './useDropdown.types';\nimport { dropdownReducer } from './dropdownReducer';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useDropdown API](https://mui.com/base-ui/react-menu/hooks-api/#use-dropdown)\n */\nexport function useDropdown(parameters = {}) {\n  const {\n    defaultOpen,\n    onOpenChange,\n    open: openProp,\n    componentName = 'useDropdown'\n  } = parameters;\n  const [popupId, setPopupId] = React.useState('');\n  const [triggerElement, setTriggerElement] = React.useState(null);\n  const lastActionType = React.useRef(null);\n  const handleStateChange = React.useCallback((event, field, value, reason) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(event, value);\n    }\n    lastActionType.current = reason;\n  }, [onOpenChange]);\n  const controlledProps = React.useMemo(() => openProp !== undefined ? {\n    open: openProp\n  } : {}, [openProp]);\n  const [state, dispatch] = useControllableReducer({\n    controlledProps,\n    initialState: defaultOpen ? {\n      open: true,\n      changeReason: null\n    } : {\n      open: false,\n      changeReason: null\n    },\n    onStateChange: handleStateChange,\n    reducer: dropdownReducer,\n    componentName\n  });\n  React.useEffect(() => {\n    if (!state.open && lastActionType.current !== null && lastActionType.current !== DropdownActionTypes.blur) {\n      triggerElement == null || triggerElement.focus();\n    }\n  }, [state.open, triggerElement]);\n  const contextValue = {\n    state,\n    dispatch,\n    popupId,\n    registerPopup: setPopupId,\n    registerTrigger: setTriggerElement,\n    triggerElement\n  };\n  return {\n    contextValue,\n    open: state.open\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC3C,MAAM;IACJC,WAAW;IACXC,YAAY;IACZC,IAAI,EAAEC,QAAQ;IACdC,aAAa,GAAG;EAClB,CAAC,GAAGL,UAAU;EACd,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGZ,KAAK,CAACa,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,KAAK,CAACa,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAMG,cAAc,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,iBAAiB,GAAGlB,KAAK,CAACmB,WAAW,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;IAC3E,IAAIF,KAAK,KAAK,MAAM,EAAE;MACpBd,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACa,KAAK,EAAEE,KAAK,CAAC;IACpD;IACAN,cAAc,CAACQ,OAAO,GAAGD,MAAM;EACjC,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAClB,MAAMkB,eAAe,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,MAAMjB,QAAQ,KAAKkB,SAAS,GAAG;IACnEnB,IAAI,EAAEC;EACR,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACnB,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,sBAAsB,CAAC;IAC/CwB,eAAe;IACfK,YAAY,EAAExB,WAAW,GAAG;MAC1BE,IAAI,EAAE,IAAI;MACVuB,YAAY,EAAE;IAChB,CAAC,GAAG;MACFvB,IAAI,EAAE,KAAK;MACXuB,YAAY,EAAE;IAChB,CAAC;IACDC,aAAa,EAAEd,iBAAiB;IAChCe,OAAO,EAAE9B,eAAe;IACxBO;EACF,CAAC,CAAC;EACFV,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACN,KAAK,CAACpB,IAAI,IAAIQ,cAAc,CAACQ,OAAO,KAAK,IAAI,IAAIR,cAAc,CAACQ,OAAO,KAAKtB,mBAAmB,CAACiC,IAAI,EAAE;MACzGrB,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACsB,KAAK,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACR,KAAK,CAACpB,IAAI,EAAEM,cAAc,CAAC,CAAC;EAChC,MAAMuB,YAAY,GAAG;IACnBT,KAAK;IACLC,QAAQ;IACRlB,OAAO;IACP2B,aAAa,EAAE1B,UAAU;IACzB2B,eAAe,EAAExB,iBAAiB;IAClCD;EACF,CAAC;EACD,OAAO;IACLuB,YAAY;IACZ7B,IAAI,EAAEoB,KAAK,CAACpB;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}