{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"enterAnimationName\", \"enterClassName\", \"exitAnimationName\", \"exitClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useTransitionStateManager } from '../useTransition';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/)\n *\n * API:\n *\n * - [CssAnimation API](https://mui.com/base-ui/react-transitions/components-api/#css-animation)\n */\nfunction CssAnimation(props) {\n  const {\n      children,\n      className,\n      enterAnimationName,\n      enterClassName,\n      exitAnimationName,\n      exitClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    requestedEnter,\n    onExited\n  } = useTransitionStateManager();\n  const hasExited = React.useRef(true);\n  React.useEffect(() => {\n    if (requestedEnter && hasExited.current) {\n      hasExited.current = false;\n    }\n  }, [requestedEnter]);\n  const handleAnimationEnd = React.useCallback(event => {\n    if (event.animationName === exitAnimationName) {\n      onExited();\n      hasExited.current = true;\n    } else if (event.animationName === enterAnimationName) {\n      hasExited.current = false;\n    }\n  }, [onExited, exitAnimationName, enterAnimationName]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    onAnimationEnd: handleAnimationEnd,\n    className: clsx(className, requestedEnter ? enterClassName : exitClassName)\n  }, other, {\n    children: children\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? CssAnimation.propTypes = {\n  children: PropTypes.node,\n  className: PropTypes.string,\n  enterAnimationName: PropTypes.string,\n  enterClassName: PropTypes.string,\n  exitAnimationName: PropTypes.string,\n  exitClassName: PropTypes.string\n} : void 0;\nexport { CssAnimation };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "useTransitionStateManager", "jsx", "_jsx", "CssAnimation", "props", "children", "className", "enterAnimationName", "enterClassName", "exitAnimationName", "exitClassName", "other", "requestedEnter", "onExited", "hasExited", "useRef", "useEffect", "current", "handleAnimationEnd", "useCallback", "event", "animationName", "onAnimationEnd", "process", "env", "NODE_ENV", "propTypes", "node", "string"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/Transitions/CssAnimation.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"enterAnimationName\", \"enterClassName\", \"exitAnimationName\", \"exitClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useTransitionStateManager } from '../useTransition';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/)\n *\n * API:\n *\n * - [CssAnimation API](https://mui.com/base-ui/react-transitions/components-api/#css-animation)\n */\nfunction CssAnimation(props) {\n  const {\n      children,\n      className,\n      enterAnimationName,\n      enterClassName,\n      exitAnimationName,\n      exitClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    requestedEnter,\n    onExited\n  } = useTransitionStateManager();\n  const hasExited = React.useRef(true);\n  React.useEffect(() => {\n    if (requestedEnter && hasExited.current) {\n      hasExited.current = false;\n    }\n  }, [requestedEnter]);\n  const handleAnimationEnd = React.useCallback(event => {\n    if (event.animationName === exitAnimationName) {\n      onExited();\n      hasExited.current = true;\n    } else if (event.animationName === enterAnimationName) {\n      hasExited.current = false;\n    }\n  }, [onExited, exitAnimationName, enterAnimationName]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    onAnimationEnd: handleAnimationEnd,\n    className: clsx(className, requestedEnter ? enterClassName : exitClassName)\n  }, other, {\n    children: children\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? CssAnimation.propTypes = {\n  children: PropTypes.node,\n  className: PropTypes.string,\n  enterAnimationName: PropTypes.string,\n  enterClassName: PropTypes.string,\n  exitAnimationName: PropTypes.string,\n  exitClassName: PropTypes.string\n} : void 0;\nexport { CssAnimation };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,eAAe,CAAC;AACzH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,yBAAyB,QAAQ,kBAAkB;AAC5D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAM;MACFC,QAAQ;MACRC,SAAS;MACTC,kBAAkB;MAClBC,cAAc;MACdC,iBAAiB;MACjBC;IACF,CAAC,GAAGN,KAAK;IACTO,KAAK,GAAGhB,6BAA6B,CAACS,KAAK,EAAER,SAAS,CAAC;EACzD,MAAM;IACJgB,cAAc;IACdC;EACF,CAAC,GAAGb,yBAAyB,CAAC,CAAC;EAC/B,MAAMc,SAAS,GAAGjB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;EACpClB,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpB,IAAIJ,cAAc,IAAIE,SAAS,CAACG,OAAO,EAAE;MACvCH,SAAS,CAACG,OAAO,GAAG,KAAK;IAC3B;EACF,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EACpB,MAAMM,kBAAkB,GAAGrB,KAAK,CAACsB,WAAW,CAACC,KAAK,IAAI;IACpD,IAAIA,KAAK,CAACC,aAAa,KAAKZ,iBAAiB,EAAE;MAC7CI,QAAQ,CAAC,CAAC;MACVC,SAAS,CAACG,OAAO,GAAG,IAAI;IAC1B,CAAC,MAAM,IAAIG,KAAK,CAACC,aAAa,KAAKd,kBAAkB,EAAE;MACrDO,SAAS,CAACG,OAAO,GAAG,KAAK;IAC3B;EACF,CAAC,EAAE,CAACJ,QAAQ,EAAEJ,iBAAiB,EAAEF,kBAAkB,CAAC,CAAC;EACrD,OAAO,aAAaL,IAAI,CAAC,KAAK,EAAER,QAAQ,CAAC;IACvC4B,cAAc,EAAEJ,kBAAkB;IAClCZ,SAAS,EAAEP,IAAI,CAACO,SAAS,EAAEM,cAAc,GAAGJ,cAAc,GAAGE,aAAa;EAC5E,CAAC,EAAEC,KAAK,EAAE;IACRN,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AACAkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,YAAY,CAACuB,SAAS,GAAG;EAC/DrB,QAAQ,EAAEP,SAAS,CAAC6B,IAAI;EACxBrB,SAAS,EAAER,SAAS,CAAC8B,MAAM;EAC3BrB,kBAAkB,EAAET,SAAS,CAAC8B,MAAM;EACpCpB,cAAc,EAAEV,SAAS,CAAC8B,MAAM;EAChCnB,iBAAiB,EAAEX,SAAS,CAAC8B,MAAM;EACnClB,aAAa,EAAEZ,SAAS,CAAC8B;AAC3B,CAAC,GAAG,KAAK,CAAC;AACV,SAASzB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}