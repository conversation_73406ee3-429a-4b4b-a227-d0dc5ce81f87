{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabPanelUtilityClass } from './tabPanelClasses';\nimport { useTabPanel } from '../useTabPanel/useTabPanel';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabPanelUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [TabPanel API](https://mui.com/base-ui/react-tabs/components-api/#tab-panel)\n */\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    hidden,\n    getRootProps\n  } = useTabPanel(props);\n  const ownerState = _extends({}, props, {\n    hidden\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabPanelRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabPanelRootProps = useSlotProps({\n    elementType: TabPanelRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tabpanel',\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({}, tabPanelRootProps, {\n    children: !hidden && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside the TabPanel.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the TabPanel.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected.\n   * If not provided, it will fall back to the index of the panel.\n   * It is recommended to explicitly provide it, as it's required for the tab panel to be rendered on the server.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { TabPanel };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useSlotProps", "unstable_composeClasses", "composeClasses", "getTabPanelUtilityClass", "useTabPanel", "useClassNamesOverride", "jsx", "_jsx", "useUtilityClasses", "ownerState", "hidden", "slots", "root", "TabPanel", "forwardRef", "props", "forwardedRef", "_slots$root", "children", "slotProps", "other", "getRootProps", "classes", "TabPanelRoot", "tabPanelRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "additionalProps", "role", "ref", "className", "process", "env", "NODE_ENV", "propTypes", "node", "string", "shape", "oneOfType", "func", "object", "value", "number"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/TabPanel/TabPanel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { getTabPanelUtilityClass } from './tabPanelClasses';\nimport { useTabPanel } from '../useTabPanel/useTabPanel';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, useClassNamesOverride(getTabPanelUtilityClass));\n};\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/)\n *\n * API:\n *\n * - [TabPanel API](https://mui.com/base-ui/react-tabs/components-api/#tab-panel)\n */\nconst TabPanel = /*#__PURE__*/React.forwardRef(function TabPanel(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    hidden,\n    getRootProps\n  } = useTabPanel(props);\n  const ownerState = _extends({}, props, {\n    hidden\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabPanelRoot = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const tabPanelRootProps = useSlotProps({\n    elementType: TabPanelRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tabpanel',\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({}, tabPanelRootProps, {\n    children: !hidden && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside the TabPanel.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the TabPanel.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected.\n   * If not provided, it will fall back to the index of the panel.\n   * It is recommended to explicitly provide it, as it's required for the tab panel to be rendered on the server.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport { TabPanel };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC;AAC7D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,MAAM,IAAI,QAAQ;EACnC,CAAC;EACD,OAAOR,cAAc,CAACS,KAAK,EAAEN,qBAAqB,CAACF,uBAAuB,CAAC,CAAC;AAC9E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,QAAQ,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,QAAQA,CAACE,KAAK,EAAEC,YAAY,EAAE;EACpF,IAAIC,WAAW;EACf,MAAM;MACFC,QAAQ;MACRC,SAAS,GAAG,CAAC,CAAC;MACdR,KAAK,GAAG,CAAC;IACX,CAAC,GAAGI,KAAK;IACTK,KAAK,GAAGxB,6BAA6B,CAACmB,KAAK,EAAElB,SAAS,CAAC;EACzD,MAAM;IACJa,MAAM;IACNW;EACF,CAAC,GAAGjB,WAAW,CAACW,KAAK,CAAC;EACtB,MAAMN,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IACrCL;EACF,CAAC,CAAC;EACF,MAAMY,OAAO,GAAGd,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMc,YAAY,GAAG,CAACN,WAAW,GAAGN,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGK,WAAW,GAAG,KAAK;EAC7E,MAAMO,iBAAiB,GAAGxB,YAAY,CAAC;IACrCyB,WAAW,EAAEF,YAAY;IACzBG,YAAY,EAAEL,YAAY;IAC1BM,iBAAiB,EAAER,SAAS,CAACP,IAAI;IACjCgB,sBAAsB,EAAER,KAAK;IAC7BS,eAAe,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBC,GAAG,EAAEf;IACP,CAAC;IACDP,UAAU;IACVuB,SAAS,EAAEV,OAAO,CAACV;EACrB,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACgB,YAAY,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,iBAAiB,EAAE;IACrEN,QAAQ,EAAE,CAACR,MAAM,IAAIQ;EACvB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,QAAQ,CAACuB,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACElB,QAAQ,EAAEnB,SAAS,CAACsC,IAAI;EACxB;AACF;AACA;EACEL,SAAS,EAAEjC,SAAS,CAACuC,MAAM;EAC3B;AACF;AACA;AACA;EACEnB,SAAS,EAAEpB,SAAS,CAACwC,KAAK,CAAC;IACzB3B,IAAI,EAAEb,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC0C,IAAI,EAAE1C,SAAS,CAAC2C,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE/B,KAAK,EAAEZ,SAAS,CAACwC,KAAK,CAAC;IACrB3B,IAAI,EAAEb,SAAS,CAAC0B;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEkB,KAAK,EAAE5C,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC6C,MAAM,EAAE7C,SAAS,CAACuC,MAAM,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,SAASzB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}