import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ILocation } from "../interfaces/response/ILocationsListResponseModel";

// Define the interface locally since it's used in multiple places
interface IGraphDataModel {
  data: number[];
  labels: string[];
}

export interface ChartExportData {
  chartTitle: string;
  data: IGraphDataModel;
  selectedLocationIds: string[];
  availableLocations: ILocation[];
  locationDataMap: Record<string, any>; // Individual location data
  metricType: string; // e.g., "WEBSITE_CLICKS", "CALL_CLICKS", etc.
  daysDifference: number;
  isSameMonthYear: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

export class ExcelExportService {
  /**
   * Export chart data to Excel file
   */
  static exportChartToExcel(exportData: ChartExportData): void {
    const {
      chartTitle,
      data,
      selectedLocationIds,
      availableLocations,
      dateRange,
    } = exportData;

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare chart data with new matrix format
    const chartData = this.prepareChartData(exportData);

    // Prepare summary information
    const summaryData = this.prepareSummaryData(
      chartTitle,
      data,
      dateRange,
      selectedLocationIds.length
    );

    // Create worksheets
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    const chartSheet = XLSX.utils.aoa_to_sheet(chartData);

    // Add worksheets to workbook
    XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");
    XLSX.utils.book_append_sheet(workbook, chartSheet, "Chart Data");

    // Set column widths for better readability - adjust for new format
    this.setColumnWidths(summarySheet, [20, 30]);
    // Dynamic column widths based on number of locations
    const chartColumnWidths = [15, ...selectedLocationIds.map(() => 20), 15]; // Date, locations, total
    this.setColumnWidths(chartSheet, chartColumnWidths);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const filename = `${chartTitle.replace(
      /[^a-zA-Z0-9]/g,
      "_"
    )}_${timestamp}.xlsx`;

    // Export the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, filename);
  }

  /**
   * Process individual location data to extract metric values for specific dates
   */
  private static processLocationMetricData(
    locationData: any,
    metricType: string,
    dates: string[],
    daysDifference: number,
    isSameMonthYear: boolean
  ): number[] {
    if (!locationData || !locationData.multiDailyMetricTimeSeries) {
      return dates.map(() => 0);
    }

    // Find the metric series for this location
    let metricSeries: any = null;
    for (const multiMetric of locationData.multiDailyMetricTimeSeries) {
      if (multiMetric && multiMetric.dailyMetricTimeSeries) {
        metricSeries = multiMetric.dailyMetricTimeSeries.find(
          (series: any) => series && series.dailyMetric === metricType
        );
        if (metricSeries) break;
      }
    }

    if (
      !metricSeries ||
      !metricSeries.timeSeries ||
      !metricSeries.timeSeries.datedValues
    ) {
      return dates.map(() => 0);
    }

    // Create a map of date to value
    const dateValueMap: Record<string, number> = {};

    for (const entry of metricSeries.timeSeries.datedValues) {
      if (!entry.date) continue;

      const { year, month, day } = entry.date;
      const key =
        daysDifference < 15 || isSameMonthYear
          ? `${year}-${month.toString().padStart(2, "0")}-${day
              .toString()
              .padStart(2, "0")}`
          : `${year}-${month.toString().padStart(2, "0")}-01`;

      const value = parseInt(entry.value ?? "0", 10);
      dateValueMap[key] = (dateValueMap[key] || 0) + value;
    }

    // Map the aggregated data to the expected date format
    return dates.map((dateLabel) => {
      // Convert the chart date label back to the key format
      let key = "";
      if (daysDifference < 15 || isSameMonthYear) {
        // For daily data, convert "2024-Jan-15" to "2024-01-15"
        const parts = dateLabel.split("-");
        if (parts.length === 3) {
          const monthMap: Record<string, string> = {
            Jan: "01",
            Feb: "02",
            Mar: "03",
            Apr: "04",
            May: "05",
            Jun: "06",
            Jul: "07",
            Aug: "08",
            Sep: "09",
            Oct: "10",
            Nov: "11",
            Dec: "12",
          };
          key = `${parts[0]}-${
            monthMap[parts[1]] || parts[1]
          }-${parts[2].padStart(2, "0")}`;
        }
      } else {
        // For monthly data, convert "Jan 2024" to "2024-01-01"
        const parts = dateLabel.split(" ");
        if (parts.length === 2) {
          const monthMap: Record<string, string> = {
            Jan: "01",
            Feb: "02",
            Mar: "03",
            Apr: "04",
            May: "05",
            Jun: "06",
            Jul: "07",
            Aug: "08",
            Sep: "09",
            Oct: "10",
            Nov: "11",
            Dec: "12",
          };
          key = `${parts[1]}-${monthMap[parts[0]] || "01"}-01`;
        }
      }

      return dateValueMap[key] || 0;
    });
  }

  /**
   * Prepare chart data for Excel export with locations as columns and dates as rows
   */
  private static prepareChartData(exportData: ChartExportData): any[][] {
    const {
      chartTitle,
      selectedLocationIds,
      availableLocations,
      locationDataMap,
      data,
      metricType,
      daysDifference,
      isSameMonthYear,
    } = exportData;
    const chartData: any[][] = [];

    // Debug logging
    console.log("Excel Export Debug:", {
      chartTitle,
      metricType,
      selectedLocationIds,
      locationDataMapKeys: Object.keys(locationDataMap),
      dataLabels: data.labels,
      dataValues: data.data,
      locationDataSample:
        selectedLocationIds.length > 0
          ? locationDataMap[selectedLocationIds[0]]
          : null,
    });

    // Add header
    chartData.push([chartTitle]);
    chartData.push([]); // Empty row

    // Get location names for selected locations
    const selectedLocations = selectedLocationIds.map((locationId) => {
      const location = availableLocations.find(
        (loc) => loc.gmbLocationId === locationId
      );
      return location ? location.gmbLocationName : locationId;
    });

    // Create header row: Date | Location1 | Location2 | ... | Total
    const headerRow = ["Date", ...selectedLocations, "Total"];
    chartData.push(headerRow);

    // Get all unique dates from the aggregated data
    const dates = data.labels || [];

    // Process each location's data for this metric
    const locationDataArrays: Record<string, number[]> = {};
    selectedLocationIds.forEach((locationId) => {
      const locationData = locationDataMap[locationId];
      locationDataArrays[locationId] = this.processLocationMetricData(
        locationData,
        metricType,
        dates,
        daysDifference,
        isSameMonthYear
      );
    });

    // Create data rows
    dates.forEach((date, dateIndex) => {
      const row: any[] = [date]; // Start with date
      let rowTotal = 0;

      // Add data for each location
      selectedLocationIds.forEach((locationId) => {
        const value = locationDataArrays[locationId][dateIndex] || 0;
        row.push(value);
        rowTotal += value;
      });

      // Add row total
      row.push(rowTotal);
      chartData.push(row);
    });

    // Add totals row
    chartData.push([]); // Empty row
    const totalsRow: any[] = ["Total"];
    let grandTotal = 0;

    selectedLocationIds.forEach((locationId) => {
      const locationTotal = locationDataArrays[locationId].reduce(
        (sum: number, value: number) => sum + (value || 0),
        0
      );
      totalsRow.push(locationTotal);
      grandTotal += locationTotal;
    });

    totalsRow.push(grandTotal);
    chartData.push(totalsRow);

    return chartData;
  }

  /**
   * Prepare location data for Excel export
   */
  private static prepareLocationData(
    selectedLocationIds: string[],
    availableLocations: ILocation[]
  ): any[][] {
    const locationData: any[][] = [];

    // Add header
    locationData.push(["Selected Locations"]);
    locationData.push([]); // Empty row
    locationData.push(["Location ID", "Location Name", "Account ID"]);

    // Add location rows
    selectedLocationIds.forEach((locationId) => {
      const location = availableLocations.find(
        (loc) => loc.gmbLocationId === locationId
      );
      if (location) {
        locationData.push([
          location.gmbLocationId,
          location.gmbLocationName,
          location.gmbAccountId,
        ]);
      }
    });

    return locationData;
  }

  /**
   * Prepare summary data for Excel export
   */
  private static prepareSummaryData(
    chartTitle: string,
    data: IGraphDataModel,
    dateRange?: { from: string; to: string },
    locationCount?: number
  ): any[][] {
    const summaryData: any[][] = [];

    // Add title and metadata
    summaryData.push(["Analytics Export Summary"]);
    summaryData.push([]); // Empty row
    summaryData.push(["Chart Title", chartTitle]);
    summaryData.push(["Export Date", new Date().toLocaleString()]);

    if (dateRange) {
      summaryData.push(["Date Range", `${dateRange.from} to ${dateRange.to}`]);
    }

    if (locationCount) {
      summaryData.push(["Number of Locations", locationCount]);
    }

    // Add data summary
    const total = data.data
      ? data.data.reduce((sum: number, value: number) => sum + (value || 0), 0)
      : 0;
    const average =
      data.data && data.data.length > 0 ? total / data.data.length : 0;
    const max = data.data && data.data.length > 0 ? Math.max(...data.data) : 0;
    const min = data.data && data.data.length > 0 ? Math.min(...data.data) : 0;

    summaryData.push([]); // Empty row
    summaryData.push(["Data Summary", ""]);
    summaryData.push(["Total Value", total]);
    summaryData.push(["Average Value", Math.round(average * 100) / 100]);
    summaryData.push(["Maximum Value", max]);
    summaryData.push(["Minimum Value", min]);
    summaryData.push(["Data Points", data.data ? data.data.length : 0]);

    return summaryData;
  }

  /**
   * Set column widths for better readability
   */
  private static setColumnWidths(
    sheet: XLSX.WorkSheet,
    widths: number[]
  ): void {
    if (!sheet["!cols"]) {
      sheet["!cols"] = [];
    }

    widths.forEach((width, index) => {
      if (!sheet["!cols"]![index]) {
        sheet["!cols"]![index] = {};
      }
      sheet["!cols"]![index].wch = width;
    });
  }

  /**
   * Export multiple charts to a single Excel file
   */
  static exportMultipleChartsToExcel(
    charts: ChartExportData[],
    filename: string = "Analytics_Export"
  ): void {
    const workbook = XLSX.utils.book_new();

    // Add summary sheet with all charts overview
    const overviewData = this.prepareOverviewData(charts);
    const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
    XLSX.utils.book_append_sheet(workbook, overviewSheet, "Overview");

    // Add individual chart sheets
    charts.forEach((chartData, index) => {
      const chartSheetData = this.prepareChartData(chartData);
      const chartSheet = XLSX.utils.aoa_to_sheet(chartSheetData);
      const sheetName = `Chart_${index + 1}_${chartData.chartTitle
        .substring(0, 20)
        .replace(/[^a-zA-Z0-9]/g, "_")}`;
      XLSX.utils.book_append_sheet(workbook, chartSheet, sheetName);
    });

    // Locations sheet removed as per user request

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const finalFilename = `${filename}_${timestamp}.xlsx`;

    // Export the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    saveAs(blob, finalFilename);
  }

  /**
   * Prepare overview data for multiple charts export
   */
  private static prepareOverviewData(charts: ChartExportData[]): any[][] {
    const overviewData: any[][] = [];

    overviewData.push(["Analytics Export Overview"]);
    overviewData.push([]); // Empty row
    overviewData.push(["Export Date", new Date().toLocaleString()]);
    overviewData.push(["Number of Charts", charts.length]);

    if (charts.length > 0 && charts[0].dateRange) {
      overviewData.push([
        "Date Range",
        `${charts[0].dateRange.from} to ${charts[0].dateRange.to}`,
      ]);
    }

    overviewData.push([
      "Number of Locations",
      charts[0]?.selectedLocationIds.length || 0,
    ]);
    overviewData.push([]); // Empty row

    // Add chart summaries
    overviewData.push(["Chart Summaries", "", ""]);
    overviewData.push(["Chart Title", "Total Value", "Data Points"]);

    charts.forEach((chart) => {
      const total = chart.data.data
        ? chart.data.data.reduce(
            (sum: number, value: number) => sum + (value || 0),
            0
          )
        : 0;
      const dataPoints = chart.data.data ? chart.data.data.length : 0;
      overviewData.push([chart.chartTitle, total, dataPoints]);
    });

    return overviewData;
  }
}
