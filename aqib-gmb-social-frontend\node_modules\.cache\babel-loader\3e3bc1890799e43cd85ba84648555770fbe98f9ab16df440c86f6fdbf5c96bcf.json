{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport isHostComponent from '../isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}\nexport default appendOwnerState;", "map": {"version": 3, "names": ["_extends", "isHostComponent", "appendOwnerState", "elementType", "otherProps", "ownerState", "undefined"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/appendOwnerState/appendOwnerState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport isHostComponent from '../isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}\nexport default appendOwnerState;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,oBAAoB;;AAEhD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC7D,IAAIF,WAAW,KAAKG,SAAS,IAAIL,eAAe,CAACE,WAAW,CAAC,EAAE;IAC7D,OAAOC,UAAU;EACnB;EACA,OAAOJ,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,EAAE;IAC9BC,UAAU,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACC,UAAU,EAAEA,UAAU;EAC5D,CAAC,CAAC;AACJ;AACA,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}