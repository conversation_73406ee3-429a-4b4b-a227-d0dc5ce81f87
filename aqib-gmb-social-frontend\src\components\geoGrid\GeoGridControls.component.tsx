import React, { useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  InputAdornment,
} from "@mui/material";
import {
  Search as SearchIcon,
  LocationOn as LocationOnIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";
import {
  LocationSearchRequest,
  GridConfiguration,
} from "../../services/geoGrid/geoGrid.service";

interface GeoGridControlsProps {
  onLocationSearch: (searchRequest: LocationSearchRequest) => void;
  onGenerateGrid: () => void;
  onSaveConfiguration: () => void;
  loading: boolean;
  currentLocation: any;
  savedConfigurations: GridConfiguration[];
  onLoadConfiguration: (config: GridConfiguration) => void;
  onDeleteConfiguration: (configId: number) => void;
}

const GeoGridControls: React.FC<GeoGridControlsProps> = ({
  onLocationSearch,
  onGenerateGrid,
  onSaveConfiguration,
  loading,
  currentLocation,
  savedConfigurations,
  onLoadConfiguration,
  onDeleteConfiguration,
}) => {
  const [searchType, setSearchType] = useState<"name" | "coordinates" | "mapUrl">("name");
  const [searchQuery, setSearchQuery] = useState("");
  const [coordinates, setCoordinates] = useState({ lat: "", lng: "" });
  const [configName, setConfigName] = useState("");

  const handleSearch = () => {
    if (searchType === "name" && !searchQuery.trim()) {
      return;
    }
    
    if (searchType === "coordinates" && (!coordinates.lat || !coordinates.lng)) {
      return;
    }
    
    if (searchType === "mapUrl" && !searchQuery.trim()) {
      return;
    }

    const searchRequest: LocationSearchRequest = {
      searchType,
      query: searchQuery,
      coordinates: searchType === "coordinates" ? {
        lat: parseFloat(coordinates.lat),
        lng: parseFloat(coordinates.lng),
      } : undefined,
    };

    onLocationSearch(searchRequest);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  const renderSearchInput = () => {
    switch (searchType) {
      case "name":
        return (
          <TextField
            fullWidth
            label="Search Location"
            placeholder="e.g., New York, NY"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />
        );
      
      case "coordinates":
        return (
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Latitude"
              placeholder="40.7128"
              value={coordinates.lat}
              onChange={(e) => setCoordinates(prev => ({ ...prev, lat: e.target.value }))}
              type="number"
              sx={{ mb: 1 }}
            />
            <TextField
              fullWidth
              label="Longitude"
              placeholder="-74.0060"
              value={coordinates.lng}
              onChange={(e) => setCoordinates(prev => ({ ...prev, lng: e.target.value }))}
              type="number"
            />
          </Box>
        );
      
      case "mapUrl":
        return (
          <TextField
            fullWidth
            label="Google Maps URL"
            placeholder="https://maps.google.com/..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ mb: 2 }}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Search Location
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 2 }}>
        <FormLabel component="legend">Search Method</FormLabel>
        <RadioGroup
          row
          value={searchType}
          onChange={(e) => setSearchType(e.target.value as any)}
        >
          <FormControlLabel value="name" control={<Radio />} label="By Name" />
          <FormControlLabel value="coordinates" control={<Radio />} label="Coordinates" />
          <FormControlLabel value="mapUrl" control={<Radio />} label="Map URL" />
        </RadioGroup>
      </FormControl>

      {renderSearchInput()}

      <LoadingButton
        fullWidth
        variant="contained"
        onClick={handleSearch}
        loading={loading}
        startIcon={<SearchIcon />}
        sx={{ mb: 3 }}
      >
        Search & Generate Grid
      </LoadingButton>

      {currentLocation && (
        <Box sx={{ mb: 3 }}>
          <Chip
            icon={<LocationOnIcon />}
            label={`${currentLocation.name} (${currentLocation.lat.toFixed(4)}, ${currentLocation.lng.toFixed(4)})`}
            color="primary"
            variant="outlined"
            sx={{ mb: 2, maxWidth: "100%" }}
          />
          
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={onGenerateGrid}
              startIcon={<RefreshIcon />}
              disabled={loading}
            >
              Regenerate
            </Button>
          </Box>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      <Typography variant="h6" gutterBottom>
        Save Configuration
      </Typography>

      <TextField
        fullWidth
        label="Configuration Name"
        placeholder="My Grid Configuration"
        value={configName}
        onChange={(e) => setConfigName(e.target.value)}
        sx={{ mb: 2 }}
      />

      <LoadingButton
        fullWidth
        variant="contained"
        onClick={onSaveConfiguration}
        loading={loading}
        startIcon={<SaveIcon />}
        disabled={!configName.trim() || !currentLocation}
        sx={{ mb: 3 }}
      >
        Save Configuration
      </LoadingButton>

      <Divider sx={{ my: 2 }} />

      <Typography variant="h6" gutterBottom>
        Saved Configurations
      </Typography>

      {savedConfigurations.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No saved configurations yet
        </Typography>
      ) : (
        <List dense>
          {savedConfigurations.map((config) => (
            <ListItem key={config.id} divider>
              <ListItemText
                primary={config.name}
                secondary={`${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  onClick={() => onLoadConfiguration(config)}
                  disabled={loading}
                  size="small"
                >
                  <DownloadIcon />
                </IconButton>
                <IconButton
                  edge="end"
                  onClick={() => config.id && onDeleteConfiguration(config.id)}
                  disabled={loading}
                  size="small"
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );
};

export default GeoGridControls;
