import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  InputAdornment,
  Autocomplete,
  CircularProgress,
} from "@mui/material";
import {
  Search as SearchIcon,
  LocationOn as LocationOnIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";
import {
  LocationSearchRequest,
  GridConfiguration,
} from "../../services/geoGrid/geoGrid.service";
import {
  loadGoogleMapsAPI,
  isGoogleMapsAPIAvailable,
  getAutocompletePredictions,
} from "../../utils/googleMaps.utils";

interface GeoGridControlsProps {
  onLocationSearch: (searchRequest: LocationSearchRequest) => void;
  onGenerateGrid: () => void;
  onSaveConfiguration: () => void;
  loading: boolean;
  currentLocation: any;
  savedConfigurations: GridConfiguration[];
  onLoadConfiguration: (config: GridConfiguration) => void;
  onDeleteConfiguration: (configId: number) => void;
}

const GeoGridControls: React.FC<GeoGridControlsProps> = ({
  onLocationSearch,
  onGenerateGrid,
  onSaveConfiguration,
  loading,
  currentLocation,
  savedConfigurations,
  onLoadConfiguration,
  onDeleteConfiguration,
}) => {
  const [searchType, setSearchType] = useState<
    "name" | "coordinates" | "mapUrl"
  >("name");
  const [searchQuery, setSearchQuery] = useState("");
  const [coordinates, setCoordinates] = useState({ lat: "", lng: "" });
  const [configName, setConfigName] = useState("");

  // Location suggestions state
  const [locationSuggestions, setLocationSuggestions] = useState<any[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load Google Maps API on component mount
  useEffect(() => {
    loadGoogleMapsAPI().catch((error) => {
      console.warn("Failed to load Google Maps API:", error);
    });
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Location suggestions functionality
  const fetchLocationSuggestions = async (query: string) => {
    if (!query || query.length < 2) {
      setLocationSuggestions([]);
      return;
    }

    setLoadingSuggestions(true);

    try {
      // Try Google Places API first (if available)
      if (isGoogleMapsAPIAvailable()) {
        const suggestions = await getAutocompletePredictions(query, {
          types: ["(cities)"],
          componentRestrictions: { country: "us" }, // You can modify this or make it configurable
        });
        setLocationSuggestions(suggestions);
      } else {
        // Fallback to static suggestions if Google Places API is not available
        setLocationSuggestions(getStaticSuggestions(query));
      }
    } catch (error) {
      console.error("Error fetching location suggestions:", error);
      setLocationSuggestions(getStaticSuggestions(query));
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Static fallback suggestions
  const getStaticSuggestions = (query: string) => {
    const staticLocations = [
      { id: "1", name: "New York, NY, USA", lat: 40.7128, lng: -74.006 },
      { id: "2", name: "Los Angeles, CA, USA", lat: 34.0522, lng: -118.2437 },
      { id: "3", name: "Chicago, IL, USA", lat: 41.8781, lng: -87.6298 },
      { id: "4", name: "Houston, TX, USA", lat: 29.7604, lng: -95.3698 },
      { id: "5", name: "Phoenix, AZ, USA", lat: 33.4484, lng: -112.074 },
      { id: "6", name: "Philadelphia, PA, USA", lat: 39.9526, lng: -75.1652 },
      { id: "7", name: "San Antonio, TX, USA", lat: 29.4241, lng: -98.4936 },
      { id: "8", name: "San Diego, CA, USA", lat: 32.7157, lng: -117.1611 },
      { id: "9", name: "Dallas, TX, USA", lat: 32.7767, lng: -96.797 },
      { id: "10", name: "San Jose, CA, USA", lat: 37.3382, lng: -121.8863 },
      { id: "11", name: "Austin, TX, USA", lat: 30.2672, lng: -97.7431 },
      { id: "12", name: "Jacksonville, FL, USA", lat: 30.3322, lng: -81.6557 },
      { id: "13", name: "Fort Worth, TX, USA", lat: 32.7555, lng: -97.3308 },
      { id: "14", name: "Columbus, OH, USA", lat: 39.9612, lng: -82.9988 },
      { id: "15", name: "Charlotte, NC, USA", lat: 35.2271, lng: -80.8431 },
      {
        id: "16",
        name: "San Francisco, CA, USA",
        lat: 37.7749,
        lng: -122.4194,
      },
      { id: "17", name: "Indianapolis, IN, USA", lat: 39.7684, lng: -86.1581 },
      { id: "18", name: "Seattle, WA, USA", lat: 47.6062, lng: -122.3321 },
      { id: "19", name: "Denver, CO, USA", lat: 39.7392, lng: -104.9903 },
      { id: "20", name: "Boston, MA, USA", lat: 42.3601, lng: -71.0589 },
    ];

    return staticLocations
      .filter((location) =>
        location.name.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 5);
  };

  // Handle input change with debouncing
  const handleLocationInputChange = (value: string) => {
    setSearchQuery(value);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debouncing
    searchTimeoutRef.current = setTimeout(() => {
      fetchLocationSuggestions(value);
    }, 300);
  };

  // Handle location selection from autocomplete
  const handleLocationSelect = (location: any) => {
    if (location) {
      setSelectedLocation(location);
      setSearchQuery(location.name);

      // If it's a static suggestion with coordinates, use them directly
      if (location.lat && location.lng) {
        const searchRequest: LocationSearchRequest = {
          searchType: "coordinates",
          coordinates: {
            lat: location.lat,
            lng: location.lng,
          },
        };
        onLocationSearch(searchRequest);
      } else {
        // For Google Places API results, search by name
        const searchRequest: LocationSearchRequest = {
          searchType: "name",
          query: location.name,
        };
        onLocationSearch(searchRequest);
      }
    }
  };

  const handleSearch = () => {
    if (searchType === "name" && !searchQuery.trim()) {
      return;
    }

    if (
      searchType === "coordinates" &&
      (!coordinates.lat || !coordinates.lng)
    ) {
      return;
    }

    if (searchType === "mapUrl" && !searchQuery.trim()) {
      return;
    }

    const searchRequest: LocationSearchRequest = {
      searchType,
      query: searchQuery,
      coordinates:
        searchType === "coordinates"
          ? {
              lat: parseFloat(coordinates.lat),
              lng: parseFloat(coordinates.lng),
            }
          : undefined,
    };

    onLocationSearch(searchRequest);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  const renderSearchInput = () => {
    switch (searchType) {
      case "name":
        return (
          <Autocomplete
            fullWidth
            options={locationSuggestions}
            getOptionLabel={(option) => option.name || ""}
            value={selectedLocation}
            onChange={(event, newValue) => handleLocationSelect(newValue)}
            inputValue={searchQuery}
            onInputChange={(event, newInputValue) => {
              handleLocationInputChange(newInputValue);
            }}
            loading={loadingSuggestions}
            loadingText="Searching locations..."
            noOptionsText="No locations found"
            renderInput={(params) => (
              <TextField
                {...params}
                label="Search Location"
                placeholder="e.g., New York, NY"
                onKeyPress={handleKeyPress}
                InputProps={{
                  ...params.InputProps,
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <>
                      {loadingSuggestions ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <LocationOnIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Box>
                  <Typography variant="body2">{option.name}</Typography>
                  {option.types && (
                    <Typography variant="caption" color="text.secondary">
                      {option.types.join(", ")}
                    </Typography>
                  )}
                </Box>
              </Box>
            )}
            sx={{ mb: 2 }}
          />
        );

      case "coordinates":
        return (
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Latitude"
              placeholder="40.7128"
              value={coordinates.lat}
              onChange={(e) =>
                setCoordinates((prev) => ({ ...prev, lat: e.target.value }))
              }
              type="number"
              sx={{ mb: 1 }}
            />
            <TextField
              fullWidth
              label="Longitude"
              placeholder="-74.0060"
              value={coordinates.lng}
              onChange={(e) =>
                setCoordinates((prev) => ({ ...prev, lng: e.target.value }))
              }
              type="number"
            />
          </Box>
        );

      case "mapUrl":
        return (
          <TextField
            fullWidth
            label="Google Maps URL"
            placeholder="https://maps.google.com/..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ mb: 2 }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Search Location
      </Typography>

      <FormControl component="fieldset" sx={{ mb: 2 }}>
        <FormLabel component="legend">Search Method</FormLabel>
        <RadioGroup
          row
          value={searchType}
          onChange={(e) => setSearchType(e.target.value as any)}
        >
          <FormControlLabel value="name" control={<Radio />} label="By Name" />
          <FormControlLabel
            value="coordinates"
            control={<Radio />}
            label="Coordinates"
          />
          <FormControlLabel
            value="mapUrl"
            control={<Radio />}
            label="Map URL"
          />
        </RadioGroup>
      </FormControl>

      {renderSearchInput()}

      <LoadingButton
        fullWidth
        variant="contained"
        onClick={handleSearch}
        loading={loading}
        startIcon={<SearchIcon />}
        sx={{ mb: 3 }}
      >
        Search & Generate Grid
      </LoadingButton>

      {currentLocation && (
        <Box sx={{ mb: 3 }}>
          <Chip
            icon={<LocationOnIcon />}
            label={`${currentLocation.name} (${currentLocation.lat.toFixed(
              4
            )}, ${currentLocation.lng.toFixed(4)})`}
            color="primary"
            variant="outlined"
            sx={{ mb: 2, maxWidth: "100%" }}
          />

          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={onGenerateGrid}
              startIcon={<RefreshIcon />}
              disabled={loading}
            >
              Regenerate
            </Button>
          </Box>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      <Typography variant="h6" gutterBottom>
        Save Configuration
      </Typography>

      <TextField
        fullWidth
        label="Configuration Name"
        placeholder="My Grid Configuration"
        value={configName}
        onChange={(e) => setConfigName(e.target.value)}
        sx={{ mb: 2 }}
      />

      <LoadingButton
        fullWidth
        variant="contained"
        onClick={onSaveConfiguration}
        loading={loading}
        startIcon={<SaveIcon />}
        disabled={!configName.trim() || !currentLocation}
        sx={{ mb: 3 }}
      >
        Save Configuration
      </LoadingButton>

      <Divider sx={{ my: 2 }} />

      <Typography variant="h6" gutterBottom>
        Saved Configurations
      </Typography>

      {savedConfigurations.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No saved configurations yet
        </Typography>
      ) : (
        <List dense>
          {savedConfigurations.map((config) => (
            <ListItem key={config.id} divider>
              <ListItemText
                primary={config.name}
                secondary={`${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  onClick={() => onLoadConfiguration(config)}
                  disabled={loading}
                  size="small"
                >
                  <DownloadIcon />
                </IconButton>
                <IconButton
                  edge="end"
                  onClick={() => config.id && onDeleteConfiguration(config.id)}
                  disabled={loading}
                  size="small"
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );
};

export default GeoGridControls;
