{"ast": null, "code": "'use strict';\n\nvar fnToStr = Function.prototype.toString;\nvar reflectApply = typeof Reflect === 'object' && Reflect !== null && Reflect.apply;\nvar badArrayLike;\nvar isCallableMarker;\nif (typeof reflectApply === 'function' && typeof Object.defineProperty === 'function') {\n  try {\n    badArrayLike = Object.defineProperty({}, 'length', {\n      get: function () {\n        throw isCallableMarker;\n      }\n    });\n    isCallableMarker = {};\n    // eslint-disable-next-line no-throw-literal\n    reflectApply(function () {\n      throw 42;\n    }, null, badArrayLike);\n  } catch (_) {\n    if (_ !== isCallableMarker) {\n      reflectApply = null;\n    }\n  }\n} else {\n  reflectApply = null;\n}\nvar constructorRegex = /^\\s*class\\b/;\nvar isES6ClassFn = function isES6ClassFunction(value) {\n  try {\n    var fnStr = fnToStr.call(value);\n    return constructorRegex.test(fnStr);\n  } catch (e) {\n    return false; // not a function\n  }\n};\nvar tryFunctionObject = function tryFunctionToStr(value) {\n  try {\n    if (isES6ClassFn(value)) {\n      return false;\n    }\n    fnToStr.call(value);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\nvar toStr = Object.prototype.toString;\nvar objectClass = '[object Object]';\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar ddaClass = '[object HTMLAllCollection]'; // IE 11\nvar ddaClass2 = '[object HTML document.all class]';\nvar ddaClass3 = '[object HTMLCollection]'; // IE 9-10\nvar hasToStringTag = typeof Symbol === 'function' && !!Symbol.toStringTag; // better: use `has-tostringtag`\n\nvar isIE68 = !(0 in [,]); // eslint-disable-line no-sparse-arrays, comma-spacing\n\nvar isDDA = function isDocumentDotAll() {\n  return false;\n};\nif (typeof document === 'object') {\n  // Firefox 3 canonicalizes DDA to undefined when it's not accessed directly\n  var all = document.all;\n  if (toStr.call(all) === toStr.call(document.all)) {\n    isDDA = function isDocumentDotAll(value) {\n      /* globals document: false */\n      // in IE 6-8, typeof document.all is \"object\" and it's truthy\n      if ((isIE68 || !value) && (typeof value === 'undefined' || typeof value === 'object')) {\n        try {\n          var str = toStr.call(value);\n          return (str === ddaClass || str === ddaClass2 || str === ddaClass3 // opera 12.16\n          || str === objectClass // IE 6-8\n          ) && value('') == null; // eslint-disable-line eqeqeq\n        } catch (e) {/**/}\n      }\n      return false;\n    };\n  }\n}\nmodule.exports = reflectApply ? function isCallable(value) {\n  if (isDDA(value)) {\n    return true;\n  }\n  if (!value) {\n    return false;\n  }\n  if (typeof value !== 'function' && typeof value !== 'object') {\n    return false;\n  }\n  try {\n    reflectApply(value, null, badArrayLike);\n  } catch (e) {\n    if (e !== isCallableMarker) {\n      return false;\n    }\n  }\n  return !isES6ClassFn(value) && tryFunctionObject(value);\n} : function isCallable(value) {\n  if (isDDA(value)) {\n    return true;\n  }\n  if (!value) {\n    return false;\n  }\n  if (typeof value !== 'function' && typeof value !== 'object') {\n    return false;\n  }\n  if (hasToStringTag) {\n    return tryFunctionObject(value);\n  }\n  if (isES6ClassFn(value)) {\n    return false;\n  }\n  var strClass = toStr.call(value);\n  if (strClass !== fnClass && strClass !== genClass && !/^\\[object HTML/.test(strClass)) {\n    return false;\n  }\n  return tryFunctionObject(value);\n};", "map": {"version": 3, "names": ["fnToStr", "Function", "prototype", "toString", "reflectApply", "Reflect", "apply", "badArrayLike", "isCallableMarker", "Object", "defineProperty", "get", "_", "constructorRegex", "isES6ClassFn", "isES6ClassFunction", "value", "fnStr", "call", "test", "e", "tryFunctionObject", "tryFunctionToStr", "toStr", "objectClass", "fnClass", "genClass", "ddaClass", "ddaClass2", "ddaClass3", "hasToStringTag", "Symbol", "toStringTag", "isIE68", "isDDA", "isDocumentDotAll", "document", "all", "str", "module", "exports", "isCallable", "strClass"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/is-callable/index.js"], "sourcesContent": ["'use strict';\n\nvar fnToStr = Function.prototype.toString;\nvar reflectApply = typeof Reflect === 'object' && Reflect !== null && Reflect.apply;\nvar badArrayLike;\nvar isCallableMarker;\nif (typeof reflectApply === 'function' && typeof Object.defineProperty === 'function') {\n\ttry {\n\t\tbadArrayLike = Object.defineProperty({}, 'length', {\n\t\t\tget: function () {\n\t\t\t\tthrow isCallableMarker;\n\t\t\t}\n\t\t});\n\t\tisCallableMarker = {};\n\t\t// eslint-disable-next-line no-throw-literal\n\t\treflectApply(function () { throw 42; }, null, badArrayLike);\n\t} catch (_) {\n\t\tif (_ !== isCallableMarker) {\n\t\t\treflectApply = null;\n\t\t}\n\t}\n} else {\n\treflectApply = null;\n}\n\nvar constructorRegex = /^\\s*class\\b/;\nvar isES6ClassFn = function isES6ClassFunction(value) {\n\ttry {\n\t\tvar fnStr = fnToStr.call(value);\n\t\treturn constructorRegex.test(fnStr);\n\t} catch (e) {\n\t\treturn false; // not a function\n\t}\n};\n\nvar tryFunctionObject = function tryFunctionToStr(value) {\n\ttry {\n\t\tif (isES6ClassFn(value)) { return false; }\n\t\tfnToStr.call(value);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\nvar toStr = Object.prototype.toString;\nvar objectClass = '[object Object]';\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar ddaClass = '[object HTMLAllCollection]'; // IE 11\nvar ddaClass2 = '[object HTML document.all class]';\nvar ddaClass3 = '[object HTMLCollection]'; // IE 9-10\nvar hasToStringTag = typeof Symbol === 'function' && !!Symbol.toStringTag; // better: use `has-tostringtag`\n\nvar isIE68 = !(0 in [,]); // eslint-disable-line no-sparse-arrays, comma-spacing\n\nvar isDDA = function isDocumentDotAll() { return false; };\nif (typeof document === 'object') {\n\t// Firefox 3 canonicalizes DDA to undefined when it's not accessed directly\n\tvar all = document.all;\n\tif (toStr.call(all) === toStr.call(document.all)) {\n\t\tisDDA = function isDocumentDotAll(value) {\n\t\t\t/* globals document: false */\n\t\t\t// in IE 6-8, typeof document.all is \"object\" and it's truthy\n\t\t\tif ((isIE68 || !value) && (typeof value === 'undefined' || typeof value === 'object')) {\n\t\t\t\ttry {\n\t\t\t\t\tvar str = toStr.call(value);\n\t\t\t\t\treturn (\n\t\t\t\t\t\tstr === ddaClass\n\t\t\t\t\t\t|| str === ddaClass2\n\t\t\t\t\t\t|| str === ddaClass3 // opera 12.16\n\t\t\t\t\t\t|| str === objectClass // IE 6-8\n\t\t\t\t\t) && value('') == null; // eslint-disable-line eqeqeq\n\t\t\t\t} catch (e) { /**/ }\n\t\t\t}\n\t\t\treturn false;\n\t\t};\n\t}\n}\n\nmodule.exports = reflectApply\n\t? function isCallable(value) {\n\t\tif (isDDA(value)) { return true; }\n\t\tif (!value) { return false; }\n\t\tif (typeof value !== 'function' && typeof value !== 'object') { return false; }\n\t\ttry {\n\t\t\treflectApply(value, null, badArrayLike);\n\t\t} catch (e) {\n\t\t\tif (e !== isCallableMarker) { return false; }\n\t\t}\n\t\treturn !isES6ClassFn(value) && tryFunctionObject(value);\n\t}\n\t: function isCallable(value) {\n\t\tif (isDDA(value)) { return true; }\n\t\tif (!value) { return false; }\n\t\tif (typeof value !== 'function' && typeof value !== 'object') { return false; }\n\t\tif (hasToStringTag) { return tryFunctionObject(value); }\n\t\tif (isES6ClassFn(value)) { return false; }\n\t\tvar strClass = toStr.call(value);\n\t\tif (strClass !== fnClass && strClass !== genClass && !(/^\\[object HTML/).test(strClass)) { return false; }\n\t\treturn tryFunctionObject(value);\n\t};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,QAAQ,CAACC,SAAS,CAACC,QAAQ;AACzC,IAAIC,YAAY,GAAG,OAAOC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACC,KAAK;AACnF,IAAIC,YAAY;AAChB,IAAIC,gBAAgB;AACpB,IAAI,OAAOJ,YAAY,KAAK,UAAU,IAAI,OAAOK,MAAM,CAACC,cAAc,KAAK,UAAU,EAAE;EACtF,IAAI;IACHH,YAAY,GAAGE,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE;MAClDC,GAAG,EAAE,SAAAA,CAAA,EAAY;QAChB,MAAMH,gBAAgB;MACvB;IACD,CAAC,CAAC;IACFA,gBAAgB,GAAG,CAAC,CAAC;IACrB;IACAJ,YAAY,CAAC,YAAY;MAAE,MAAM,EAAE;IAAE,CAAC,EAAE,IAAI,EAAEG,YAAY,CAAC;EAC5D,CAAC,CAAC,OAAOK,CAAC,EAAE;IACX,IAAIA,CAAC,KAAKJ,gBAAgB,EAAE;MAC3BJ,YAAY,GAAG,IAAI;IACpB;EACD;AACD,CAAC,MAAM;EACNA,YAAY,GAAG,IAAI;AACpB;AAEA,IAAIS,gBAAgB,GAAG,aAAa;AACpC,IAAIC,YAAY,GAAG,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACrD,IAAI;IACH,IAAIC,KAAK,GAAGjB,OAAO,CAACkB,IAAI,CAACF,KAAK,CAAC;IAC/B,OAAOH,gBAAgB,CAACM,IAAI,CAACF,KAAK,CAAC;EACpC,CAAC,CAAC,OAAOG,CAAC,EAAE;IACX,OAAO,KAAK,CAAC,CAAC;EACf;AACD,CAAC;AAED,IAAIC,iBAAiB,GAAG,SAASC,gBAAgBA,CAACN,KAAK,EAAE;EACxD,IAAI;IACH,IAAIF,YAAY,CAACE,KAAK,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IACzChB,OAAO,CAACkB,IAAI,CAACF,KAAK,CAAC;IACnB,OAAO,IAAI;EACZ,CAAC,CAAC,OAAOI,CAAC,EAAE;IACX,OAAO,KAAK;EACb;AACD,CAAC;AACD,IAAIG,KAAK,GAAGd,MAAM,CAACP,SAAS,CAACC,QAAQ;AACrC,IAAIqB,WAAW,GAAG,iBAAiB;AACnC,IAAIC,OAAO,GAAG,mBAAmB;AACjC,IAAIC,QAAQ,GAAG,4BAA4B;AAC3C,IAAIC,QAAQ,GAAG,4BAA4B,CAAC,CAAC;AAC7C,IAAIC,SAAS,GAAG,kCAAkC;AAClD,IAAIC,SAAS,GAAG,yBAAyB,CAAC,CAAC;AAC3C,IAAIC,cAAc,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,CAAC,CAACA,MAAM,CAACC,WAAW,CAAC,CAAC;;AAE3E,IAAIC,MAAM,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;;AAE1B,IAAIC,KAAK,GAAG,SAASC,gBAAgBA,CAAA,EAAG;EAAE,OAAO,KAAK;AAAE,CAAC;AACzD,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;EACjC;EACA,IAAIC,GAAG,GAAGD,QAAQ,CAACC,GAAG;EACtB,IAAId,KAAK,CAACL,IAAI,CAACmB,GAAG,CAAC,KAAKd,KAAK,CAACL,IAAI,CAACkB,QAAQ,CAACC,GAAG,CAAC,EAAE;IACjDH,KAAK,GAAG,SAASC,gBAAgBA,CAACnB,KAAK,EAAE;MACxC;MACA;MACA,IAAI,CAACiB,MAAM,IAAI,CAACjB,KAAK,MAAM,OAAOA,KAAK,KAAK,WAAW,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC,EAAE;QACtF,IAAI;UACH,IAAIsB,GAAG,GAAGf,KAAK,CAACL,IAAI,CAACF,KAAK,CAAC;UAC3B,OAAO,CACNsB,GAAG,KAAKX,QAAQ,IACbW,GAAG,KAAKV,SAAS,IACjBU,GAAG,KAAKT,SAAS,CAAC;UAAA,GAClBS,GAAG,KAAKd,WAAW,CAAC;UAAA,KACnBR,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAE;MACf;MACA,OAAO,KAAK;IACb,CAAC;EACF;AACD;AAEAmB,MAAM,CAACC,OAAO,GAAGpC,YAAY,GAC1B,SAASqC,UAAUA,CAACzB,KAAK,EAAE;EAC5B,IAAIkB,KAAK,CAAClB,KAAK,CAAC,EAAE;IAAE,OAAO,IAAI;EAAE;EACjC,IAAI,CAACA,KAAK,EAAE;IAAE,OAAO,KAAK;EAAE;EAC5B,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAC9E,IAAI;IACHZ,YAAY,CAACY,KAAK,EAAE,IAAI,EAAET,YAAY,CAAC;EACxC,CAAC,CAAC,OAAOa,CAAC,EAAE;IACX,IAAIA,CAAC,KAAKZ,gBAAgB,EAAE;MAAE,OAAO,KAAK;IAAE;EAC7C;EACA,OAAO,CAACM,YAAY,CAACE,KAAK,CAAC,IAAIK,iBAAiB,CAACL,KAAK,CAAC;AACxD,CAAC,GACC,SAASyB,UAAUA,CAACzB,KAAK,EAAE;EAC5B,IAAIkB,KAAK,CAAClB,KAAK,CAAC,EAAE;IAAE,OAAO,IAAI;EAAE;EACjC,IAAI,CAACA,KAAK,EAAE;IAAE,OAAO,KAAK;EAAE;EAC5B,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAC9E,IAAIc,cAAc,EAAE;IAAE,OAAOT,iBAAiB,CAACL,KAAK,CAAC;EAAE;EACvD,IAAIF,YAAY,CAACE,KAAK,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EACzC,IAAI0B,QAAQ,GAAGnB,KAAK,CAACL,IAAI,CAACF,KAAK,CAAC;EAChC,IAAI0B,QAAQ,KAAKjB,OAAO,IAAIiB,QAAQ,KAAKhB,QAAQ,IAAI,CAAE,gBAAgB,CAAEP,IAAI,CAACuB,QAAQ,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EACzG,OAAOrB,iBAAiB,CAACL,KAAK,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}