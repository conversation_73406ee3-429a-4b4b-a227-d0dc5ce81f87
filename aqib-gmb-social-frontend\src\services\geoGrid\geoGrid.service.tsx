import { Dispatch } from "react";
import { Action } from "redux";
import HttpHelperService from "../httpHelper.service";
import {
  GEO_GRID_SEARCH_LOCATION,
  GEO_GRID_GENERATE_GRID,
  GEO_GRID_GET_DATA,
  GEO_GRID_SAVE_CONFIG,
  GEO_GRID_GET_CONFIGS,
  GEO_GRID_UPDATE_CONFIG,
  GEO_GRID_DELETE_CONFIG,
  GEO_GRID_LOCATION_SUGGESTIONS,
  GEO_GRID_VALIDATE_COORDINATES,
} from "../../constants/endPoints.constant";

export interface LocationSearchRequest {
  searchType: "name" | "coordinates" | "mapUrl";
  query?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface GridGenerationRequest {
  centerLat: number;
  centerLng: number;
  gridSize: string;
  distance: number;
  distanceUnit: "meters" | "miles" | "kilometers";
}

export interface GridConfiguration {
  id?: number;
  userId?: number;
  name: string;
  centerLat: number;
  centerLng: number;
  gridSize: string;
  distance: number;
  distanceUnit: "meters" | "miles" | "kilometers";
  searchType: "name" | "coordinates" | "mapUrl";
  searchQuery?: string;
  isScheduleEnabled: boolean;
  settings?: any;
  gridPoints?: GridPoint[];
}

export interface GridPoint {
  lat: number;
  lng: number;
  index: number;
  gridPosition: {
    row: number;
    col: number;
  };
  address?: string;
  placeId?: string;
}

export interface LocationSuggestion {
  name: string;
  placeId: string;
  lat: number;
  lng: number;
}

class GeoGridService {
  private _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Search for location using various methods
   */
  searchLocation = async (searchRequest: LocationSearchRequest) => {
    return await this._httpHelperService.post(
      GEO_GRID_SEARCH_LOCATION,
      searchRequest
    );
  };

  /**
   * Generate grid points based on center location and configuration
   */
  generateGrid = async (gridRequest: GridGenerationRequest) => {
    return await this._httpHelperService.post(
      GEO_GRID_GENERATE_GRID,
      gridRequest
    );
  };

  /**
   * Get grid data by ID
   */
  getGridData = async (gridId: string) => {
    return await this._httpHelperService.get(GEO_GRID_GET_DATA(gridId));
  };

  /**
   * Save grid configuration
   */
  saveGridConfiguration = async (configuration: GridConfiguration) => {
    return await this._httpHelperService.post(
      GEO_GRID_SAVE_CONFIG,
      configuration
    );
  };

  /**
   * Get all grid configurations for a user
   */
  getGridConfigurations = async (userId: number) => {
    return await this._httpHelperService.get(GEO_GRID_GET_CONFIGS(userId));
  };

  /**
   * Update grid configuration
   */
  updateGridConfiguration = async (
    gridId: string,
    configuration: Partial<GridConfiguration>
  ) => {
    return await this._httpHelperService.put(
      GEO_GRID_UPDATE_CONFIG(gridId),
      configuration
    );
  };

  /**
   * Delete grid configuration
   */
  deleteGridConfiguration = async (gridId: string) => {
    return await this._httpHelperService.delete(GEO_GRID_DELETE_CONFIG(gridId));
  };

  /**
   * Get location suggestions for autocomplete
   */
  getLocationSuggestions = async (query: string) => {
    return await this._httpHelperService.get(
      `${GEO_GRID_LOCATION_SUGGESTIONS}?query=${encodeURIComponent(query)}`
    );
  };

  /**
   * Validate coordinates
   */
  validateCoordinates = async (lat: number, lng: number) => {
    return await this._httpHelperService.post(GEO_GRID_VALIDATE_COORDINATES, {
      lat,
      lng,
    });
  };
}

export default GeoGridService;
