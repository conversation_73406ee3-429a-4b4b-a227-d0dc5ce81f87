{"ast": null, "code": "import * as React from 'react';\nexport const PopupContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  PopupContext.displayName = 'PopupContext';\n}", "map": {"version": 3, "names": ["React", "PopupContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/Unstable_Popup/PopupContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const PopupContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  PopupContext.displayName = 'PopupContext';\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAClE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,YAAY,CAACK,WAAW,GAAG,cAAc;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}