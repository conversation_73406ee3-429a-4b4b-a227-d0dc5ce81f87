import { FunctionComponent } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Typography } from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { RegisteredEmployeesChart } from "../../components/charts/registeredEmployees.charts";
import { ActiveJobsChart } from "../../components/charts/activeJobs.charts";
import { PieChart } from "../../components/charts/pie.charts";

//Icons
import GroupIcon from "@mui/icons-material/Group";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import ScheduleIcon from "@mui/icons-material/Schedule";
import EditLocationAltIcon from "@mui/icons-material/EditLocationAlt";
import HelpIcon from "@mui/icons-material/Help";
import { useSelector } from "react-redux";
import ArrowUpwardRoundedIcon from "@mui/icons-material/ArrowUpwardRounded";
import ArrowDownwardRoundedIcon from "@mui/icons-material/ArrowDownwardRounded";

//Css Import
import "../dashboard/dashboard.screen.style.css";
import HomeChartCard from "../../components/homeChartCard/homeChartCard.component";
import RevenueChartDashboard from "../../components/revenueChartDashboard/revenueChartDashboard.component";

const Dashboard: FunctionComponent<PageProps> = ({ title }) => {
  const { userInfo } = useSelector((state: any) => state.authReducer);

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              <Box>
                <Box sx={{ marginBottom: "5px" }}>
                  <h3 className="pageTitle">Dashboard</h3>
                  <Typography variant="subtitle2" className="subtitle2">
                    Hi, {userInfo && userInfo.name}. Welcome back to MyLocoBiz!
                  </Typography>
                </Box>
                <Box>
                  <Grid
                    container
                    spacing={2}
                    className="commonCardBottomSpacing"
                  >
                    <Grid item xs={12} md={4} lg={4}>
                      <Box className="commonCard dashboardTopIconCard">
                        <Box className="dashboardTopIcon">
                          <img
                            alt="MyLocoBiz - Logo"
                            className="width100"
                            src={require("../../assets/dashboard/icon1.png")}
                          />
                        </Box>
                        <Box className="dashboardTopInfo">
                          <Typography className="dashboardTopCount">
                            75
                          </Typography>
                          <Typography className="dashboardTopTitle">
                            Total Reviews
                          </Typography>
                          <Box>
                            <Typography className="d-flex">
                              <span className="dashboardTopStatusTypeSucess">
                                <ArrowUpwardRoundedIcon />
                              </span>
                              <span className="dashboardTopStatus">
                                4% (30 days)
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Box className="commonCard dashboardTopIconCard">
                        <Box className="dashboardTopIcon">
                          <img
                            alt="MyLocoBiz - Logo"
                            className="width100"
                            src={require("../../assets/dashboard/icon2.png")}
                          />
                        </Box>
                        <Box className="dashboardTopInfo">
                          <Typography className="dashboardTopCount">
                            357
                          </Typography>
                          <Typography className="dashboardTopTitle">
                            Total Posts
                          </Typography>
                          <Box>
                            <Typography className="d-flex">
                              <span className="dashboardTopStatusTypeSucess">
                                <ArrowUpwardRoundedIcon />
                              </span>
                              <span className="dashboardTopStatus">
                                4% (30 days)
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4} lg={4}>
                      <Box className="commonCard dashboardTopIconCard">
                        <Box className="dashboardTopIcon">
                          <img
                            alt="MyLocoBiz - Logo"
                            className="width100"
                            src={require("../../assets/dashboard/icon3.png")}
                          />
                        </Box>
                        <Box className="dashboardTopInfo">
                          <Typography className="dashboardTopCount">
                            65
                          </Typography>
                          <Typography className="dashboardTopTitle">
                            Total Users
                          </Typography>
                          <Box>
                            <Typography className="d-flex">
                              <span className="dashboardTopStatusTypeDanger">
                                <ArrowDownwardRoundedIcon />
                              </span>
                              <span className="dashboardTopStatus">
                                25% (30 days)
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>

                  <Grid
                    container
                    spacing={2}
                    className="commonCardBottomSpacing"
                  >
                    <Grid item xs={12} md={6} lg={6}>
                      <Box className="commonCard height100 pieChartDiv">
                        <HomeChartCard />
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6} lg={6}>
                      <Box className="commonCard">
                        {/* <RevenueChartDashboard /> */}
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
                <Box className="pageRight height100">
                  <Box className="pageBody commonPageScroll ">
                    <Box>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={12} lg={8}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon greenBgCcolor">
                                  <GroupIcon />
                                </span>
                                <span className="cardTitle">
                                  Users Registered
                                </span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox employeesRegistered">
                                <RegisteredEmployeesChart chartData={null} />
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={12} lg={4}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon violetBgCcolor">
                                  <ScheduleIcon />
                                </span>
                                <span className="cardTitle">
                                  Post Schedules
                                </span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox schedules">
                                <PieChart chartData={null} />
                                {/* <PolarChart chartData={null} /> */}
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6} lg={3}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon darkBgCcolor">
                                  <WorkHistoryIcon />
                                </span>
                                <span className="cardTitle">Business</span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox">
                                <ActiveJobsChart
                                  chartData={[53, 59, 77, 82, 90, 67, 45]}
                                  label="Synced"
                                />
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6} lg={3}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon orangeBgCcolor">
                                  <EditLocationAltIcon />
                                </span>
                                <span className="cardTitle">Locations</span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox">
                                <ActiveJobsChart
                                  chartData={[84, 58, 70, 57, 43, 50, 64]}
                                  label="Synced"
                                />
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6} lg={3}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon yellowBgCcolor">
                                  <EventAvailableIcon />
                                </span>
                                <span className="cardTitle">Reviews</span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox">
                                <ActiveJobsChart
                                  chartData={[69, 67, 49, 61, 78, 73, 40]}
                                  label="Synced"
                                />
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6} lg={3}>
                          <Box className="commonCard">
                            <Box className="commonCardHead">
                              <Box className="cardTitleIcon">
                                <span className="cardIcon yellowBgCcolor">
                                  <HelpIcon />
                                </span>
                                <span className="cardTitle">Q&A</span>
                              </Box>
                              {/* <Box className="cardCount">2,200</Box> */}
                            </Box>
                            <Box className="commonCardBody">
                              <Box className="GraphBox">
                                <ActiveJobsChart
                                  chartData={[61, 78, 73, 40, 69, 67, 49]}
                                  label="Synced"
                                />
                              </Box>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default Dashboard;
