import React from "react";
import { Card, CardContent, Typography, Box } from "@mui/material";
import TipsAndUpdatesOutlinedIcon from "@mui/icons-material/TipsAndUpdatesOutlined";
import { theme } from "../../../theme";

const InfoCard = () => {
  const cards = [
    {
      title: "Adding Posts",
      description:
        "Frequency of Posts & content do impact your ranking & can drive upto 5% more impressions",
      key: 0,
    },
    {
      title: "Adding Offers",
      description:
        'Uploading posts as an offers gives more visibility as it comes under the "Deals" section on Google Search',
      key: 1,
    },
    {
      title: "Post Offers",
      description:
        "Recommended to add Offers as they have higher visibility on Profiles & drive more engagement",
      key: 2,
    },
    {
      title: "Measuring Post Clicks",
      description:
        "Google doesn't allow phone numbers & URLs in Post & Offer Descriptions. Read More",
      key: 3,
    },
    {
      title: "Reasons for Post Rejections",
      description:
        "Google doesn't allow phone numbers & URLs in Post & Offer Descriptions.",
      key: 4,
    },
    {
      title: "Competitor Comparison",
      description:
        "5 out of your top 10 Competitors have a Post in last 30 Days",
      key: 5,
    },
    {
      title: "Emojis In Title",
      description:
        "Using Emojis in the Post Title gives more engagement to your post/offer",
      key: 6,
    },
  ];

  const InfoCardTemplate = (props: {
    setKey: number;
    title: string;
    description: string;
  }) => {
    return (
      <Card
        key={props.setKey}
        elevation={3}
        sx={{
          borderRadius: 2,
          p: 2,
          mb: 2,
          display: "flex",
          alignItems: "flex-start",
          gap: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: 40,
            height: 40,
            borderRadius: "50%",
          }}
        >
          <TipsAndUpdatesOutlinedIcon
            sx={{ color: theme.palette.secondary?.main }}
          />
        </Box>
        <CardContent key={"Infocard" + props.setKey} sx={{ padding: 0 }}>
          <Typography
            key={"title" + props.setKey}
            variant="h6"
            sx={{ fontWeight: 600 }}
            gutterBottom
          >
            {props.title}
          </Typography>
          <Typography
            key={"subtitle" + props.setKey}
            variant="body2"
            color="text.secondary"
          >
            {props.description}
          </Typography>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ maxWidth: "100%", margin: "10 auto", padding: 0, marginTop: 2 }}>
      {cards.map((card, index: number) => (
        <InfoCardTemplate
          setKey={card.key}
          title={card.title}
          description={card.description}
        />
      ))}
    </Box>
  );
};

export default InfoCard;
