{"ast": null, "code": "'use strict';\n\n/** @typedef {`$${import('.').InternalSlot}`} SaltedInternalSlot */\n/** @typedef {{ [k in SaltedInternalSlot]?: unknown }} SlotsObject */\nvar hasOwn = require('hasown');\n/** @type {import('side-channel').Channel<object, SlotsObject>} */\nvar channel = require('side-channel')();\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nvar SLOT = {\n  assert: function (O, slot) {\n    if (!O || typeof O !== 'object' && typeof O !== 'function') {\n      throw new $TypeError('`O` is not an object');\n    }\n    if (typeof slot !== 'string') {\n      throw new $TypeError('`slot` must be a string');\n    }\n    channel.assert(O);\n    if (!SLOT.has(O, slot)) {\n      throw new $TypeError('`' + slot + '` is not present on `O`');\n    }\n  },\n  get: function (O, slot) {\n    if (!O || typeof O !== 'object' && typeof O !== 'function') {\n      throw new $TypeError('`O` is not an object');\n    }\n    if (typeof slot !== 'string') {\n      throw new $TypeError('`slot` must be a string');\n    }\n    var slots = channel.get(O);\n    // eslint-disable-next-line no-extra-parens\n    return slots && slots[(/** @type {SaltedInternalSlot} */'$' + slot)];\n  },\n  has: function (O, slot) {\n    if (!O || typeof O !== 'object' && typeof O !== 'function') {\n      throw new $TypeError('`O` is not an object');\n    }\n    if (typeof slot !== 'string') {\n      throw new $TypeError('`slot` must be a string');\n    }\n    var slots = channel.get(O);\n    // eslint-disable-next-line no-extra-parens\n    return !!slots && hasOwn(slots, /** @type {SaltedInternalSlot} */'$' + slot);\n  },\n  set: function (O, slot, V) {\n    if (!O || typeof O !== 'object' && typeof O !== 'function') {\n      throw new $TypeError('`O` is not an object');\n    }\n    if (typeof slot !== 'string') {\n      throw new $TypeError('`slot` must be a string');\n    }\n    var slots = channel.get(O);\n    if (!slots) {\n      slots = {};\n      channel.set(O, slots);\n    }\n    // eslint-disable-next-line no-extra-parens\n    slots[(/** @type {SaltedInternalSlot} */'$' + slot)] = V;\n  }\n};\nif (Object.freeze) {\n  Object.freeze(SLOT);\n}\nmodule.exports = SLOT;", "map": {"version": 3, "names": ["hasOwn", "require", "channel", "$TypeError", "SLOT", "assert", "O", "slot", "has", "get", "slots", "set", "V", "Object", "freeze", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/internal-slot/index.js"], "sourcesContent": ["'use strict';\n\n/** @typedef {`$${import('.').InternalSlot}`} SaltedInternalSlot */\n/** @typedef {{ [k in SaltedInternalSlot]?: unknown }} SlotsObject */\n\nvar hasOwn = require('hasown');\n/** @type {import('side-channel').Channel<object, SlotsObject>} */\nvar channel = require('side-channel')();\n\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nvar SLOT = {\n\tassert: function (O, slot) {\n\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\tthrow new $TypeError('`O` is not an object');\n\t\t}\n\t\tif (typeof slot !== 'string') {\n\t\t\tthrow new $TypeError('`slot` must be a string');\n\t\t}\n\t\tchannel.assert(O);\n\t\tif (!SLOT.has(O, slot)) {\n\t\t\tthrow new $TypeError('`' + slot + '` is not present on `O`');\n\t\t}\n\t},\n\tget: function (O, slot) {\n\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\tthrow new $TypeError('`O` is not an object');\n\t\t}\n\t\tif (typeof slot !== 'string') {\n\t\t\tthrow new $TypeError('`slot` must be a string');\n\t\t}\n\t\tvar slots = channel.get(O);\n\t\t// eslint-disable-next-line no-extra-parens\n\t\treturn slots && slots[/** @type {SaltedInternalSlot} */ ('$' + slot)];\n\t},\n\thas: function (O, slot) {\n\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\tthrow new $TypeError('`O` is not an object');\n\t\t}\n\t\tif (typeof slot !== 'string') {\n\t\t\tthrow new $TypeError('`slot` must be a string');\n\t\t}\n\t\tvar slots = channel.get(O);\n\t\t// eslint-disable-next-line no-extra-parens\n\t\treturn !!slots && hasOwn(slots, /** @type {SaltedInternalSlot} */ ('$' + slot));\n\t},\n\tset: function (O, slot, V) {\n\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\tthrow new $TypeError('`O` is not an object');\n\t\t}\n\t\tif (typeof slot !== 'string') {\n\t\t\tthrow new $TypeError('`slot` must be a string');\n\t\t}\n\t\tvar slots = channel.get(O);\n\t\tif (!slots) {\n\t\t\tslots = {};\n\t\t\tchannel.set(O, slots);\n\t\t}\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tslots[/** @type {SaltedInternalSlot} */ ('$' + slot)] = V;\n\t}\n};\n\nif (Object.freeze) {\n\tObject.freeze(SLOT);\n}\n\nmodule.exports = SLOT;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC9B;AACA,IAAIC,OAAO,GAAGD,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;AAEvC,IAAIE,UAAU,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE1C;AACA,IAAIG,IAAI,GAAG;EACVC,MAAM,EAAE,SAAAA,CAAUC,CAAC,EAAEC,IAAI,EAAE;IAC1B,IAAI,CAACD,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAW,EAAE;MAC7D,MAAM,IAAIH,UAAU,CAAC,sBAAsB,CAAC;IAC7C;IACA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIJ,UAAU,CAAC,yBAAyB,CAAC;IAChD;IACAD,OAAO,CAACG,MAAM,CAACC,CAAC,CAAC;IACjB,IAAI,CAACF,IAAI,CAACI,GAAG,CAACF,CAAC,EAAEC,IAAI,CAAC,EAAE;MACvB,MAAM,IAAIJ,UAAU,CAAC,GAAG,GAAGI,IAAI,GAAG,yBAAyB,CAAC;IAC7D;EACD,CAAC;EACDE,GAAG,EAAE,SAAAA,CAAUH,CAAC,EAAEC,IAAI,EAAE;IACvB,IAAI,CAACD,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAW,EAAE;MAC7D,MAAM,IAAIH,UAAU,CAAC,sBAAsB,CAAC;IAC7C;IACA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIJ,UAAU,CAAC,yBAAyB,CAAC;IAChD;IACA,IAAIO,KAAK,GAAGR,OAAO,CAACO,GAAG,CAACH,CAAC,CAAC;IAC1B;IACA,OAAOI,KAAK,IAAIA,KAAK,EAAC,iCAAmC,GAAG,GAAGH,IAAI,EAAE;EACtE,CAAC;EACDC,GAAG,EAAE,SAAAA,CAAUF,CAAC,EAAEC,IAAI,EAAE;IACvB,IAAI,CAACD,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAW,EAAE;MAC7D,MAAM,IAAIH,UAAU,CAAC,sBAAsB,CAAC;IAC7C;IACA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIJ,UAAU,CAAC,yBAAyB,CAAC;IAChD;IACA,IAAIO,KAAK,GAAGR,OAAO,CAACO,GAAG,CAACH,CAAC,CAAC;IAC1B;IACA,OAAO,CAAC,CAACI,KAAK,IAAIV,MAAM,CAACU,KAAK,EAAE,iCAAmC,GAAG,GAAGH,IAAK,CAAC;EAChF,CAAC;EACDI,GAAG,EAAE,SAAAA,CAAUL,CAAC,EAAEC,IAAI,EAAEK,CAAC,EAAE;IAC1B,IAAI,CAACN,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,UAAW,EAAE;MAC7D,MAAM,IAAIH,UAAU,CAAC,sBAAsB,CAAC;IAC7C;IACA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIJ,UAAU,CAAC,yBAAyB,CAAC;IAChD;IACA,IAAIO,KAAK,GAAGR,OAAO,CAACO,GAAG,CAACH,CAAC,CAAC;IAC1B,IAAI,CAACI,KAAK,EAAE;MACXA,KAAK,GAAG,CAAC,CAAC;MACVR,OAAO,CAACS,GAAG,CAACL,CAAC,EAAEI,KAAK,CAAC;IACtB;IACA;IACAA,KAAK,EAAC,iCAAmC,GAAG,GAAGH,IAAI,EAAE,GAAGK,CAAC;EAC1D;AACD,CAAC;AAED,IAAIC,MAAM,CAACC,MAAM,EAAE;EAClBD,MAAM,CAACC,MAAM,CAACV,IAAI,CAAC;AACpB;AAEAW,MAAM,CAACC,OAAO,GAAGZ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}