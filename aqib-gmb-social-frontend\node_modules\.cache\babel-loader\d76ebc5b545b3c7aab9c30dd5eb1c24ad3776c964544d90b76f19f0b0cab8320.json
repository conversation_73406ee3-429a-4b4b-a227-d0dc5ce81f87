{"ast": null, "code": "import { clamp } from '@mui/utils';\nexport function clampStepwise(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return clamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return clamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return clamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}", "map": {"version": 3, "names": ["clamp", "clampStepwise", "val", "min", "Number", "MIN_SAFE_INTEGER", "max", "MAX_SAFE_INTEGER", "stepProp", "NaN", "isNaN", "step", "remainder", "positivity", "Math", "sign", "abs", "isNumber", "isFinite"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/unstable_useNumberInput/utils.js"], "sourcesContent": ["import { clamp } from '@mui/utils';\nexport function clampStepwise(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, stepProp = NaN) {\n  if (Number.isNaN(stepProp)) {\n    return clamp(val, min, max);\n  }\n  const step = stepProp || 1;\n  const remainder = val % step;\n  const positivity = Math.sign(remainder);\n  if (Math.abs(remainder) > step / 2) {\n    return clamp(val + positivity * (step - Math.abs(remainder)), min, max);\n  }\n  return clamp(val - positivity * Math.abs(remainder), min, max);\n}\nexport function isNumber(val) {\n  return typeof val === 'number' && !Number.isNaN(val) && Number.isFinite(val);\n}"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAEC,GAAG,GAAGC,MAAM,CAACC,gBAAgB,EAAEC,GAAG,GAAGF,MAAM,CAACG,gBAAgB,EAAEC,QAAQ,GAAGC,GAAG,EAAE;EAC/G,IAAIL,MAAM,CAACM,KAAK,CAACF,QAAQ,CAAC,EAAE;IAC1B,OAAOR,KAAK,CAACE,GAAG,EAAEC,GAAG,EAAEG,GAAG,CAAC;EAC7B;EACA,MAAMK,IAAI,GAAGH,QAAQ,IAAI,CAAC;EAC1B,MAAMI,SAAS,GAAGV,GAAG,GAAGS,IAAI;EAC5B,MAAME,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACH,SAAS,CAAC;EACvC,IAAIE,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,GAAGD,IAAI,GAAG,CAAC,EAAE;IAClC,OAAOX,KAAK,CAACE,GAAG,GAAGW,UAAU,IAAIF,IAAI,GAAGG,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,CAAC,EAAET,GAAG,EAAEG,GAAG,CAAC;EACzE;EACA,OAAON,KAAK,CAACE,GAAG,GAAGW,UAAU,GAAGC,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,EAAET,GAAG,EAAEG,GAAG,CAAC;AAChE;AACA,OAAO,SAASW,QAAQA,CAACf,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACE,MAAM,CAACM,KAAK,CAACR,GAAG,CAAC,IAAIE,MAAM,CAACc,QAAQ,CAAChB,GAAG,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}