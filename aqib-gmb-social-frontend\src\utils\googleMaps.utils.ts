/**
 * Google Maps API utilities
 */

let isGoogleMapsLoaded = false;
let isGoogleMapsLoading = false;
const loadPromises: Promise<void>[] = [];

/**
 * Dynamically load Google Maps API
 */
export const loadGoogleMapsAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // If already loaded, resolve immediately
    if (isGoogleMapsLoaded && window.google && window.google.maps) {
      resolve();
      return;
    }

    // If currently loading, wait for existing promise
    if (isGoogleMapsLoading) {
      const existingPromise = loadPromises[loadPromises.length - 1];
      if (existingPromise) {
        existingPromise.then(resolve).catch(reject);
        return;
      }
    }

    isGoogleMapsLoading = true;

    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    
    // Check if API key is available
    if (!apiKey || apiKey === 'YOUR_GOOGLE_MAPS_API_KEY') {
      console.warn('Google Maps API key not configured. Using fallback location suggestions.');
      isGoogleMapsLoading = false;
      resolve(); // Resolve anyway to allow fallback functionality
      return;
    }

    // Check if script already exists
    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
    if (existingScript) {
      // Wait for existing script to load
      existingScript.addEventListener('load', () => {
        isGoogleMapsLoaded = true;
        isGoogleMapsLoading = false;
        resolve();
      });
      existingScript.addEventListener('error', () => {
        isGoogleMapsLoading = false;
        reject(new Error('Failed to load Google Maps API'));
      });
      return;
    }

    // Create and load the script
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      isGoogleMapsLoaded = true;
      isGoogleMapsLoading = false;
      resolve();
    };

    script.onerror = () => {
      isGoogleMapsLoading = false;
      reject(new Error('Failed to load Google Maps API'));
    };

    document.head.appendChild(script);
  });
};

/**
 * Check if Google Maps API is available
 */
export const isGoogleMapsAPIAvailable = (): boolean => {
  return !!(window.google && window.google.maps && window.google.maps.places);
};

/**
 * Get place details using Google Places API
 */
export const getPlaceDetails = (placeId: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!isGoogleMapsAPIAvailable()) {
      reject(new Error('Google Maps API not available'));
      return;
    }

    const service = new window.google.maps.places.PlacesService(
      document.createElement('div')
    );

    service.getDetails(
      {
        placeId: placeId,
        fields: ['place_id', 'name', 'geometry', 'formatted_address']
      },
      (place, status) => {
        if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
          resolve({
            placeId: place.place_id,
            name: place.name,
            address: place.formatted_address,
            lat: place.geometry?.location.lat(),
            lng: place.geometry?.location.lng()
          });
        } else {
          reject(new Error(`Place details request failed: ${status}`));
        }
      }
    );
  });
};

/**
 * Get autocomplete predictions using Google Places API
 */
export const getAutocompletePredictions = (
  input: string,
  options: {
    types?: string[];
    componentRestrictions?: { country?: string };
  } = {}
): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    if (!isGoogleMapsAPIAvailable()) {
      reject(new Error('Google Maps API not available'));
      return;
    }

    const service = new window.google.maps.places.AutocompleteService();

    service.getPlacePredictions(
      {
        input,
        types: options.types || ['(cities)'],
        componentRestrictions: options.componentRestrictions || { country: 'us' }
      },
      (predictions, status) => {
        if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
          const suggestions = predictions.map((prediction) => ({
            id: prediction.place_id,
            name: prediction.description,
            placeId: prediction.place_id,
            types: prediction.types
          }));
          resolve(suggestions);
        } else if (status === window.google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
          resolve([]);
        } else {
          reject(new Error(`Autocomplete request failed: ${status}`));
        }
      }
    );
  });
};
