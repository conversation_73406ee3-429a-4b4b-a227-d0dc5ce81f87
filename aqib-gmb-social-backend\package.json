{"name": "express-js-aws-lambda-claudia-boilerplate", "version": "1.0.0", "description": "Backend boilerplate code built using Express.js(Node.js)", "main": "app.local.js", "scripts": {"devStart": "nodemon app.local.js", "env-setup-dev": "node setup/envToJson.js DEV", "env-setup-production": "node setup/envToJson.js PROD", "create-dev": "claudia create --config claudia.dev.json --name=__DEV_PROJECT_NAME__ --region __REGION__ --handler lambda.handler --profile=__PROFILE_NAME__ --deploy-proxy-api --role=__ROLE_NAME__ --set-env-from-json app.config.dev.json --version dev", "create-production": "claudia create --config claudia.prod.json --name=__PROD_PROJECT_NAME__ --region __REGION__ --handler lambda.handler --profile=__PROFILE_NAME__ --deploy-proxy-api --role=__ROLE_NAME__ --set-env-from-json app.config.dev.json --version prod", "update-dev": "claudia update --config claudia.dev.json --name=__DEV_PROJECT_NAME__ --profile=__PROFILE_NAME__ --handler lambda.handler --role=__ROLE_NAME__ --set-env-from-json app.config.dev.json --version dev", "update-production": "claudia update --config claudia.prod.json --name=__PROD_PROJECT_NAME__ --profile=__PROFILE_NAME__ --handler lambda.handler --role=__ROLE_NAME__ --set-env-from-json app.config.prod.json --version prod", "generate-proxy": "claudia generate-serverless-express-proxy --express-module app", "clean-old-zips": "powershell -Command \"Get-ChildItem -Path . -Filter 'deployment-*.zip' | Remove-Item -Force\"", "zip": "powershell -Command \"$timestamp = Get-Date -Format 'yyyyMMddHHmmss'; Remove-Item -Path 'deployment-*.zip' -ErrorAction Ignore; Compress-Archive -Path * -DestinationPath (\\\"deployment-aqib-gmb-social-backend-$timestamp.zip\\\") -CompressionLevel Optimal\"", "start": "ntl", "test:db": "node database/test_connection.js", "test:postLocations": "node database/test_postLocations.js", "migrate:gmb-locations": "node database/run_migration.js", "rollback:gmb-locations": "node database/run_rollback.js", "add-logging": "node scripts/add-logging-to-controllers.js", "add-comprehensive-logging": "node scripts/add-comprehensive-logging.js", "implement-all-logging": "node scripts/implement-all-logging.js", "init-geo-grid": "node scripts/init-geo-grid-db.js", "check-geo-grid": "node -e \"require('./scripts/init-geo-grid-db.js').checkGeoGridTables().then(console.log)\"", "drop-geo-grid": "node -e \"require('./scripts/init-geo-grid-db.js').dropGeoGridTables().then(console.log)\""}, "repository": {"type": "git", "url": "git+https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate.git"}, "keywords": ["Node.js", "Express.js", "Community", "Backend"], "author": "<PERSON><PERSON><PERSON>", "license": "GPL-3.0-only", "bugs": {"url": "https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate/issues"}, "homepage": "https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate#readme", "dependencies": {"@google/generative-ai": "^0.21.0", "ajv": "^8.16.0", "async": "^3.2.6", "aws-serverless-express": "^3.4.0", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "google-auth-library": "^9.15.1", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.10.0", "qs": "^6.12.3", "rc": "^1.2.8", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "util": "^0.12.5", "uuid": "^9.0.1"}, "devDependencies": {"claudia": "^5.14.1", "nodemon": "^3.1.3", "ntl": "^5.1.0"}}