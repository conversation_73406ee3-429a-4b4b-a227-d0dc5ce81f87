import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  CREATE_REVIEW_TAGS,
  GET_ALL_TAGS,
  LIST_OF_REVIEWS,
  LIST_OF_REVIEWS_EXTENDED,
  REFRESH_REVIEWS,
  REPLY_TO_REVIEW,
  REPLY_WITH_AI,
  UPDATE_TAGS_TO_REVIEW,
} from "../../constants/endPoints.constant";
import { Action } from "redux";
import { IUpdateTagToReviewRequestModel } from "../../interfaces/request/IUpdateTagToReviewRequestModel";
import { IReviewsListRequestModel } from "../../interfaces/request/IReviewsListRequestModel";

class ReviewService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  getReviews = async (userId: number, locationId: string) => {
    return await this._httpHelperService.get(LIST_OF_REVIEWS(userId), {
      "x-gmb-location-id": locationId,
    });
  };

  getReviewsExtended = async (
    userId: number,
    locationId: string,
    reviewRequestModel: IReviewsListRequestModel
  ) => {
    return await this._httpHelperService.post(
      LIST_OF_REVIEWS_EXTENDED(userId),
      reviewRequestModel,
      {
        "x-gmb-location-id": locationId,
      }
    );
  };

  getReviewReplyFromAI = async (comment: string, rating: number) => {
    return await this._httpHelperService.post(REPLY_WITH_AI, {
      comment,
      rating,
    });
  };

  createReviewTags = async (tagName: string, userId: number) => {
    return await this._httpHelperService.post(CREATE_REVIEW_TAGS, {
      tagName,
      createdBy: userId,
    });
  };

  getAllTags = async () => {
    return await this._httpHelperService.get(GET_ALL_TAGS);
  };

  updateTagsToReview = async (request: IUpdateTagToReviewRequestModel) => {
    return await this._httpHelperService.post(UPDATE_TAGS_TO_REVIEW, request);
  };

  refreshReviews = async (headerObject: any) => {
    return await this._httpHelperService.get(REFRESH_REVIEWS, headerObject);
  };

  replyToReview = async (headers: any, reqBody: any) => {
    return await this._httpHelperService.post(
      REPLY_TO_REVIEW,
      reqBody,
      headers
    );
  };
}

export default ReviewService;
