import { Component } from "react";
import moment from "moment";
import { STARRATINGMAP } from "../constants/dbConstant.constant";
import dayjs from "dayjs";

class ApplicationHelperService extends Component {
  isValidateEmail = async (text: string) => {
    try {
      let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;
      if (reg.test(text) === false) {
        console.log("In-Valid Email in Application Helper Service");
        return false;
      } else {
        console.log("Valid Email in Application Helper Service");
        return true;
      }
    } catch (error) {
      return false;
    }
  };

  getUserDateTimeFormat = (timestamp: any) => {
    return moment(new Date(timestamp)).format("DD-MM-YYYY hh:mm A"); // 12H clock (AM/PM)
  };

  getExpandedDateTimeFormat = (timestamp: any) => {
    return moment(new Date(timestamp)).format("MMMM DD, YYYY hh:mm A"); // 12H clock (AM/PM)
  };

  getFormatedDate = (date = new Date(), format = "YYYY/MM/DD") =>
    moment(date).format(format);

  getExpandedFormatedDate = (date = new Date(), format = "DD-MMM-YYYY") =>
    moment(date).format(format);

  getPaginationText = (pageNo: number, offset: number, totalRecords: number) =>
    `Showing ${(pageNo - 1) * offset + 1} - ${
      offset * pageNo > totalRecords ? totalRecords : offset * pageNo
    } of ${totalRecords} Records`;

  getRatingNumberFromText = (rating: string) => {
    return STARRATINGMAP[rating as keyof typeof STARRATINGMAP];
  };

  getAvatarText = (fullname: string): string => {
    const nameParts = fullname.trim().split(/\s+/);
    const firstInitial = nameParts[0]?.charAt(0).toUpperCase() || "";
    const lastInitial =
      nameParts[nameParts.length - 1]?.charAt(0).toUpperCase() || "";
    return `${firstInitial}${lastInitial}` || "NA";
  };

  toTitleCase(str: string): string {
    return str && str.length > 0
      ? str
          .toLowerCase()
          .split(" ")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")
      : "";
  }

  transformClicksData = (rawData: any, clickType: string) => {
    const clicksSeries =
      rawData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.find(
        (series: any) => series.dailyMetric === clickType
      );

    if (!clicksSeries) return { data: [], labels: [] };

    const monthlyAggregates: Record<string, number> = {};

    for (const entry of clicksSeries.timeSeries.datedValues) {
      const { year, month } = entry.date;
      const key = dayjs(`${year}-${month}-01`).format("MMM YYYY");

      const value = parseInt(entry.value ?? "0", 10);
      if (!monthlyAggregates[key]) monthlyAggregates[key] = 0;
      monthlyAggregates[key] += value;
    }

    const labels = Object.keys(monthlyAggregates);
    const data = labels.map((label) => monthlyAggregates[label]);

    return { data, labels };
  };

  getDaysDifference(startDate: string, endDate: string): number {
    const start = dayjs(startDate);
    const end = dayjs(endDate);

    return end.diff(start, "day");
  }
}

export default ApplicationHelperService;
