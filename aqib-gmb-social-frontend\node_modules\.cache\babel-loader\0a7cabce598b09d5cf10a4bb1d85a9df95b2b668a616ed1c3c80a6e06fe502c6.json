{"ast": null, "code": "import HttpHelperService from \"../httpHelper.service\";\nimport { GEO_GRID_SEARCH_LOCATION, GEO_GRID_GENERATE_GRID, GEO_GRID_GET_DATA, GEO_GRID_SAVE_CONFIG, GEO_GRID_GET_CONFIGS, GEO_GRID_UPDATE_CONFIG, GEO_GRID_DELETE_CONFIG, GEO_GRID_LOCATION_SUGGESTIONS, GEO_GRID_VALIDATE_COORDINATES } from \"../../constants/endPoints.constant\";\nclass GeoGridService {\n  constructor(dispatch) {\n    this._httpHelperService = void 0;\n    /**\n     * Search for location using various methods\n     */\n    this.searchLocation = async searchRequest => {\n      return await this._httpHelperService.post(GEO_GRID_SEARCH_LOCATION, searchRequest);\n    };\n    /**\n     * Generate grid points based on center location and configuration\n     */\n    this.generateGrid = async gridRequest => {\n      return await this._httpHelperService.post(GEO_GRID_GENERATE_GRID, gridRequest);\n    };\n    /**\n     * Get grid data by ID\n     */\n    this.getGridData = async gridId => {\n      return await this._httpHelperService.get(GEO_GRID_GET_DATA(gridId));\n    };\n    /**\n     * Save grid configuration\n     */\n    this.saveGridConfiguration = async configuration => {\n      return await this._httpHelperService.post(GEO_GRID_SAVE_CONFIG, configuration);\n    };\n    /**\n     * Get all grid configurations for a user\n     */\n    this.getGridConfigurations = async userId => {\n      return await this._httpHelperService.get(GEO_GRID_GET_CONFIGS(userId));\n    };\n    /**\n     * Update grid configuration\n     */\n    this.updateGridConfiguration = async (gridId, configuration) => {\n      return await this._httpHelperService.put(GEO_GRID_UPDATE_CONFIG(gridId), configuration);\n    };\n    /**\n     * Delete grid configuration\n     */\n    this.deleteGridConfiguration = async gridId => {\n      return await this._httpHelperService.delete(GEO_GRID_DELETE_CONFIG(gridId));\n    };\n    /**\n     * Get location suggestions for autocomplete\n     */\n    this.getLocationSuggestions = async query => {\n      return await this._httpHelperService.get(`${GEO_GRID_LOCATION_SUGGESTIONS}?query=${encodeURIComponent(query)}`);\n    };\n    /**\n     * Validate coordinates\n     */\n    this.validateCoordinates = async (lat, lng) => {\n      return await this._httpHelperService.post(GEO_GRID_VALIDATE_COORDINATES, {\n        lat,\n        lng\n      });\n    };\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n}\nexport default GeoGridService;", "map": {"version": 3, "names": ["HttpHelperService", "GEO_GRID_SEARCH_LOCATION", "GEO_GRID_GENERATE_GRID", "GEO_GRID_GET_DATA", "GEO_GRID_SAVE_CONFIG", "GEO_GRID_GET_CONFIGS", "GEO_GRID_UPDATE_CONFIG", "GEO_GRID_DELETE_CONFIG", "GEO_GRID_LOCATION_SUGGESTIONS", "GEO_GRID_VALIDATE_COORDINATES", "GeoGridService", "constructor", "dispatch", "_httpHelperService", "searchLocation", "searchRequest", "post", "generateGrid", "gridRequest", "getGridData", "gridId", "get", "saveGridConfiguration", "configuration", "getGridConfigurations", "userId", "updateGridConfiguration", "put", "deleteGridConfiguration", "delete", "getLocationSuggestions", "query", "encodeURIComponent", "validateCoordinates", "lat", "lng"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/services/geoGrid/geoGrid.service.tsx"], "sourcesContent": ["import { Dispatch } from \"react\";\nimport { Action } from \"redux\";\nimport HttpHelperService from \"../httpHelper.service\";\nimport {\n  GEO_GRID_SEARCH_LOCATION,\n  GEO_GRID_GENERATE_GRID,\n  GEO_GRID_GET_DATA,\n  GEO_GRID_SAVE_CONFIG,\n  GEO_GRID_GET_CONFIGS,\n  GEO_GRID_UPDATE_CONFIG,\n  GEO_GRID_DELETE_CONFIG,\n  GEO_GRID_LOCATION_SUGGESTIONS,\n  GEO_GRID_VALIDATE_COORDINATES,\n} from \"../../constants/endPoints.constant\";\n\nexport interface LocationSearchRequest {\n  searchType: \"name\" | \"coordinates\" | \"mapUrl\";\n  query?: string;\n  coordinates?: {\n    lat: number;\n    lng: number;\n  };\n}\n\nexport interface GridGenerationRequest {\n  centerLat: number;\n  centerLng: number;\n  gridSize: string;\n  distance: number;\n  distanceUnit: \"meters\" | \"miles\" | \"kilometers\";\n}\n\nexport interface GridConfiguration {\n  id?: number;\n  userId?: number;\n  name: string;\n  centerLat: number;\n  centerLng: number;\n  gridSize: string;\n  distance: number;\n  distanceUnit: \"meters\" | \"miles\" | \"kilometers\";\n  searchType: \"name\" | \"coordinates\" | \"mapUrl\";\n  searchQuery?: string;\n  isScheduleEnabled: boolean;\n  settings?: any;\n  gridPoints?: GridPoint[];\n}\n\nexport interface GridPoint {\n  lat: number;\n  lng: number;\n  index: number;\n  gridPosition: {\n    row: number;\n    col: number;\n  };\n  address?: string;\n  placeId?: string;\n}\n\nexport interface LocationSuggestion {\n  name: string;\n  placeId: string;\n  lat: number;\n  lng: number;\n}\n\nclass GeoGridService {\n  private _httpHelperService: HttpHelperService;\n\n  constructor(dispatch: Dispatch<Action>) {\n    this._httpHelperService = new HttpHelperService(dispatch);\n  }\n\n  /**\n   * Search for location using various methods\n   */\n  searchLocation = async (searchRequest: LocationSearchRequest) => {\n    return await this._httpHelperService.post(\n      GEO_GRID_SEARCH_LOCATION,\n      searchRequest\n    );\n  };\n\n  /**\n   * Generate grid points based on center location and configuration\n   */\n  generateGrid = async (gridRequest: GridGenerationRequest) => {\n    return await this._httpHelperService.post(\n      GEO_GRID_GENERATE_GRID,\n      gridRequest\n    );\n  };\n\n  /**\n   * Get grid data by ID\n   */\n  getGridData = async (gridId: string) => {\n    return await this._httpHelperService.get(GEO_GRID_GET_DATA(gridId));\n  };\n\n  /**\n   * Save grid configuration\n   */\n  saveGridConfiguration = async (configuration: GridConfiguration) => {\n    return await this._httpHelperService.post(\n      GEO_GRID_SAVE_CONFIG,\n      configuration\n    );\n  };\n\n  /**\n   * Get all grid configurations for a user\n   */\n  getGridConfigurations = async (userId: number) => {\n    return await this._httpHelperService.get(GEO_GRID_GET_CONFIGS(userId));\n  };\n\n  /**\n   * Update grid configuration\n   */\n  updateGridConfiguration = async (\n    gridId: string,\n    configuration: Partial<GridConfiguration>\n  ) => {\n    return await this._httpHelperService.put(\n      GEO_GRID_UPDATE_CONFIG(gridId),\n      configuration\n    );\n  };\n\n  /**\n   * Delete grid configuration\n   */\n  deleteGridConfiguration = async (gridId: string) => {\n    return await this._httpHelperService.delete(GEO_GRID_DELETE_CONFIG(gridId));\n  };\n\n  /**\n   * Get location suggestions for autocomplete\n   */\n  getLocationSuggestions = async (query: string) => {\n    return await this._httpHelperService.get(\n      `${GEO_GRID_LOCATION_SUGGESTIONS}?query=${encodeURIComponent(query)}`\n    );\n  };\n\n  /**\n   * Validate coordinates\n   */\n  validateCoordinates = async (lat: number, lng: number) => {\n    return await this._httpHelperService.post(GEO_GRID_VALIDATE_COORDINATES, {\n      lat,\n      lng,\n    });\n  };\n}\n\nexport default GeoGridService;\n"], "mappings": "AAEA,OAAOA,iBAAiB,MAAM,uBAAuB;AACrD,SACEC,wBAAwB,EACxBC,sBAAsB,EACtBC,iBAAiB,EACjBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,sBAAsB,EACtBC,6BAA6B,EAC7BC,6BAA6B,QACxB,oCAAoC;AAsD3C,MAAMC,cAAc,CAAC;EAGnBC,WAAWA,CAACC,QAA0B,EAAE;IAAA,KAFhCC,kBAAkB;IAM1B;AACF;AACA;IAFE,KAGAC,cAAc,GAAG,MAAOC,aAAoC,IAAK;MAC/D,OAAO,MAAM,IAAI,CAACF,kBAAkB,CAACG,IAAI,CACvCf,wBAAwB,EACxBc,aACF,CAAC;IACH,CAAC;IAED;AACF;AACA;IAFE,KAGAE,YAAY,GAAG,MAAOC,WAAkC,IAAK;MAC3D,OAAO,MAAM,IAAI,CAACL,kBAAkB,CAACG,IAAI,CACvCd,sBAAsB,EACtBgB,WACF,CAAC;IACH,CAAC;IAED;AACF;AACA;IAFE,KAGAC,WAAW,GAAG,MAAOC,MAAc,IAAK;MACtC,OAAO,MAAM,IAAI,CAACP,kBAAkB,CAACQ,GAAG,CAAClB,iBAAiB,CAACiB,MAAM,CAAC,CAAC;IACrE,CAAC;IAED;AACF;AACA;IAFE,KAGAE,qBAAqB,GAAG,MAAOC,aAAgC,IAAK;MAClE,OAAO,MAAM,IAAI,CAACV,kBAAkB,CAACG,IAAI,CACvCZ,oBAAoB,EACpBmB,aACF,CAAC;IACH,CAAC;IAED;AACF;AACA;IAFE,KAGAC,qBAAqB,GAAG,MAAOC,MAAc,IAAK;MAChD,OAAO,MAAM,IAAI,CAACZ,kBAAkB,CAACQ,GAAG,CAAChB,oBAAoB,CAACoB,MAAM,CAAC,CAAC;IACxE,CAAC;IAED;AACF;AACA;IAFE,KAGAC,uBAAuB,GAAG,OACxBN,MAAc,EACdG,aAAyC,KACtC;MACH,OAAO,MAAM,IAAI,CAACV,kBAAkB,CAACc,GAAG,CACtCrB,sBAAsB,CAACc,MAAM,CAAC,EAC9BG,aACF,CAAC;IACH,CAAC;IAED;AACF;AACA;IAFE,KAGAK,uBAAuB,GAAG,MAAOR,MAAc,IAAK;MAClD,OAAO,MAAM,IAAI,CAACP,kBAAkB,CAACgB,MAAM,CAACtB,sBAAsB,CAACa,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;AACF;AACA;IAFE,KAGAU,sBAAsB,GAAG,MAAOC,KAAa,IAAK;MAChD,OAAO,MAAM,IAAI,CAAClB,kBAAkB,CAACQ,GAAG,CACtC,GAAGb,6BAA6B,UAAUwB,kBAAkB,CAACD,KAAK,CAAC,EACrE,CAAC;IACH,CAAC;IAED;AACF;AACA;IAFE,KAGAE,mBAAmB,GAAG,OAAOC,GAAW,EAAEC,GAAW,KAAK;MACxD,OAAO,MAAM,IAAI,CAACtB,kBAAkB,CAACG,IAAI,CAACP,6BAA6B,EAAE;QACvEyB,GAAG;QACHC;MACF,CAAC,CAAC;IACJ,CAAC;IApFC,IAAI,CAACtB,kBAAkB,GAAG,IAAIb,iBAAiB,CAACY,QAAQ,CAAC;EAC3D;AAoFF;AAEA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}