{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\geoGrid\\\\geoGrid.screen.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Typography, Paper, Grid, Button, Alert, Snackbar } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService from \"../../services/geoGrid/geoGrid.service\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GeoGridScreen = ({\n  title\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [gridPoints, setGridPoints] = useState([]);\n  const [gridConfiguration, setGridConfiguration] = useState({\n    name: \"\",\n    centerLat: 0,\n    centerLng: 0,\n    gridSize: \"3x3\",\n    distance: 500,\n    distanceUnit: \"meters\",\n    searchType: \"name\",\n    searchQuery: \"\",\n    isScheduleEnabled: false,\n    settings: {}\n  });\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState([]);\n  const user = useSelector(state => {\n    var _state$authReducer;\n    return (_state$authReducer = state.authReducer) === null || _state$authReducer === void 0 ? void 0 : _state$authReducer.userInfo;\n  });\n  useEffect(() => {\n    document.title = title;\n    if (user !== null && user !== void 0 && user.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n  const loadSavedConfigurations = async () => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error) {\n      setError(\"Failed to load saved configurations\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLocationSearch = async searchRequest => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.searchLocation(searchRequest);\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration(prev => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\"\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateGrid = async (centerLat, centerLng) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit\n      });\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleConfigurationChange = updates => {\n    setGridConfiguration(prev => ({\n      ...prev,\n      ...updates\n    }));\n  };\n  const handleConfigurationNameChange = name => {\n    setGridConfiguration(prev => ({\n      ...prev,\n      name\n    }));\n  };\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (!(user !== null && user !== void 0 && user.id)) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints\n      };\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoadConfiguration = async config => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const {\n            configuration,\n            gridPoints: loadedPoints\n          } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\"\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteConfiguration = async configId => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.deleteGridConfiguration(configId.toString());\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!(user !== null && user !== void 0 && user.id)) {\n    return /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            marginBottom: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"pageTitle\",\n            children: \"Google Geo Grid\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            className: \"subtitle2\",\n            children: \"Create and manage location-based grids for your business analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            size: \"small\",\n            onClick: () => navigate(\"/\"),\n            variant: \"outlined\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this),\n          children: \"Please log in to access the Google Geo Grid functionality. You can still explore the basic features, but saving configurations requires authentication.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: \"fit-content\"\n              },\n              children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n                onLocationSearch: handleLocationSearch,\n                onGenerateGrid: generateGrid,\n                onSaveConfiguration: () => setError(\"Please log in to save configurations\"),\n                loading: loading,\n                currentLocation: currentLocation,\n                savedConfigurations: [],\n                onLoadConfiguration: () => {},\n                onDeleteConfiguration: () => {},\n                configurationName: gridConfiguration.name,\n                onConfigurationNameChange: handleConfigurationNameChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2,\n                height: 600\n              },\n              children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n                center: currentLocation ? {\n                  lat: currentLocation.lat,\n                  lng: currentLocation.lng\n                } : null,\n                gridPoints: gridPoints,\n                loading: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: !!error,\n          autoHideDuration: 6000,\n          onClose: handleCloseSnackbar,\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleCloseSnackbar,\n            severity: \"error\",\n            sx: {\n              width: \"100%\"\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n          open: !!success,\n          autoHideDuration: 4000,\n          onClose: handleCloseSnackbar,\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            onClose: handleCloseSnackbar,\n            severity: \"success\",\n            sx: {\n              width: \"100%\"\n            },\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(LeftMenuComponent, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          marginBottom: \"5px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"pageTitle\",\n          children: \"Google Geo Grid\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          className: \"subtitle2\",\n          children: \"Create and manage location-based grids for your business analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: \"fit-content\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n              onLocationSearch: handleLocationSearch,\n              onGenerateGrid: generateGrid,\n              onSaveConfiguration: handleSaveConfiguration,\n              loading: loading,\n              currentLocation: currentLocation,\n              savedConfigurations: savedConfigurations,\n              onLoadConfiguration: handleLoadConfiguration,\n              onDeleteConfiguration: handleDeleteConfiguration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 5,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 600\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n              center: currentLocation ? {\n                lat: currentLocation.lat,\n                lng: currentLocation.lng\n              } : null,\n              gridPoints: gridPoints,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: \"fit-content\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridSettings, {\n              configuration: gridConfiguration,\n              onConfigurationChange: handleConfigurationChange,\n              onGenerateGrid: generateGrid,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!error,\n        autoHideDuration: 6000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"error\",\n          sx: {\n            width: \"100%\"\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!success,\n        autoHideDuration: 4000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"success\",\n          sx: {\n            width: \"100%\"\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 351,\n    columnNumber: 5\n  }, this);\n};\n_s(GeoGridScreen, \"dgTOCs4Ee7Z8q+XBx2Bk0pyaGX4=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = GeoGridScreen;\nexport default GeoGridScreen;\nvar _c;\n$RefreshReg$(_c, \"GeoGridScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "<PERSON><PERSON>", "<PERSON><PERSON>", "Snackbar", "useSelector", "useDispatch", "useNavigate", "LeftMenuComponent", "GeoGridControls", "GeoGridMap", "GeoGridSettings", "GeoGridService", "jsxDEV", "_jsxDEV", "GeoGridScreen", "title", "_s", "dispatch", "navigate", "geoGridService", "loading", "setLoading", "error", "setError", "success", "setSuccess", "currentLocation", "setCurrentLocation", "gridPoints", "setGridPoints", "gridConfiguration", "setGridConfiguration", "name", "centerLat", "centerLng", "gridSize", "distance", "distanceUnit", "searchType", "searchQuery", "isScheduleEnabled", "settings", "activeTab", "setActiveTab", "savedConfigurations", "setSavedConfigurations", "user", "state", "_state$authReducer", "authReducer", "userInfo", "document", "id", "loadSavedConfigurations", "response", "getGridConfigurations", "data", "handleLocationSearch", "searchRequest", "searchLocation", "locationData", "prev", "lat", "lng", "query", "generateGrid", "message", "Error", "handleConfigurationChange", "updates", "handleConfigurationNameChange", "handleSaveConfiguration", "trim", "configToSave", "userId", "saveGridConfiguration", "handleLoadConfiguration", "config", "getGridData", "toString", "configuration", "loadedPoints", "address", "placeId", "handleDeleteConfiguration", "configId", "deleteGridConfiguration", "handleCloseSnackbar", "children", "sx", "marginBottom", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "severity", "mt", "action", "color", "size", "onClick", "container", "spacing", "item", "xs", "md", "p", "height", "onLocationSearch", "onGenerateGrid", "onSaveConfiguration", "onLoadConfiguration", "onDeleteConfiguration", "configurationName", "onConfigurationNameChange", "center", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "onConfigurationChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/geoGrid/geoGrid.screen.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport {\n  Box,\n  Container,\n  Typography,\n  Paper,\n  Grid,\n  Button,\n  Alert,\n  Snackbar,\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport LeftMenuComponent from \"../../components/leftMenu/leftMenu.component\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService, {\n  GridConfiguration,\n  GridPoint,\n  LocationSearchRequest,\n} from \"../../services/geoGrid/geoGrid.service\";\n\ninterface GeoGridScreenProps {\n  title: string;\n}\n\ninterface LocationData {\n  name: string;\n  lat: number;\n  lng: number;\n  address: string;\n  placeId: string;\n}\n\nconst GeoGridScreen: React.FC<GeoGridScreenProps> = ({ title }) => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(\n    null\n  );\n  const [gridPoints, setGridPoints] = useState<GridPoint[]>([]);\n  const [gridConfiguration, setGridConfiguration] = useState<GridConfiguration>(\n    {\n      name: \"\",\n      centerLat: 0,\n      centerLng: 0,\n      gridSize: \"3x3\",\n      distance: 500,\n      distanceUnit: \"meters\",\n      searchType: \"name\",\n      searchQuery: \"\",\n      isScheduleEnabled: false,\n      settings: {},\n    }\n  );\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState<\n    GridConfiguration[]\n  >([]);\n\n  const user = useSelector((state: any) => state.authReducer?.userInfo);\n\n  useEffect(() => {\n    document.title = title;\n    if (user?.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n\n  const loadSavedConfigurations = async () => {\n    if (!user?.id) return;\n\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error: any) {\n      setError(\"Failed to load saved configurations\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLocationSearch = async (searchRequest: LocationSearchRequest) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.searchLocation(searchRequest);\n\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration((prev) => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\",\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateGrid = async (centerLat?: number, centerLng?: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit,\n      });\n\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleConfigurationChange = (updates: Partial<GridConfiguration>) => {\n    setGridConfiguration((prev) => ({ ...prev, ...updates }));\n  };\n\n  const handleConfigurationNameChange = (name: string) => {\n    setGridConfiguration((prev) => ({ ...prev, name }));\n  };\n\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (!user?.id) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints,\n      };\n\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoadConfiguration = async (config: GridConfiguration) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const { configuration, gridPoints: loadedPoints } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\",\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteConfiguration = async (configId: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.deleteGridConfiguration(\n        configId.toString()\n      );\n\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!user?.id) {\n    return (\n      <LeftMenuComponent>\n        <Box>\n          <Box sx={{ marginBottom: \"5px\" }}>\n            <h3 className=\"pageTitle\">Google Geo Grid</h3>\n            <Typography variant=\"subtitle2\" className=\"subtitle2\">\n              Create and manage location-based grids for your business analysis\n            </Typography>\n          </Box>\n\n          <Alert\n            severity=\"warning\"\n            sx={{ mt: 2 }}\n            action={\n              <Button\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => navigate(\"/\")}\n                variant=\"outlined\"\n              >\n                Login\n              </Button>\n            }\n          >\n            Please log in to access the Google Geo Grid functionality. You can\n            still explore the basic features, but saving configurations requires\n            authentication.\n          </Alert>\n\n          {/* Show limited functionality for non-authenticated users */}\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Paper sx={{ p: 2, height: \"fit-content\" }}>\n                <GeoGridControls\n                  onLocationSearch={handleLocationSearch}\n                  onGenerateGrid={generateGrid}\n                  onSaveConfiguration={() =>\n                    setError(\"Please log in to save configurations\")\n                  }\n                  loading={loading}\n                  currentLocation={currentLocation}\n                  savedConfigurations={[]}\n                  onLoadConfiguration={() => {}}\n                  onDeleteConfiguration={() => {}}\n                  configurationName={gridConfiguration.name}\n                  onConfigurationNameChange={handleConfigurationNameChange}\n                />\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Paper sx={{ p: 2, height: 600 }}>\n                <GeoGridMap\n                  center={\n                    currentLocation\n                      ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                      : null\n                  }\n                  gridPoints={gridPoints}\n                  loading={loading}\n                />\n              </Paper>\n            </Grid>\n          </Grid>\n\n          {/* Snackbar for notifications */}\n          <Snackbar\n            open={!!error}\n            autoHideDuration={6000}\n            onClose={handleCloseSnackbar}\n            anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n          >\n            <Alert\n              onClose={handleCloseSnackbar}\n              severity=\"error\"\n              sx={{ width: \"100%\" }}\n            >\n              {error}\n            </Alert>\n          </Snackbar>\n\n          <Snackbar\n            open={!!success}\n            autoHideDuration={4000}\n            onClose={handleCloseSnackbar}\n            anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n          >\n            <Alert\n              onClose={handleCloseSnackbar}\n              severity=\"success\"\n              sx={{ width: \"100%\" }}\n            >\n              {success}\n            </Alert>\n          </Snackbar>\n        </Box>\n      </LeftMenuComponent>\n    );\n  }\n\n  return (\n    <LeftMenuComponent>\n      <Box>\n        <Box sx={{ marginBottom: \"5px\" }}>\n          <h3 className=\"pageTitle\">Google Geo Grid</h3>\n          <Typography variant=\"subtitle2\" className=\"subtitle2\">\n            Create and manage location-based grids for your business analysis\n          </Typography>\n        </Box>\n\n        <Grid container spacing={3}>\n          {/* Controls Panel */}\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 2, height: \"fit-content\" }}>\n              <GeoGridControls\n                onLocationSearch={handleLocationSearch}\n                onGenerateGrid={generateGrid}\n                onSaveConfiguration={handleSaveConfiguration}\n                loading={loading}\n                currentLocation={currentLocation}\n                savedConfigurations={savedConfigurations}\n                onLoadConfiguration={handleLoadConfiguration}\n                onDeleteConfiguration={handleDeleteConfiguration}\n              />\n            </Paper>\n          </Grid>\n\n          {/* Map Panel */}\n          <Grid item xs={12} md={5}>\n            <Paper sx={{ p: 2, height: 600 }}>\n              <GeoGridMap\n                center={\n                  currentLocation\n                    ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                    : null\n                }\n                gridPoints={gridPoints}\n                loading={loading}\n              />\n            </Paper>\n          </Grid>\n\n          {/* Settings Panel */}\n          <Grid item xs={12} md={3}>\n            <Paper sx={{ p: 2, height: \"fit-content\" }}>\n              <GeoGridSettings\n                configuration={gridConfiguration}\n                onConfigurationChange={handleConfigurationChange}\n                onGenerateGrid={generateGrid}\n                loading={loading}\n              />\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Snackbar for notifications */}\n        <Snackbar\n          open={!!error}\n          autoHideDuration={6000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"error\"\n            sx={{ width: \"100%\" }}\n          >\n            {error}\n          </Alert>\n        </Snackbar>\n\n        <Snackbar\n          open={!!success}\n          autoHideDuration={4000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"success\"\n            sx={{ width: \"100%\" }}\n          >\n            {success}\n          </Alert>\n        </Snackbar>\n      </Box>\n    </LeftMenuComponent>\n  );\n};\n\nexport default GeoGridScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAqB,OAAO;AAC/D,SACEC,GAAG,EAEHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,UAAU,MAAM,+CAA+C;AACtE,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,cAAc,MAId,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAchD,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAIgB,cAAc,CAACM,QAAQ,CAAC,CAAC;EAC/D,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CACpD,IACF,CAAC;EACD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACmC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpC,QAAQ,CACxD;IACEqC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,QAAQ,EAAE,CAAC;EACb,CACF,CAAC;;EAED;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlD,QAAQ,CAE5D,EAAE,CAAC;EAEL,MAAMmD,IAAI,GAAG1C,WAAW,CAAE2C,KAAU;IAAA,IAAAC,kBAAA;IAAA,QAAAA,kBAAA,GAAKD,KAAK,CAACE,WAAW,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,QAAQ;EAAA,EAAC;EAErEtD,SAAS,CAAC,MAAM;IACduD,QAAQ,CAACpC,KAAK,GAAGA,KAAK;IACtB,IAAI+B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,EAAE;MACZC,uBAAuB,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACtC,KAAK,EAAE+B,IAAI,CAAC,CAAC;EAEjB,MAAMO,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,EAACP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IAEf,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,QAAQ,GAAG,MAAMnC,cAAc,CAACoC,qBAAqB,CAACT,IAAI,CAACM,EAAE,CAAC;MACpE,IAAIE,QAAQ,CAAC9B,OAAO,EAAE;QACpBqB,sBAAsB,CAACS,QAAQ,CAACE,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOlC,KAAU,EAAE;MACnBC,QAAQ,CAAC,qCAAqC,CAAC;IACjD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAG,MAAOC,aAAoC,IAAK;IAC3E,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM+B,QAAQ,GAAG,MAAMnC,cAAc,CAACwC,cAAc,CAACD,aAAa,CAAC;MAEnE,IAAIJ,QAAQ,CAAC9B,OAAO,EAAE;QACpB,MAAMoC,YAAY,GAAGN,QAAQ,CAACE,IAAI;QAClC7B,kBAAkB,CAACiC,YAAY,CAAC;QAChC7B,oBAAoB,CAAE8B,IAAI,KAAM;UAC9B,GAAGA,IAAI;UACP5B,SAAS,EAAE2B,YAAY,CAACE,GAAG;UAC3B5B,SAAS,EAAE0B,YAAY,CAACG,GAAG;UAC3BzB,UAAU,EAAEoB,aAAa,CAACpB,UAAU;UACpCC,WAAW,EAAEmB,aAAa,CAACM,KAAK,IAAI;QACtC,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,YAAY,CAACL,YAAY,CAACE,GAAG,EAAEF,YAAY,CAACG,GAAG,CAAC;QACtDtC,UAAU,CAAC,iDAAiD,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,2BAA2B,CAAC;IACxD,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAAA,CAAOhC,SAAkB,EAAEC,SAAkB,KAAK;IACrE,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,GAAG,GAAG7B,SAAS,IAAIH,iBAAiB,CAACG,SAAS;MACpD,MAAM8B,GAAG,GAAG7B,SAAS,IAAIJ,iBAAiB,CAACI,SAAS;MAEpD,IAAI,CAAC4B,GAAG,IAAI,CAACC,GAAG,EAAE;QAChB,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMb,QAAQ,GAAG,MAAMnC,cAAc,CAAC8C,YAAY,CAAC;QACjDhC,SAAS,EAAE6B,GAAG;QACd5B,SAAS,EAAE6B,GAAG;QACd5B,QAAQ,EAAEL,iBAAiB,CAACK,QAAQ;QACpCC,QAAQ,EAAEN,iBAAiB,CAACM,QAAQ;QACpCC,YAAY,EAAEP,iBAAiB,CAACO;MAClC,CAAC,CAAC;MAEF,IAAIiB,QAAQ,CAAC9B,OAAO,EAAE;QACpBK,aAAa,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,UAAU,CAAC;QACvCH,UAAU,CAAC,8BAA8B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,yBAAyB,CAAC;IACtD,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,yBAAyB,GAAIC,OAAmC,IAAK;IACzEtC,oBAAoB,CAAE8B,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,GAAGQ;IAAQ,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,6BAA6B,GAAItC,IAAY,IAAK;IACtDD,oBAAoB,CAAE8B,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE7B;IAAK,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,MAAMuC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACFlD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,EAACuB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;QACb,MAAM,IAAIe,KAAK,CAAC,sCAAsC,CAAC;MACzD;MAEA,IAAI,CAACrC,iBAAiB,CAACE,IAAI,CAACwC,IAAI,CAAC,CAAC,EAAE;QAClC,MAAM,IAAIL,KAAK,CAAC,gCAAgC,CAAC;MACnD;MAEA,MAAMM,YAAY,GAAG;QACnB,GAAG3C,iBAAiB;QACpB4C,MAAM,EAAE5B,IAAI,CAACM,EAAE;QACfxB;MACF,CAAC;MAED,MAAM0B,QAAQ,GAAG,MAAMnC,cAAc,CAACwD,qBAAqB,CAACF,YAAY,CAAC;MAEzE,IAAInB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,mCAAmC,CAAC;QAC/C,MAAM4B,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuD,uBAAuB,GAAG,MAAOC,MAAyB,IAAK;IACnE,IAAI;MACFxD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIsD,MAAM,CAACzB,EAAE,EAAE;QACb,MAAME,QAAQ,GAAG,MAAMnC,cAAc,CAAC2D,WAAW,CAACD,MAAM,CAACzB,EAAE,CAAC2B,QAAQ,CAAC,CAAC,CAAC;QACvE,IAAIzB,QAAQ,CAAC9B,OAAO,EAAE;UACpB,MAAM;YAAEwD,aAAa;YAAEpD,UAAU,EAAEqD;UAAa,CAAC,GAAG3B,QAAQ,CAACE,IAAI;UACjEzB,oBAAoB,CAACiD,aAAa,CAAC;UACnCnD,aAAa,CAACoD,YAAY,IAAI,EAAE,CAAC;UACjCtD,kBAAkB,CAAC;YACjBK,IAAI,EAAEgD,aAAa,CAACzC,WAAW,IAAI,iBAAiB;YACpDuB,GAAG,EAAEkB,aAAa,CAAC/C,SAAS;YAC5B8B,GAAG,EAAEiB,aAAa,CAAC9C,SAAS;YAC5BgD,OAAO,EAAE,EAAE;YACXC,OAAO,EAAE;UACX,CAAC,CAAC;UACF1D,UAAU,CAAC,oCAAoC,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,yBAAyB,GAAG,MAAOC,QAAgB,IAAK;IAC5D,IAAI;MACFhE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM+B,QAAQ,GAAG,MAAMnC,cAAc,CAACmE,uBAAuB,CAC3DD,QAAQ,CAACN,QAAQ,CAAC,CACpB,CAAC;MAED,IAAIzB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjD,MAAM4B,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,gCAAgC,CAAC;IAC7D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkE,mBAAmB,GAAGA,CAAA,KAAM;IAChChE,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;;EAED;EACA,IAAI,EAACqB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IACb,oBACEvC,OAAA,CAACN,iBAAiB;MAAAiF,QAAA,eAChB3E,OAAA,CAAChB,GAAG;QAAA2F,QAAA,gBACF3E,OAAA,CAAChB,GAAG;UAAC4F,EAAE,EAAE;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBAC/B3E,OAAA;YAAI8E,SAAS,EAAC,WAAW;YAAAH,QAAA,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ClF,OAAA,CAACf,UAAU;YAACkG,OAAO,EAAC,WAAW;YAACL,SAAS,EAAC,WAAW;YAAAH,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENlF,OAAA,CAACX,KAAK;UACJ+F,QAAQ,EAAC,SAAS;UAClBR,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UACdC,MAAM,eACJtF,OAAA,CAACZ,MAAM;YACLmG,KAAK,EAAC,SAAS;YACfC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMpF,QAAQ,CAAC,GAAG,CAAE;YAC7B8E,OAAO,EAAC,UAAU;YAAAR,QAAA,EACnB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAP,QAAA,EACF;QAID;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAGRlF,OAAA,CAACb,IAAI;UAACuG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzB3E,OAAA,CAACb,IAAI;YAACyG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvB3E,OAAA,CAACd,KAAK;cAAC0F,EAAE,EAAE;gBAAEmB,CAAC,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAc,CAAE;cAAArB,QAAA,eACzC3E,OAAA,CAACL,eAAe;gBACdsG,gBAAgB,EAAErD,oBAAqB;gBACvCsD,cAAc,EAAE9C,YAAa;gBAC7B+C,mBAAmB,EAAEA,CAAA,KACnBzF,QAAQ,CAAC,sCAAsC,CAChD;gBACDH,OAAO,EAAEA,OAAQ;gBACjBM,eAAe,EAAEA,eAAgB;gBACjCkB,mBAAmB,EAAE,EAAG;gBACxBqE,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAE;gBAC9BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAE;gBAChCC,iBAAiB,EAAErF,iBAAiB,CAACE,IAAK;gBAC1CoF,yBAAyB,EAAE9C;cAA8B;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEPlF,OAAA,CAACb,IAAI;YAACyG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvB3E,OAAA,CAACd,KAAK;cAAC0F,EAAE,EAAE;gBAAEmB,CAAC,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAAArB,QAAA,eAC/B3E,OAAA,CAACJ,UAAU;gBACT4G,MAAM,EACJ3F,eAAe,GACX;kBAAEoC,GAAG,EAAEpC,eAAe,CAACoC,GAAG;kBAAEC,GAAG,EAAErC,eAAe,CAACqC;gBAAI,CAAC,GACtD,IACL;gBACDnC,UAAU,EAAEA,UAAW;gBACvBR,OAAO,EAAEA;cAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlF,OAAA,CAACV,QAAQ;UACPmH,IAAI,EAAE,CAAC,CAAChG,KAAM;UACdiG,gBAAgB,EAAE,IAAK;UACvBC,OAAO,EAAEjC,mBAAoB;UAC7BkC,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAE;UAAAnC,QAAA,eAE1D3E,OAAA,CAACX,KAAK;YACJsH,OAAO,EAAEjC,mBAAoB;YAC7BU,QAAQ,EAAC,OAAO;YAChBR,EAAE,EAAE;cAAEmC,KAAK,EAAE;YAAO,CAAE;YAAApC,QAAA,EAErBlE;UAAK;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEXlF,OAAA,CAACV,QAAQ;UACPmH,IAAI,EAAE,CAAC,CAAC9F,OAAQ;UAChB+F,gBAAgB,EAAE,IAAK;UACvBC,OAAO,EAAEjC,mBAAoB;UAC7BkC,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAE;UAAAnC,QAAA,eAE1D3E,OAAA,CAACX,KAAK;YACJsH,OAAO,EAAEjC,mBAAoB;YAC7BU,QAAQ,EAAC,SAAS;YAClBR,EAAE,EAAE;cAAEmC,KAAK,EAAE;YAAO,CAAE;YAAApC,QAAA,EAErBhE;UAAO;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAExB;EAEA,oBACElF,OAAA,CAACN,iBAAiB;IAAAiF,QAAA,eAChB3E,OAAA,CAAChB,GAAG;MAAA2F,QAAA,gBACF3E,OAAA,CAAChB,GAAG;QAAC4F,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAAF,QAAA,gBAC/B3E,OAAA;UAAI8E,SAAS,EAAC,WAAW;UAAAH,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ClF,OAAA,CAACf,UAAU;UAACkG,OAAO,EAAC,WAAW;UAACL,SAAS,EAAC,WAAW;UAAAH,QAAA,EAAC;QAEtD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAENlF,OAAA,CAACb,IAAI;QAACuG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhB,QAAA,gBAEzB3E,OAAA,CAACb,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB3E,OAAA,CAACd,KAAK;YAAC0F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAc,CAAE;YAAArB,QAAA,eACzC3E,OAAA,CAACL,eAAe;cACdsG,gBAAgB,EAAErD,oBAAqB;cACvCsD,cAAc,EAAE9C,YAAa;cAC7B+C,mBAAmB,EAAEzC,uBAAwB;cAC7CnD,OAAO,EAAEA,OAAQ;cACjBM,eAAe,EAAEA,eAAgB;cACjCkB,mBAAmB,EAAEA,mBAAoB;cACzCqE,mBAAmB,EAAErC,uBAAwB;cAC7CsC,qBAAqB,EAAE9B;YAA0B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPlF,OAAA,CAACb,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB3E,OAAA,CAACd,KAAK;YAAC0F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAI,CAAE;YAAArB,QAAA,eAC/B3E,OAAA,CAACJ,UAAU;cACT4G,MAAM,EACJ3F,eAAe,GACX;gBAAEoC,GAAG,EAAEpC,eAAe,CAACoC,GAAG;gBAAEC,GAAG,EAAErC,eAAe,CAACqC;cAAI,CAAC,GACtD,IACL;cACDnC,UAAU,EAAEA,UAAW;cACvBR,OAAO,EAAEA;YAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPlF,OAAA,CAACb,IAAI;UAACyG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvB3E,OAAA,CAACd,KAAK;YAAC0F,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAc,CAAE;YAAArB,QAAA,eACzC3E,OAAA,CAACH,eAAe;cACdsE,aAAa,EAAElD,iBAAkB;cACjC+F,qBAAqB,EAAEzD,yBAA0B;cACjD2C,cAAc,EAAE9C,YAAa;cAC7B7C,OAAO,EAAEA;YAAQ;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlF,OAAA,CAACV,QAAQ;QACPmH,IAAI,EAAE,CAAC,CAAChG,KAAM;QACdiG,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAEjC,mBAAoB;QAC7BkC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAnC,QAAA,eAE1D3E,OAAA,CAACX,KAAK;UACJsH,OAAO,EAAEjC,mBAAoB;UAC7BU,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEmC,KAAK,EAAE;UAAO,CAAE;UAAApC,QAAA,EAErBlE;QAAK;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEXlF,OAAA,CAACV,QAAQ;QACPmH,IAAI,EAAE,CAAC,CAAC9F,OAAQ;QAChB+F,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAEjC,mBAAoB;QAC7BkC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAnC,QAAA,eAE1D3E,OAAA,CAACX,KAAK;UACJsH,OAAO,EAAEjC,mBAAoB;UAC7BU,QAAQ,EAAC,SAAS;UAClBR,EAAE,EAAE;YAAEmC,KAAK,EAAE;UAAO,CAAE;UAAApC,QAAA,EAErBhE;QAAO;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAExB,CAAC;AAAC/E,EAAA,CAlZIF,aAA2C;EAAA,QAC9BT,WAAW,EACXC,WAAW,EAgCfF,WAAW;AAAA;AAAA0H,EAAA,GAlCpBhH,aAA2C;AAoZjD,eAAeA,aAAa;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}