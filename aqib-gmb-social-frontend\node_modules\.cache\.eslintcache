[{"C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx": "4", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx": "5", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx": "6", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx": "7", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx": "8", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx": "9", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx": "10", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx": "11", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx": "12", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx": "13", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx": "14", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx": "15", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx": "16", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx": "17", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx": "18", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx": "19", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx": "20", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx": "21", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx": "22", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx": "23", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx": "24", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx": "25", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx": "26", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx": "27", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx": "28", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx": "29", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx": "30", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx": "31", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx": "32", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx": "33", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx": "34", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx": "35", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx": "36", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx": "37", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx": "38", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx": "39", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx": "40", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx": "41", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx": "42", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx": "43", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx": "44", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx": "45", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx": "46", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx": "47", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx": "48", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx": "49", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx": "50", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx": "51", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx": "52", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx": "53", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx": "54", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx": "55", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx": "56", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx": "57", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx": "58", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx": "59", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx": "60", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx": "61", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx": "62", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx": "63", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx": "64", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx": "65", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx": "66", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx": "67", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx": "68", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx": "69", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx": "70", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx": "71", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx": "72", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx": "73", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx": "74", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx": "75", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx": "76", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx": "77", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx": "78", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx": "79", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx": "80", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx": "81", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx": "82", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx": "83", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx": "84", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx": "85", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx": "86", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx": "87", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx": "88", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx": "89", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx": "90", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx": "91", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx": "92", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx": "93", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx": "94", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx": "95", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx": "96", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx": "97", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx": "98", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx": "99", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx": "100", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx": "101", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx": "102", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx": "103", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx": "104", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx": "105", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx": "106", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx": "107", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx": "108", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx": "109", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx": "110", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx": "111", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx": "112", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx": "113", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx": "114", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx": "115", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx": "116", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx": "117", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx": "118", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx": "119", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts": "120", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx": "121", "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts": "122"}, {"size": 1322, "mtime": 1735557527698, "results": "123", "hashOfConfig": "124"}, {"size": 425, "mtime": 1732825514676, "results": "125", "hashOfConfig": "124"}, {"size": 11901, "mtime": 1748626548303, "results": "126", "hashOfConfig": "124"}, {"size": 1924, "mtime": 1748447423371, "results": "127", "hashOfConfig": "124"}, {"size": 1261, "mtime": 1745417383983, "results": "128", "hashOfConfig": "124"}, {"size": 457, "mtime": 1734026524277, "results": "129", "hashOfConfig": "124"}, {"size": 157, "mtime": 1701682230464, "results": "130", "hashOfConfig": "124"}, {"size": 3060, "mtime": 1745228037979, "results": "131", "hashOfConfig": "124"}, {"size": 118, "mtime": 1734026414980, "results": "132", "hashOfConfig": "124"}, {"size": 2661, "mtime": 1743422802376, "results": "133", "hashOfConfig": "124"}, {"size": 11012, "mtime": 1745224565621, "results": "134", "hashOfConfig": "124"}, {"size": 14227, "mtime": 1745153279478, "results": "135", "hashOfConfig": "124"}, {"size": 22162, "mtime": 1746354630774, "results": "136", "hashOfConfig": "124"}, {"size": 228, "mtime": 1732915981749, "results": "137", "hashOfConfig": "124"}, {"size": 72602, "mtime": 1748504384752, "results": "138", "hashOfConfig": "124"}, {"size": 71530, "mtime": 1748273135277, "results": "139", "hashOfConfig": "124"}, {"size": 23584, "mtime": 1748081008306, "results": "140", "hashOfConfig": "124"}, {"size": 4224, "mtime": 1747927064178, "results": "141", "hashOfConfig": "124"}, {"size": 2557, "mtime": 1747926509581, "results": "142", "hashOfConfig": "124"}, {"size": 92285, "mtime": 1748096498620, "results": "143", "hashOfConfig": "124"}, {"size": 12381, "mtime": 1748627638574, "results": "144", "hashOfConfig": "124"}, {"size": 37116, "mtime": 1748283097919, "results": "145", "hashOfConfig": "124"}, {"size": 22457, "mtime": 1748268107744, "results": "146", "hashOfConfig": "124"}, {"size": 25334, "mtime": 1748622303774, "results": "147", "hashOfConfig": "124"}, {"size": 56467, "mtime": 1748103534373, "results": "148", "hashOfConfig": "124"}, {"size": 30332, "mtime": 1748091334232, "results": "149", "hashOfConfig": "124"}, {"size": 4594, "mtime": 1739004178483, "results": "150", "hashOfConfig": "124"}, {"size": 43075, "mtime": 1748262014290, "results": "151", "hashOfConfig": "124"}, {"size": 991, "mtime": 1745432980413, "results": "152", "hashOfConfig": "124"}, {"size": 1802, "mtime": 1745154549344, "results": "153", "hashOfConfig": "124"}, {"size": 18225, "mtime": 1748096373981, "results": "154", "hashOfConfig": "124"}, {"size": 347, "mtime": 1748514901624, "results": "155", "hashOfConfig": "124"}, {"size": 1880, "mtime": 1746361169966, "results": "156", "hashOfConfig": "124"}, {"size": 1876, "mtime": 1746360898380, "results": "157", "hashOfConfig": "124"}, {"size": 4165, "mtime": 1748626411286, "results": "158", "hashOfConfig": "124"}, {"size": 384, "mtime": 1745418826460, "results": "159", "hashOfConfig": "124"}, {"size": 9311, "mtime": 1743515029706, "results": "160", "hashOfConfig": "124"}, {"size": 1088, "mtime": 1732882009247, "results": "161", "hashOfConfig": "124"}, {"size": 327, "mtime": 1737044880500, "results": "162", "hashOfConfig": "124"}, {"size": 1953, "mtime": 1747674177440, "results": "163", "hashOfConfig": "124"}, {"size": 20198, "mtime": 1748628612055, "results": "164", "hashOfConfig": "124"}, {"size": 3187, "mtime": 1745654081462, "results": "165", "hashOfConfig": "124"}, {"size": 3899, "mtime": 1748504225839, "results": "166", "hashOfConfig": "124"}, {"size": 4028, "mtime": 1748447576248, "results": "167", "hashOfConfig": "124"}, {"size": 1997, "mtime": 1741184787031, "results": "168", "hashOfConfig": "124"}, {"size": 1170, "mtime": 1745152642022, "results": "169", "hashOfConfig": "124"}, {"size": 1016, "mtime": 1741184738204, "results": "170", "hashOfConfig": "124"}, {"size": 2779, "mtime": 1748275515999, "results": "171", "hashOfConfig": "124"}, {"size": 1440, "mtime": 1741157415799, "results": "172", "hashOfConfig": "124"}, {"size": 2104, "mtime": 1741175030313, "results": "173", "hashOfConfig": "124"}, {"size": 1687, "mtime": 1745173540907, "results": "174", "hashOfConfig": "124"}, {"size": 4969, "mtime": 1741157515080, "results": "175", "hashOfConfig": "124"}, {"size": 1007, "mtime": 1743912188289, "results": "176", "hashOfConfig": "124"}, {"size": 4015, "mtime": 1748504257458, "results": "177", "hashOfConfig": "124"}, {"size": 2108, "mtime": 1743964958327, "results": "178", "hashOfConfig": "124"}, {"size": 3524, "mtime": 1748452767055, "results": "179", "hashOfConfig": "124"}, {"size": 6531, "mtime": 1747674884828, "results": "180", "hashOfConfig": "124"}, {"size": 1066, "mtime": 1745130929150, "results": "181", "hashOfConfig": "124"}, {"size": 3176, "mtime": 1748356415373, "results": "182", "hashOfConfig": "124"}, {"size": 42711, "mtime": 1748268104980, "results": "183", "hashOfConfig": "124"}, {"size": 8043, "mtime": 1745778157886, "results": "184", "hashOfConfig": "124"}, {"size": 4054, "mtime": 1748275903493, "results": "185", "hashOfConfig": "124"}, {"size": 1840, "mtime": 1743959659084, "results": "186", "hashOfConfig": "124"}, {"size": 1521, "mtime": 1748447423366, "results": "187", "hashOfConfig": "124"}, {"size": 7403, "mtime": 1747927040597, "results": "188", "hashOfConfig": "124"}, {"size": 8129, "mtime": 1747926970546, "results": "189", "hashOfConfig": "124"}, {"size": 12230, "mtime": 1748096498612, "results": "190", "hashOfConfig": "124"}, {"size": 9701, "mtime": 1748096498609, "results": "191", "hashOfConfig": "124"}, {"size": 7411, "mtime": 1747983900839, "results": "192", "hashOfConfig": "124"}, {"size": 7645, "mtime": 1748096498642, "results": "193", "hashOfConfig": "124"}, {"size": 7764, "mtime": 1748096498625, "results": "194", "hashOfConfig": "124"}, {"size": 8438, "mtime": 1748096498645, "results": "195", "hashOfConfig": "124"}, {"size": 9418, "mtime": 1748096498628, "results": "196", "hashOfConfig": "124"}, {"size": 8390, "mtime": 1748096498661, "results": "197", "hashOfConfig": "124"}, {"size": 7694, "mtime": 1748096498633, "results": "198", "hashOfConfig": "124"}, {"size": 8842, "mtime": 1748096498648, "results": "199", "hashOfConfig": "124"}, {"size": 8910, "mtime": 1748096498650, "results": "200", "hashOfConfig": "124"}, {"size": 9753, "mtime": 1748096498658, "results": "201", "hashOfConfig": "124"}, {"size": 14253, "mtime": 1748628046726, "results": "202", "hashOfConfig": "124"}, {"size": 8526, "mtime": 1748626529383, "results": "203", "hashOfConfig": "124"}, {"size": 3621, "mtime": 1748627420404, "results": "204", "hashOfConfig": "124"}, {"size": 6848, "mtime": 1748626503234, "results": "205", "hashOfConfig": "124"}, {"size": 751, "mtime": 1739004316101, "results": "206", "hashOfConfig": "124"}, {"size": 2655, "mtime": 1747673914361, "results": "207", "hashOfConfig": "124"}, {"size": 2179, "mtime": 1748282005004, "results": "208", "hashOfConfig": "124"}, {"size": 62073, "mtime": 1748268099200, "results": "209", "hashOfConfig": "124"}, {"size": 881, "mtime": 1739736032570, "results": "210", "hashOfConfig": "124"}, {"size": 3505, "mtime": 1747674887530, "results": "211", "hashOfConfig": "124"}, {"size": 825, "mtime": 1748091399276, "results": "212", "hashOfConfig": "124"}, {"size": 4855, "mtime": 1745166216216, "results": "213", "hashOfConfig": "124"}, {"size": 3676, "mtime": 1745152642016, "results": "214", "hashOfConfig": "124"}, {"size": 1151, "mtime": 1745646436581, "results": "215", "hashOfConfig": "124"}, {"size": 9176, "mtime": 1747683781666, "results": "216", "hashOfConfig": "124"}, {"size": 2338, "mtime": 1745655282096, "results": "217", "hashOfConfig": "124"}, {"size": 3305, "mtime": 1748103773906, "results": "218", "hashOfConfig": "124"}, {"size": 1668, "mtime": 1745167749805, "results": "219", "hashOfConfig": "124"}, {"size": 607, "mtime": 1743920400746, "results": "220", "hashOfConfig": "124"}, {"size": 481, "mtime": 1737047216070, "results": "221", "hashOfConfig": "124"}, {"size": 6596, "mtime": 1748262291113, "results": "222", "hashOfConfig": "124"}, {"size": 15273, "mtime": 1745778157866, "results": "223", "hashOfConfig": "124"}, {"size": 8516, "mtime": 1748275747346, "results": "224", "hashOfConfig": "124"}, {"size": 1191, "mtime": 1746359987224, "results": "225", "hashOfConfig": "124"}, {"size": 2245, "mtime": 1746356711188, "results": "226", "hashOfConfig": "124"}, {"size": 12541, "mtime": 1744000300023, "results": "227", "hashOfConfig": "124"}, {"size": 3452, "mtime": 1743915304875, "results": "228", "hashOfConfig": "124"}, {"size": 506, "mtime": 1745432977071, "results": "229", "hashOfConfig": "124"}, {"size": 6166, "mtime": 1745223603623, "results": "230", "hashOfConfig": "124"}, {"size": 2617, "mtime": 1748496622200, "results": "231", "hashOfConfig": "124"}, {"size": 3400, "mtime": 1743780827843, "results": "232", "hashOfConfig": "124"}, {"size": 2900, "mtime": 1743926009788, "results": "233", "hashOfConfig": "124"}, {"size": 1394, "mtime": 1743924141624, "results": "234", "hashOfConfig": "124"}, {"size": 5825, "mtime": 1743778841267, "results": "235", "hashOfConfig": "124"}, {"size": 9763, "mtime": 1739741482429, "results": "236", "hashOfConfig": "124"}, {"size": 2929, "mtime": 1741701510631, "results": "237", "hashOfConfig": "124"}, {"size": 4699, "mtime": 1741701603500, "results": "238", "hashOfConfig": "124"}, {"size": 2178, "mtime": 1741701600352, "results": "239", "hashOfConfig": "124"}, {"size": 3248, "mtime": 1741701606568, "results": "240", "hashOfConfig": "124"}, {"size": 3430, "mtime": 1741701609669, "results": "241", "hashOfConfig": "124"}, {"size": 3100, "mtime": 1743924130802, "results": "242", "hashOfConfig": "124"}, {"size": 13705, "mtime": 1748504200123, "results": "243", "hashOfConfig": "124"}, {"size": 168, "mtime": 1701682230489, "results": "244", "hashOfConfig": "124"}, {"size": 4815, "mtime": 1748628188151, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qj6s6v", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\App.tsx", ["612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\theme.tsx", ["631"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\store\\index.tsx", ["632"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\toast.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\loading.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\auth.actions.tsx", ["633", "634", "635", "636", "637", "638"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\toastSeverity.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\forgotPassword\\forgotPassword.screen.tsx", ["639"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\signIn\\signIn.screen.tsx", ["640", "641", "642", "643", "644", "645", "646", "647", "648"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboard\\dashboard.screen.tsx", ["649"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\qanda\\qanda.screen.tsx", ["650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\help\\help.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\analytics\\analytics.screen.tsx", ["663", "664", "665", "666"], ["667"], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\createSocialPost.screen.tsx", ["668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\dashboardV2.screen.tsx", ["688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\businessCategory.screen.tsx", ["710"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\servicesDemo.screen.tsx", ["711", "712", "713"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\demo.screen.tsx", ["714", "715", "716", "717", "718", "719"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\geoGrid\\geoGrid.screen.tsx", ["720", "721", "722", "723", "724"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\roles\\roles.screen.tsx", ["725", "726", "727", "728", "729", "730", "731", "732", "733", "734"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\userManagement\\users\\users.screen.tsx", ["735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\manageBusiness\\manageBusiness.screen.tsx", ["748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\businessSummary\\businessSummary.screen.tsx", ["761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\localBusiness\\localBusiness.screen.tsx", ["778", "779", "780", "781", "782", "783", "784", "785", "786", "787"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessManagement\\callback\\callback.screen.tsx", ["788", "789", "790", "791"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\reviewManagement\\manageReviews\\manageReviews.screen.tsx", ["792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\userPreferences.reducer.tsx", ["821", "822", "823", "824", "825", "826", "827"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\reducers\\auth.reducer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\PostListing.screen.tsx", ["828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\loader\\loader.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\unAuthorized\\notFoundPage.component.tsx", ["843"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\notFoundPage\\notFoundPage.component.tsx", ["844"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\endPoints.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\reducer.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\httpHelper.service.tsx", ["845", "846"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\helperService.tsx", ["847", "848"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\dbConstant.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\message.constant.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\leftMenu\\leftMenu.component.tsx", ["849", "850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\ApplicationHelperService.tsx", ["867", "868"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\websiteClicksChart.tsx", ["869", "870"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\platformBreakdownChart.tsx", ["871"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\registeredEmployees.charts.tsx", ["872", "873", "874"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\activeJobs.charts.tsx", ["875"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\charts\\pie.charts.tsx", ["876", "877"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\homeChartCard\\homeChartCard.component.tsx", ["878", "879"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\qanda\\qanda.service.tsx", ["880"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\business\\business.service.tsx", ["881"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\location\\location.service.tsx", ["882"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyQuestionAnswer\\replyQuestionAnswer.component.tsx", ["883", "884", "885"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\genericDrawer\\genericDrawer.component.tsx", ["886", "887", "888", "889"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\revenueChartDashboard\\revenueChartDashboard.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\constants\\application.constant.tsx", ["890"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\locationChips\\locationChips.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\dateFilter\\dateFilter.component.tsx", ["891"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\locationMetrics\\locationMetrics.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\InfoCard.screen.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\createSocialPost\\components\\submitPost.component.tsx", ["892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\searchBreakdown.tsx", ["925", "926", "927", "928", "929"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\dashboardV2\\businessProfileInteractionsChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\posts\\posts.service.tsx", ["930", "931", "932"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addBusinessCategory.component.tsx", ["933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\servicesDisplay.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\addServices.component.tsx", ["953"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\BusinessHours.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\businessCategory\\components\\AddSpecialHours.tsx", ["954", "955", "956", "957", "958"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editBusinessName\\editBusinessName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\FromTheBusiness.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\AddChildren.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\MoreAccessibility.tsx", ["959"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\Amenities.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ServiceOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\CrowdComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\ParkingComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PaymentsComponent.tsx", ["960"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\moreActivity\\PlanningComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridControls.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridMap.component.tsx", ["961", "962"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\geoGrid\\geoGrid.service.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\geoGrid\\GeoGridSettings.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\roles\\roles.service.tsx", ["963", "964"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\user\\user.service.tsx", ["965", "966", "967"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\confirmModel\\confirmModel.component.tsx", ["968", "969"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditUser\\addEditUser.component.tsx", ["970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\auth\\auth.service.tsx", ["982", "983"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\alertDialog\\alertDialog.component.tsx", ["984"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\noRowsFound\\noRowsFound.component.tsx", ["985", "986", "987"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\logosPhotosDisplay\\logsPhotosDisplay.component.tsx", ["988", "989", "990"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\categoryDisplay\\categoryDisplay.component.tsx", ["991", "992", "993", "994"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\LinearProgressWithLabel\\LinearProgressWithLabel.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\addEditBusiness\\addEditBusiness.component.tsx", ["995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceItemsDisplay\\serviceItemsDisplay.component.tsx", ["1006", "1007", "1008", "1009", "1010"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\regularHoursTable\\regularHoursTable.component.tsx", ["1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\serviceAreaList\\serviceAreaList.component.tsx", ["1022", "1023", "1024"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\iconOnAvailability\\iconOnAvailability.component.tsx", ["1025", "1026", "1027"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatarWIthName\\userAvatarWIthName.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\replyReviews\\replyReviews.component.tsx", ["1028", "1029", "1030", "1031"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPost.component.tsx", ["1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createTags\\createTags.component.tsx", ["1045", "1046", "1047", "1048", "1049", "1050"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\ratingsStar\\ratingsStar.component.tsx", ["1051", "1052", "1053"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\review\\review.service.tsx", ["1054"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\listing\\postCard\\postCard.screen.tsx", ["1055", "1056", "1057", "1058", "1059", "1060"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\screens\\posts\\updatesSection\\updatesSection.screen.tsx", ["1061", "1062"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\actions\\userPreferences.actions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\menuListItemNested\\menuListItemNested.component.tsx", ["1063", "1064", "1065"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\exportButton\\exportButton.component.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\header\\header.component.tsx", ["1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\mediaGallery\\mediaGallery.component.tsx", ["1076", "1077"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\userAvatar\\userAvatar.component.tsx", ["1078"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\editElements\\editElements.component.tsx", ["1079", "1080", "1081"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\colorPalette\\colorPalette.component.tsx", ["1082", "1083", "1084"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard1\\testimonialCard1.component.tsx", ["1085"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard3\\testimonialCard3.component.tsx", ["1086", "1087", "1088", "1089"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard2\\testimonialCard2.component.tsx", ["1090", "1091", "1092"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard4\\testimonialCard4.component.tsx", ["1093", "1094", "1095", "1096"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard5\\testimonialCard5.component.tsx", ["1097", "1098", "1099", "1100", "1101"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\components\\createPost\\createPostTemplates\\cards\\testimonialCard6\\testimonialCard6.component.tsx", ["1102", "1103", "1104", "1105"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\services\\excelExport.service.ts", ["1106"], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\context\\preferences.context.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-frontend\\src\\utils\\googleMaps.utils.ts", [], [], {"ruleId": "1107", "severity": 1, "message": "1108", "line": 3, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1111", "line": 9, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 11, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1113", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1114", "line": 13, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1115", "line": 16, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1116", "line": 19, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1117", "line": 59, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 59, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1118", "line": 59, "column": 26, "nodeType": "1109", "messageId": "1110", "endLine": 59, "endColumn": 43}, {"ruleId": "1119", "severity": 1, "message": "1120", "line": 75, "column": 5, "nodeType": "1121", "endLine": 75, "endColumn": 14, "suggestions": "1122"}, {"ruleId": "1119", "severity": 1, "message": "1123", "line": 83, "column": 5, "nodeType": "1121", "endLine": 83, "endColumn": 14, "suggestions": "1124"}, {"ruleId": "1107", "severity": 1, "message": "1125", "line": 94, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 94, "endColumn": 26}, {"ruleId": "1119", "severity": 1, "message": "1126", "line": 98, "column": 5, "nodeType": "1121", "endLine": 98, "endColumn": 21, "suggestions": "1127"}, {"ruleId": "1119", "severity": 1, "message": "1128", "line": 126, "column": 6, "nodeType": "1121", "endLine": 126, "endColumn": 25, "suggestions": "1129"}, {"ruleId": "1119", "severity": 1, "message": "1130", "line": 137, "column": 6, "nodeType": "1121", "endLine": 137, "endColumn": 22, "suggestions": "1131"}, {"ruleId": "1119", "severity": 1, "message": "1132", "line": 161, "column": 5, "nodeType": "1121", "endLine": 161, "endColumn": 11, "suggestions": "1133"}, {"ruleId": "1119", "severity": 1, "message": "1132", "line": 168, "column": 5, "nodeType": "1121", "endLine": 168, "endColumn": 11, "suggestions": "1134"}, {"ruleId": "1119", "severity": 1, "message": "1135", "line": 175, "column": 5, "nodeType": "1121", "endLine": 175, "endColumn": 18, "suggestions": "1136"}, {"ruleId": "1119", "severity": 1, "message": "1137", "line": 187, "column": 5, "nodeType": "1121", "endLine": 187, "endColumn": 33, "suggestions": "1138"}, {"ruleId": "1107", "severity": 1, "message": "1139", "line": 3, "column": 24, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 38}, {"ruleId": "1107", "severity": 1, "message": "1140", "line": 6, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1141", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 4, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1142", "line": 4, "column": 22, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 31}, {"ruleId": "1107", "severity": 1, "message": "1143", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1144", "line": 14, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1145", "line": 18, "column": 7, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1146", "line": 11, "column": 13, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1147", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1148", "line": 16, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1149", "line": 20, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1150", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1151", "line": 38, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 38, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 42, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 42, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1153", "line": 45, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 45, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1154", "line": 51, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 51, "endColumn": 30}, {"ruleId": "1119", "severity": 1, "message": "1155", "line": 79, "column": 6, "nodeType": "1121", "endLine": 79, "endColumn": 8, "suggestions": "1156"}, {"ruleId": "1107", "severity": 1, "message": "1157", "line": 27, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 27, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 17, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 18, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1160", "line": 64, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1161", "line": 64, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 33}, {"ruleId": "1107", "severity": 1, "message": "1162", "line": 67, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 67, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1145", "line": 69, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 69, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 71, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 71, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1163", "line": 73, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 73, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1164", "line": 81, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 81, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1165", "line": 95, "column": 25, "nodeType": "1109", "messageId": "1110", "endLine": 95, "endColumn": 41}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 134, "column": 56, "nodeType": "1168", "messageId": "1169", "endLine": 134, "endColumn": 58}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 139, "column": 57, "nodeType": "1168", "messageId": "1169", "endLine": 139, "endColumn": 59}, {"ruleId": "1119", "severity": 1, "message": "1170", "line": 146, "column": 6, "nodeType": "1121", "endLine": 146, "endColumn": 8, "suggestions": "1171"}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 7, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1172", "line": 13, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1173", "line": 140, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 140, "endColumn": 30}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 306, "column": 56, "nodeType": "1168", "messageId": "1169", "endLine": 306, "endColumn": 58}, {"ruleId": "1119", "severity": 1, "message": "1174", "line": 151, "column": 6, "nodeType": "1121", "endLine": 151, "endColumn": 8, "suggestions": "1175", "suppressions": "1176"}, {"ruleId": "1107", "severity": 1, "message": "1177", "line": 8, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1178", "line": 37, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 37, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1179", "line": 39, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 39, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1180", "line": 61, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 61, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1181", "line": 99, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 99, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1182", "line": 100, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 100, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1183", "line": 234, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 234, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1184", "line": 234, "column": 16, "nodeType": "1109", "messageId": "1110", "endLine": 234, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1185", "line": 235, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 235, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1186", "line": 235, "column": 28, "nodeType": "1109", "messageId": "1110", "endLine": 235, "endColumn": 47}, {"ruleId": "1119", "severity": 1, "message": "1187", "line": 303, "column": 6, "nodeType": "1121", "endLine": 303, "endColumn": 8, "suggestions": "1188"}, {"ruleId": "1107", "severity": 1, "message": "1189", "line": 305, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 305, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1190", "line": 307, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 307, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1191", "line": 577, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 577, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1192", "line": 882, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 882, "endColumn": 27}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 1407, "column": 71, "nodeType": "1168", "messageId": "1169", "endLine": 1407, "endColumn": 73}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 1422, "column": 42, "nodeType": "1168", "messageId": "1169", "endLine": 1422, "endColumn": 44}, {"ruleId": "1194", "severity": 1, "message": "1195", "line": 1653, "column": 19, "nodeType": "1196", "endLine": 1664, "endColumn": 21}, {"ruleId": "1194", "severity": 1, "message": "1195", "line": 1711, "column": 17, "nodeType": "1196", "endLine": 1724, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1197", "line": 1825, "column": 17, "nodeType": "1109", "messageId": "1110", "endLine": 1825, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1198", "line": 9, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1199", "line": 10, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1200", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1201", "line": 14, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1202", "line": 15, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 15, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1203", "line": 16, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1204", "line": 17, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1205", "line": 18, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1206", "line": 19, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1207", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 30}, {"ruleId": "1107", "severity": 1, "message": "1208", "line": 22, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1172", "line": 26, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 26, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1209", "line": 31, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 31, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1210", "line": 31, "column": 26, "nodeType": "1109", "messageId": "1110", "endLine": 31, "endColumn": 31}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 40, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 40, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1211", "line": 41, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 41, "endColumn": 33}, {"ruleId": "1107", "severity": 1, "message": "1212", "line": 43, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 43, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1213", "line": 66, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 66, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 72, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 72, "endColumn": 34}, {"ruleId": "1119", "severity": 1, "message": "1214", "line": 116, "column": 6, "nodeType": "1121", "endLine": 116, "endColumn": 8, "suggestions": "1215"}, {"ruleId": "1119", "severity": 1, "message": "1216", "line": 125, "column": 6, "nodeType": "1121", "endLine": 125, "endColumn": 26, "suggestions": "1217"}, {"ruleId": "1119", "severity": 1, "message": "1218", "line": 138, "column": 6, "nodeType": "1121", "endLine": 138, "endColumn": 45, "suggestions": "1219"}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 9, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 9, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1221", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1222", "line": 13, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1223", "line": 25, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 25, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1224", "line": 33, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 33, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1225", "line": 33, "column": 33, "nodeType": "1109", "messageId": "1110", "endLine": 33, "endColumn": 41}, {"ruleId": "1107", "severity": 1, "message": "1226", "line": 34, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 34, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1227", "line": 36, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 36, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1228", "line": 37, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 37, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1229", "line": 1, "column": 38, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 49}, {"ruleId": "1107", "severity": 1, "message": "1230", "line": 3, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 6}, {"ruleId": "1107", "severity": 1, "message": "1231", "line": 64, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1232", "line": 64, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 33}, {"ruleId": "1119", "severity": 1, "message": "1233", "line": 76, "column": 6, "nodeType": "1121", "endLine": 76, "endColumn": 19, "suggestions": "1234"}, {"ruleId": "1107", "severity": 1, "message": "1235", "line": 14, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1236", "line": 25, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 25, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1237", "line": 26, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 26, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1238", "line": 31, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 31, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 47, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 47, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1116", "line": 53, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 53, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 70, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 70, "endColumn": 34}, {"ruleId": "1119", "severity": 1, "message": "1239", "line": 104, "column": 6, "nodeType": "1121", "endLine": 104, "endColumn": 8, "suggestions": "1240"}, {"ruleId": "1119", "severity": 1, "message": "1241", "line": 108, "column": 6, "nodeType": "1121", "endLine": 108, "endColumn": 8, "suggestions": "1242"}, {"ruleId": "1107", "severity": 1, "message": "1191", "line": 223, "column": 13, "nodeType": "1109", "messageId": "1110", "endLine": 223, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1243", "line": 20, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1244", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1245", "line": 34, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 34, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 49, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 49, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 49, "column": 18, "nodeType": "1109", "messageId": "1110", "endLine": 49, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1247", "line": 49, "column": 31, "nodeType": "1109", "messageId": "1110", "endLine": 49, "endColumn": 36}, {"ruleId": "1107", "severity": 1, "message": "1248", "line": 53, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 53, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 91, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 91, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1249", "line": 96, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 96, "endColumn": 20}, {"ruleId": "1119", "severity": 1, "message": "1250", "line": 120, "column": 6, "nodeType": "1121", "endLine": 120, "endColumn": 23, "suggestions": "1251"}, {"ruleId": "1107", "severity": 1, "message": "1252", "line": 217, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 217, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1253", "line": 219, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 219, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1254", "line": 224, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 224, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 18, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1255", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1256", "line": 22, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 30}, {"ruleId": "1107", "severity": 1, "message": "1257", "line": 30, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 30, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1258", "line": 44, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 44, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1244", "line": 51, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 51, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1259", "line": 59, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 59, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1260", "line": 61, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 61, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1261", "line": 62, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 62, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 86, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 86, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1262", "line": 98, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 98, "endColumn": 37}, {"ruleId": "1107", "severity": 1, "message": "1249", "line": 102, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 102, "endColumn": 20}, {"ruleId": "1119", "severity": 1, "message": "1263", "line": 108, "column": 6, "nodeType": "1121", "endLine": 108, "endColumn": 8, "suggestions": "1264"}, {"ruleId": "1107", "severity": 1, "message": "1265", "line": 17, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1266", "line": 18, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1267", "line": 19, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1268", "line": 29, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 29, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1269", "line": 40, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 40, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1270", "line": 45, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 45, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1261", "line": 56, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 56, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1271", "line": 57, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 57, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1272", "line": 64, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1273", "line": 70, "column": 7, "nodeType": "1109", "messageId": "1110", "endLine": 70, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1274", "line": 72, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 72, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 87, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 87, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1275", "line": 107, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 107, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1276", "line": 107, "column": 20, "nodeType": "1109", "messageId": "1110", "endLine": 107, "endColumn": 31}, {"ruleId": "1119", "severity": 1, "message": "1277", "line": 121, "column": 6, "nodeType": "1121", "endLine": 121, "endColumn": 8, "suggestions": "1278"}, {"ruleId": "1119", "severity": 1, "message": "1279", "line": 223, "column": 6, "nodeType": "1121", "endLine": 223, "endColumn": 23, "suggestions": "1280"}, {"ruleId": "1194", "severity": 1, "message": "1195", "line": 678, "column": 31, "nodeType": "1196", "endLine": 682, "endColumn": 33}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 20, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1281", "line": 67, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 67, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1282", "line": 97, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 97, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1283", "line": 97, "column": 28, "nodeType": "1109", "messageId": "1110", "endLine": 97, "endColumn": 47}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 102, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 102, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1213", "line": 106, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 106, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1262", "line": 119, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 119, "endColumn": 37}, {"ruleId": "1119", "severity": 1, "message": "1155", "line": 133, "column": 6, "nodeType": "1121", "endLine": 133, "endColumn": 8, "suggestions": "1284"}, {"ruleId": "1119", "severity": 1, "message": "1285", "line": 137, "column": 6, "nodeType": "1121", "endLine": 137, "endColumn": 23, "suggestions": "1286"}, {"ruleId": "1119", "severity": 1, "message": "1287", "line": 146, "column": 6, "nodeType": "1121", "endLine": 146, "endColumn": 8, "suggestions": "1288"}, {"ruleId": "1107", "severity": 1, "message": "1289", "line": 12, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1290", "line": 13, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1291", "line": 19, "column": 6, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 19}, {"ruleId": "1119", "severity": 1, "message": "1292", "line": 57, "column": 6, "nodeType": "1121", "endLine": 57, "endColumn": 20, "suggestions": "1293"}, {"ruleId": "1107", "severity": 1, "message": "1294", "line": 27, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 27, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1237", "line": 28, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 28, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 32, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 32, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1296", "line": 41, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 41, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1297", "line": 53, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 53, "endColumn": 35}, {"ruleId": "1107", "severity": 1, "message": "1298", "line": 57, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 57, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1299", "line": 80, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 80, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1300", "line": 84, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 84, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1301", "line": 90, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 90, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 91, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 91, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1302", "line": 92, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 92, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1303", "line": 106, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 106, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1304", "line": 107, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 107, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1305", "line": 108, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 108, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1306", "line": 109, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 109, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1162", "line": 114, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 114, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 118, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 118, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1164", "line": 123, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 123, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1307", "line": 128, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 128, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1165", "line": 139, "column": 25, "nodeType": "1109", "messageId": "1110", "endLine": 139, "endColumn": 41}, {"ruleId": "1107", "severity": 1, "message": "1249", "line": 141, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 141, "endColumn": 20}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 179, "column": 56, "nodeType": "1168", "messageId": "1169", "endLine": 179, "endColumn": 58}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 184, "column": 57, "nodeType": "1168", "messageId": "1169", "endLine": 184, "endColumn": 59}, {"ruleId": "1119", "severity": 1, "message": "1308", "line": 192, "column": 6, "nodeType": "1121", "endLine": 192, "endColumn": 8, "suggestions": "1309"}, {"ruleId": "1107", "severity": 1, "message": "1310", "line": 207, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 207, "endColumn": 20}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 217, "column": 36, "nodeType": "1168", "messageId": "1169", "endLine": 217, "endColumn": 38}, {"ruleId": "1107", "severity": 1, "message": "1311", "line": 278, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 278, "endColumn": 20}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 390, "column": 60, "nodeType": "1168", "messageId": "1169", "endLine": 390, "endColumn": 62}, {"ruleId": "1107", "severity": 1, "message": "1312", "line": 499, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 499, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1313", "line": 2, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1314", "line": 3, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1315", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1316", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1317", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1318", "line": 10, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 39}, {"ruleId": "1107", "severity": 1, "message": "1319", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 36}, {"ruleId": "1107", "severity": 1, "message": "1320", "line": 7, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1321", "line": 8, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1281", "line": 11, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1210", "line": 14, "column": 19, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1297", "line": 28, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 28, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1159", "line": 32, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 32, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1322", "line": 36, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 36, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1323", "line": 37, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 37, "endColumn": 8}, {"ruleId": "1107", "severity": 1, "message": "1324", "line": 51, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 51, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1325", "line": 57, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 57, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1165", "line": 78, "column": 25, "nodeType": "1109", "messageId": "1110", "endLine": 78, "endColumn": 41}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 83, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 83, "endColumn": 34}, {"ruleId": "1119", "severity": 1, "message": "1326", "line": 118, "column": 6, "nodeType": "1121", "endLine": 118, "endColumn": 8, "suggestions": "1327"}, {"ruleId": "1107", "severity": 1, "message": "1328", "line": 179, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 179, "endColumn": 18}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 368, "column": 65, "nodeType": "1168", "messageId": "1169", "endLine": 368, "endColumn": 67}, {"ruleId": "1119", "severity": 1, "message": "1329", "line": 28, "column": 6, "nodeType": "1121", "endLine": 28, "endColumn": 16, "suggestions": "1330"}, {"ruleId": "1119", "severity": 1, "message": "1329", "line": 28, "column": 6, "nodeType": "1121", "endLine": 28, "endColumn": 16, "suggestions": "1331"}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 2, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 19}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 29, "column": 45, "nodeType": "1168", "messageId": "1169", "endLine": 29, "endColumn": 47}, {"ruleId": "1333", "severity": 1, "message": "1334", "line": 7, "column": 24, "nodeType": "1335", "messageId": "1336", "endLine": 7, "endColumn": 25, "suggestions": "1337"}, {"ruleId": "1333", "severity": 1, "message": "1334", "line": 7, "column": 40, "nodeType": "1335", "messageId": "1336", "endLine": 7, "endColumn": 41, "suggestions": "1338"}, {"ruleId": "1107", "severity": 1, "message": "1339", "line": 6, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1340", "line": 9, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 10, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1341", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 14, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1342", "line": 15, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 15, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1343", "line": 16, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1344", "line": 22, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1345", "line": 28, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 28, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1346", "line": 31, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 31, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1347", "line": 32, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 32, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1348", "line": 33, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 33, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1349", "line": 41, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 41, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1350", "line": 90, "column": 7, "nodeType": "1109", "messageId": "1110", "endLine": 90, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1351", "line": 154, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 154, "endColumn": 14}, {"ruleId": "1119", "severity": 1, "message": "1352", "line": 167, "column": 6, "nodeType": "1121", "endLine": 167, "endColumn": 8, "suggestions": "1353"}, {"ruleId": "1107", "severity": 1, "message": "1354", "line": 169, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 169, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1355", "line": 182, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 182, "endColumn": 41}, {"ruleId": "1333", "severity": 1, "message": "1334", "line": 9, "column": 24, "nodeType": "1335", "messageId": "1336", "endLine": 9, "endColumn": 25, "suggestions": "1356"}, {"ruleId": "1333", "severity": 1, "message": "1334", "line": 9, "column": 40, "nodeType": "1335", "messageId": "1336", "endLine": 9, "endColumn": 41, "suggestions": "1357"}, {"ruleId": "1107", "severity": 1, "message": "1358", "line": 1, "column": 17, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1359", "line": 1, "column": 28, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 35}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 4, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1320", "line": 4, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1321", "line": 4, "column": 16, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1360", "line": 8, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1361", "line": 2, "column": 16, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1362", "line": 2, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1361", "line": 2, "column": 15, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1320", "line": 3, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1321", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1363", "line": 11, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 32, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 32, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1191", "line": 42, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 42, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 1, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 2, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1210", "line": 2, "column": 29, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1247", "line": 2, "column": 36, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 41}, {"ruleId": "1107", "severity": 1, "message": "1364", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 15}, {"ruleId": "1119", "severity": 1, "message": "1365", "line": 63, "column": 6, "nodeType": "1121", "endLine": 63, "endColumn": 8, "suggestions": "1366"}, {"ruleId": "1107", "severity": 1, "message": "1235", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 8}, {"ruleId": "1107", "severity": 1, "message": "1367", "line": 8, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1221", "line": 25, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 25, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1368", "line": 26, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 26, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 27, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 27, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1339", "line": 28, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 28, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1369", "line": 48, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 48, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1370", "line": 50, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 50, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1320", "line": 52, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 52, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1321", "line": 53, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 53, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1281", "line": 54, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 54, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 54, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 54, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1371", "line": 55, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 55, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1372", "line": 55, "column": 24, "nodeType": "1109", "messageId": "1110", "endLine": 55, "endColumn": 39}, {"ruleId": "1107", "severity": 1, "message": "1373", "line": 56, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 56, "endColumn": 31}, {"ruleId": "1107", "severity": 1, "message": "1302", "line": 57, "column": 26, "nodeType": "1109", "messageId": "1110", "endLine": 57, "endColumn": 40}, {"ruleId": "1107", "severity": 1, "message": "1374", "line": 107, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 107, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1375", "line": 108, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 108, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1376", "line": 109, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 109, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1377", "line": 111, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 111, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1378", "line": 112, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 112, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1379", "line": 117, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 117, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1380", "line": 125, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 125, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1381", "line": 131, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 131, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1382", "line": 133, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 133, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1383", "line": 142, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 142, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1165", "line": 177, "column": 25, "nodeType": "1109", "messageId": "1110", "endLine": 177, "endColumn": 41}, {"ruleId": "1119", "severity": 1, "message": "1384", "line": 222, "column": 6, "nodeType": "1121", "endLine": 222, "endColumn": 8, "suggestions": "1385"}, {"ruleId": "1119", "severity": 1, "message": "1386", "line": 244, "column": 6, "nodeType": "1121", "endLine": 244, "endColumn": 68, "suggestions": "1387"}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 426, "column": 64, "nodeType": "1168", "messageId": "1169", "endLine": 426, "endColumn": 66}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 432, "column": 66, "nodeType": "1168", "messageId": "1169", "endLine": 432, "endColumn": 68}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 450, "column": 50, "nodeType": "1168", "messageId": "1169", "endLine": 450, "endColumn": 52}, {"ruleId": "1166", "severity": 1, "message": "1167", "line": 743, "column": 61, "nodeType": "1168", "messageId": "1169", "endLine": 743, "endColumn": 63}, {"ruleId": "1107", "severity": 1, "message": "1108", "line": 1, "column": 46, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 56}, {"ruleId": "1107", "severity": 1, "message": "1367", "line": 7, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1388", "line": 19, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 24}, {"ruleId": "1119", "severity": 1, "message": "1389", "line": 58, "column": 6, "nodeType": "1121", "endLine": 58, "endColumn": 39, "suggestions": "1390"}, {"ruleId": "1119", "severity": 1, "message": "1391", "line": 137, "column": 6, "nodeType": "1121", "endLine": 137, "endColumn": 36, "suggestions": "1392"}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1393", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1394", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1395", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1340", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1230", "line": 7, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 6}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 8, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1368", "line": 9, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1339", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1265", "line": 11, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1266", "line": 12, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 11}, {"ruleId": "1107", "severity": 1, "message": "1367", "line": 13, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 14, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1396", "line": 17, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1370", "line": 19, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1397", "line": 20, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1398", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1399", "line": 22, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1400", "line": 23, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 23, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1401", "line": 23, "column": 18, "nodeType": "1109", "messageId": "1110", "endLine": 23, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1146", "line": 24, "column": 13, "nodeType": "1109", "messageId": "1110", "endLine": 24, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1402", "line": 33, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 33, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1403", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1113", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1323", "line": 14, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 8}, {"ruleId": "1107", "severity": 1, "message": "1404", "line": 21, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1405", "line": 22, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 30}, {"ruleId": "1107", "severity": 1, "message": "1406", "line": 22, "column": 32, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 42}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 15, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 15, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 15, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 15, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1358", "line": 1, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 36}, {"ruleId": "1107", "severity": 1, "message": "1407", "line": 13, "column": 13, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1408", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1408", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1409", "line": 11, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1410", "line": 1, "column": 17, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1411", "line": 10, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1412", "line": 8, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 29, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 29, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1413", "line": 52, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 52, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1414", "line": 57, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 57, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1415", "line": 58, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 58, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1416", "line": 59, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 59, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1417", "line": 73, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 73, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1418", "line": 73, "column": 18, "nodeType": "1109", "messageId": "1110", "endLine": 73, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 76, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 76, "endColumn": 34}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 143, "column": 53, "nodeType": "1168", "messageId": "1169", "endLine": 143, "endColumn": 55}, {"ruleId": "1119", "severity": 1, "message": "1419", "line": 152, "column": 6, "nodeType": "1121", "endLine": 152, "endColumn": 8, "suggestions": "1420"}, {"ruleId": "1119", "severity": 1, "message": "1241", "line": 373, "column": 6, "nodeType": "1121", "endLine": 373, "endColumn": 8, "suggestions": "1421"}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1422", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1423", "line": 29, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 29, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 1, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1358", "line": 2, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1410", "line": 2, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1321", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 8, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1424", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 8}, {"ruleId": "1107", "severity": 1, "message": "1265", "line": 9, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1266", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 11}, {"ruleId": "1107", "severity": 1, "message": "1367", "line": 11, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 12, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1425", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1426", "line": 12, "column": 18, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 35}, {"ruleId": "1107", "severity": 1, "message": "1343", "line": 13, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1427", "line": 14, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1150", "line": 16, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1148", "line": 17, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1428", "line": 20, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1429", "line": 21, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1430", "line": 45, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 45, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1165", "line": 59, "column": 25, "nodeType": "1109", "messageId": "1110", "endLine": 59, "endColumn": 41}, {"ruleId": "1119", "severity": 1, "message": "1431", "line": 81, "column": 6, "nodeType": "1121", "endLine": 81, "endColumn": 8, "suggestions": "1432"}, {"ruleId": "1107", "severity": 1, "message": "1265", "line": 9, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 7}, {"ruleId": "1107", "severity": 1, "message": "1266", "line": 10, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 11}, {"ruleId": "1107", "severity": 1, "message": "1367", "line": 11, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 15}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 12, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 10}, {"ruleId": "1433", "severity": 1, "message": "1434", "line": 37, "column": 61, "nodeType": "1435", "messageId": "1436", "endLine": 37, "endColumn": 63}, {"ruleId": "1107", "severity": 1, "message": "1340", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 13, "column": 16, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1437", "line": 13, "column": 24, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 40}, {"ruleId": "1107", "severity": 1, "message": "1424", "line": 13, "column": 42, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 47}, {"ruleId": "1107", "severity": 1, "message": "1261", "line": 14, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 14, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1438", "line": 15, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 15, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1439", "line": 17, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 17, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1440", "line": 18, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1441", "line": 19, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1442", "line": 20, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1443", "line": 21, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 25}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 8, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1444", "line": 10, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 26}, {"ruleId": "1107", "severity": 1, "message": "1445", "line": 22, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 22}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 1, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1358", "line": 2, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1410", "line": 2, "column": 21, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1446", "line": 2, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1358", "line": 4, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 38, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 38, "endColumn": 34}, {"ruleId": "1107", "severity": 1, "message": "1191", "line": 49, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 49, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1425", "line": 7, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1343", "line": 8, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1150", "line": 9, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1148", "line": 10, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1447", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 29}, {"ruleId": "1107", "severity": 1, "message": "1296", "line": 13, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 13, "endColumn": 23}, {"ruleId": "1107", "severity": 1, "message": "1448", "line": 19, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1449", "line": 20, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 20, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1450", "line": 21, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 21}, {"ruleId": "1107", "severity": 1, "message": "1451", "line": 21, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 32}, {"ruleId": "1107", "severity": 1, "message": "1158", "line": 21, "column": 41, "nodeType": "1109", "messageId": "1110", "endLine": 21, "endColumn": 48}, {"ruleId": "1107", "severity": 1, "message": "1452", "line": 22, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 27}, {"ruleId": "1119", "severity": 1, "message": "1453", "line": 56, "column": 6, "nodeType": "1121", "endLine": 56, "endColumn": 8, "suggestions": "1454"}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 32, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 32, "endColumn": 34}, {"ruleId": "1119", "severity": 1, "message": "1455", "line": 57, "column": 6, "nodeType": "1121", "endLine": 57, "endColumn": 8, "suggestions": "1456"}, {"ruleId": "1107", "severity": 1, "message": "1457", "line": 64, "column": 15, "nodeType": "1109", "messageId": "1110", "endLine": 64, "endColumn": 57}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 85, "column": 38, "nodeType": "1168", "messageId": "1169", "endLine": 85, "endColumn": 40}, {"ruleId": "1166", "severity": 1, "message": "1193", "line": 94, "column": 67, "nodeType": "1168", "messageId": "1169", "endLine": 94, "endColumn": 69}, {"ruleId": "1107", "severity": 1, "message": "1458", "line": 113, "column": 13, "nodeType": "1109", "messageId": "1110", "endLine": 113, "endColumn": 72}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 1, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 14}, {"ruleId": "1119", "severity": 1, "message": "1459", "line": 15, "column": 6, "nodeType": "1121", "endLine": 15, "endColumn": 8, "suggestions": "1460"}, {"ruleId": "1461", "severity": 1, "message": "1462", "line": 22, "column": 9, "nodeType": "1463", "messageId": "1464", "endLine": 38, "endColumn": 10}, {"ruleId": "1107", "severity": 1, "message": "1332", "line": 1, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1465", "line": 8, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1399", "line": 10, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 10, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1394", "line": 27, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 27, "endColumn": 27}, {"ruleId": "1107", "severity": 1, "message": "1423", "line": 30, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 30, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1152", "line": 36, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 36, "endColumn": 34}, {"ruleId": "1194", "severity": 1, "message": "1195", "line": 126, "column": 11, "nodeType": "1196", "endLine": 137, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1466", "line": 33, "column": 5, "nodeType": "1109", "messageId": "1110", "endLine": 33, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1467", "line": 69, "column": 11, "nodeType": "1109", "messageId": "1110", "endLine": 69, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1468", "line": 2, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 24}, {"ruleId": "1107", "severity": 1, "message": "1230", "line": 9, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 9, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1469", "line": 26, "column": 30, "nodeType": "1109", "messageId": "1110", "endLine": 26, "endColumn": 44}, {"ruleId": "1107", "severity": 1, "message": "1220", "line": 3, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1346", "line": 4, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 28}, {"ruleId": "1107", "severity": 1, "message": "1112", "line": 5, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1342", "line": 7, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 7, "endColumn": 12}, {"ruleId": "1107", "severity": 1, "message": "1343", "line": 8, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 8, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1470", "line": 11, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1471", "line": 16, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 16, "endColumn": 19}, {"ruleId": "1107", "severity": 1, "message": "1472", "line": 18, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 18, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1311", "line": 19, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 19, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1378", "line": 22, "column": 9, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 20}, {"ruleId": "1107", "severity": 1, "message": "1270", "line": 11, "column": 23, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 34}, {"ruleId": "1119", "severity": 1, "message": "1473", "line": 43, "column": 6, "nodeType": "1121", "endLine": 43, "endColumn": 8, "suggestions": "1474"}, {"ruleId": "1119", "severity": 1, "message": "1475", "line": 37, "column": 6, "nodeType": "1121", "endLine": 37, "endColumn": 8, "suggestions": "1476"}, {"ruleId": "1107", "severity": 1, "message": "1477", "line": 22, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 18}, {"ruleId": "1107", "severity": 1, "message": "1478", "line": 22, "column": 20, "nodeType": "1109", "messageId": "1110", "endLine": 22, "endColumn": 31}, {"ruleId": "1119", "severity": 1, "message": "1479", "line": 41, "column": 6, "nodeType": "1121", "endLine": 41, "endColumn": 26, "suggestions": "1480"}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 1, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 1, "endColumn": 14}, {"ruleId": "1107", "severity": 1, "message": "1246", "line": 3, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 3, "endColumn": 31}, {"ruleId": "1119", "severity": 1, "message": "1479", "line": 136, "column": 6, "nodeType": "1121", "endLine": 136, "endColumn": 26, "suggestions": "1481"}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 4, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1483", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1484", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 2, "column": 27, "nodeType": "1109", "messageId": "1110", "endLine": 2, "endColumn": 33}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 4, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 4, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1484", "line": 5, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1483", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1484", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1483", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1484", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 16}, {"ruleId": "1485", "severity": 1, "message": "1486", "line": 50, "column": 15, "nodeType": "1196", "endLine": 52, "endColumn": 17}, {"ruleId": "1107", "severity": 1, "message": "1295", "line": 5, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 5, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1483", "line": 6, "column": 3, "nodeType": "1109", "messageId": "1110", "endLine": 6, "endColumn": 9}, {"ruleId": "1107", "severity": 1, "message": "1482", "line": 11, "column": 10, "nodeType": "1109", "messageId": "1110", "endLine": 11, "endColumn": 13}, {"ruleId": "1107", "severity": 1, "message": "1484", "line": 12, "column": 8, "nodeType": "1109", "messageId": "1110", "endLine": 12, "endColumn": 16}, {"ruleId": "1107", "severity": 1, "message": "1487", "line": 35, "column": 7, "nodeType": "1109", "messageId": "1110", "endLine": 35, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'logo' is defined but never used.", "'Button' is defined but never used.", "'TextField' is defined but never used.", "'ThreeCircles' is defined but never used.", "'PreferencesContext' is defined but never used.", "'theme' is defined but never used.", "'appToastConfig' is assigned a value but never used.", "'setAppToastConfig' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has an unnecessary dependency: 'loading'. Either exclude it or remove the dependency array.", "ArrayExpression", ["1488"], "React Hook useMemo has a missing dependency: 'setLoading'. Either include it or remove the dependency array.", ["1489"], "'setActiveMenuItem' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'activeMenuItem'. Either exclude it or remove the dependency array.", ["1490"], "React Hook useEffect has missing dependencies: 'location.pathname', 'loginMessage', and 'navigate'. Either include them or remove the dependency array.", ["1491"], "React Hook useEffect has a missing dependency: 'dispatchSessionExpired'. Either include it or remove the dependency array.", ["1492"], "React Hook useCallback has an unnecessary dependency: 'open'. Either exclude it or remove the dependency array.", ["1493"], ["1494"], "React Hook useCallback has an unnecessary dependency: 'toastConfig'. Either exclude it or remove the dependency array.", ["1495"], "React Hook useMemo has missing dependencies: 'setOpen', 'setToastConfig', and 'setToastMessage'. Either include them or remove the dependency array.", ["1496"], "'PaletteOptions' is defined but never used.", "'lookupReducer' is defined but never used.", "'LOGIN' is defined but never used.", "'createRef' is defined but never used.", "'axios' is defined but never used.", "'AnyAction' is defined but never used.", "'_applicationHelperService' is assigned a value but never used.", "'yup' is defined but never used.", "'Link' is defined but never used.", "'FormControl' is defined but never used.", "'FilledInput' is defined but never used.", "'InputLabel' is defined but never used.", "'MessageConstants' is defined but never used.", "'setOpen' is assigned a value but never used.", "'handleMouseDownPassword' is assigned a value but never used.", "'handleMouseUpPassword' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'title'. Either include it or remove the dependency array.", ["1497"], "'RevenueChartDashboard' is defined but never used.", "'Divider' is defined but never used.", "'Drawer' is defined but never used.", "'openqanda' is assigned a value but never used.", "'setOpenqanda' is assigned a value but never used.", "'_locationService' is assigned a value but never used.", "'businessGroupsOnBusiness' is assigned a value but never used.", "'paginationModel' is assigned a value but never used.", "'setInitialValues' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1498"], "'HomeChartCard' is defined but never used.", "'originalLocationData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocations', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1499"], ["1500"], "'PageProps' is defined but never used.", "'ILoginModel' is defined but never used.", "'authInitiate' is defined but never used.", "'CalendarToday' is defined but never used.", "'ScheduleLater' is defined but never used.", "'GenericDrawer' is defined but never used.", "'date' is assigned a value but never used.", "'setDate' is assigned a value but never used.", "'scheduleForLater' is assigned a value but never used.", "'setScheduleForLater' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'location.state' and 'title'. Either include them or remove the dependency array.", ["1501"], "'checkFormValidity' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'response' is assigned a value but never used.", "'formatDayJsToISO' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'element' is assigned a value but never used.", "'RegisteredEmployeesChart' is defined but never used.", "'ActiveJobsChart' is defined but never used.", "'PieChart' is defined but never used.", "'GroupIcon' is defined but never used.", "'WorkHistoryIcon' is defined but never used.", "'EventAvailableIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'EditLocationAltIcon' is defined but never used.", "'HelpIcon' is defined but never used.", "'ArrowUpwardRoundedIcon' is defined but never used.", "'ArrowDownwardRoundedIcon' is defined but never used.", "'FormHelperText' is defined but never used.", "'Grid2' is defined but never used.", "'BusinessInteractionsChart' is defined but never used.", "'SearchQueriesList' is defined but never used.", "'rbAccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["1502"], "React Hook useEffect has a missing dependency: 'locationList'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedAccountId' needs the current value of 'locationList'.", ["1503"], "React Hook useEffect has missing dependencies: 'fetchAnalyticsData', 'locationList', and 'selectedAccountId'. Either include them or remove the dependency array.", ["1504"], "'IconButton' is defined but never used.", "'Dialog' is defined but never used.", "'AddIcon' is defined but never used.", "'ServicesDisplay' is defined but never used.", "'Accessibility' is defined but never used.", "'FlagIcon' is defined but never used.", "'ChatIcon' is defined but never used.", "'LanguageIcon' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'useCallback' is defined but never used.", "'Box' is defined but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedConfigurations'. Either include it or remove the dependency array.", ["1505"], "'Modal' is defined but never used.", "'DeleteIcon' is defined but never used.", "'SendIcon' is defined but never used.", "'DeleteOutlineIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo.roleId'. Either include them or remove the dependency array.", ["1506"], "React Hook useEffect has a missing dependency: 'getRolesList'. Either include it or remove the dependency array.", ["1507"], "'SearchOffIcon' is defined but never used.", "'TableRowsRoundedIcon' is defined but never used.", "'IUsersListResponse' is defined but never used.", "'Grid' is defined but never used.", "'Stack' is defined but never used.", "'TablePagination' is defined but never used.", "'searchText' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsersPaginated'. Either include it or remove the dependency array.", ["1508"], "'rowsPerPage' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'BlockOutlinedIcon' is defined but never used.", "'CheckCircleRoundedIcon' is defined but never used.", "'IBusinessListResponseModel' is defined but never used.", "'FormGroup' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'PauseCircleFilledIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'setAlertConfig' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchBusinessPaginated', 'title', and 'userInfo'. Either include them or remove the dependency array.", ["1509"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'Switch' is defined but never used.", "'businessPreview' is defined but never used.", "'LockOpenIcon' is defined but never used.", "'useSelector' is defined but never used.", "'CancelOutlinedIcon' is defined but never used.", "'CampaignRoundedIcon' is defined but never used.", "'label' is assigned a value but never used.", "'StatusCardProps' is defined but never used.", "'progress' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId', 'businessId', 'getLocationSummary', and 'locationId'. Either include them or remove the dependency array.", ["1510"], "React Hook useEffect has a missing dependency: 'performMissingInformationOperation'. Either include it or remove the dependency array.", ["1511"], "'CardMedia' is defined but never used.", "'showConfirmPopup' is assigned a value but never used.", "'setShowConfirmPopup' is assigned a value but never used.", ["1512"], "React Hook useEffect has a missing dependency: 'fetchLocationsPaginated'. Either include it or remove the dependency array.", ["1513"], "React Hook useEffect has missing dependencies: 'getBusiness' and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1514"], "'FallingLines' is defined but never used.", "'RotatingLines' is defined but never used.", "'IDeleteRecord' is defined but never used.", "React Hook useEffect has a missing dependency: 'gmbCallBack'. Either include it or remove the dependency array.", ["1515"], "'StarBorderIcon' is defined but never used.", "'Avatar' is defined but never used.", "'StarRoundedIcon' is defined but never used.", "'ILocationListRequestModel' is defined but never used.", "'STARRATINGMAP' is defined but never used.", "'UserAvatar' is defined but never used.", "'Chip' is defined but never used.", "'SearchOutlinedIcon' is defined but never used.", "'InputAdornment' is defined but never used.", "'newestIcon' is assigned a value but never used.", "'oldestIcon' is assigned a value but never used.", "'highRatingIcon' is assigned a value but never used.", "'lowRatingIcon' is assigned a value but never used.", "'showScroll' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchLocationsPaginated', 'getAllTags', 'getBusiness', and 'getBusinessGroups'. Either include them or remove the dependency array.", ["1516"], "'scrollToTop' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'AUTH_REQUESTED' is defined but never used.", "'AUTH_SUCCESS' is defined but never used.", "'AUTH_LOGOUT' is defined but never used.", "'AUTH_ERROR' is defined but never used.", "'AUTH_UNAUTHORIZED' is defined but never used.", "'IRoleBasedAccessResponseModel' is defined but never used.", "'ILoggedInUserResponseModel' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Pagination' is defined but never used.", "'Paper' is defined but never used.", "'OutlinedInput' is defined but never used.", "'getIn' is defined but never used.", "React Hook useEffect has missing dependencies: 'getBusiness', 'getBusinessGroups', and 'getLocationsList'. Either include them or remove the dependency array.", ["1517"], "'MenuProps' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'logoutUser'. Either include it or remove the dependency array.", ["1518"], ["1519"], "'Component' is defined but never used.", "no-useless-escape", "Unnecessary escape character: \\..", "Literal", "unnecessaryEscape", ["1520", "1521"], ["1522", "1523"], "'Toolbar' is defined but never used.", "'Typography' is defined but never used.", "'MenuIcon' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'ManageAccountsIcon' is defined but never used.", "'Collapse' is defined but never used.", "'SettingsOutlinedIcon' is defined but never used.", "'ArrowForwardIosRoundedIcon' is defined but never used.", "'ListAltSharp' is defined but never used.", "'MapsUgcRoundedIcon' is defined but never used.", "'AppBar' is assigned a value but never used.", "'theme' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isMobile', 'menuOpened', 'openLeftMenu', 'rbAccess', and 'userInfo'. Either include them or remove the dependency array.", ["1524"], "'handleMenuItemClick' is assigned a value but never used.", "'AnalyticsRoutes' is assigned a value but never used.", ["1525", "1526"], ["1527", "1528"], "'useEffect' is defined but never used.", "'useMemo' is defined but never used.", "'MONTHS' is assigned a value but never used.", "'PolarArea' is defined but never used.", "'Bar' is defined but never used.", "'ReviewService' is defined but never used.", "'title' is defined but never used.", "React Hook useEffect has missing dependencies: 'onDateChange' and 'selectedDuration'. Either include them or remove the dependency array. If 'onDateChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1529"], "'ListItemText' is defined but never used.", "'AppBar' is defined but never used.", "'RoleType' is defined but never used.", "'CloseIcon' is defined but never used.", "'ArrowBackIos' is defined but never used.", "'ArrowForwardIos' is defined but never used.", "'LinearProgressWithLabel' is defined but never used.", "'open' is assigned a value but never used.", "'selectedValue' is assigned a value but never used.", "'selectedOptions' is assigned a value but never used.", "'handleOpen' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'handleDropdownChange' is assigned a value but never used.", "'handleMultiSelectChange' is assigned a value but never used.", "'currentIndex' is assigned a value but never used.", "'handleNext' is assigned a value but never used.", "'handlePrev' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getBusiness' and 'getLocationsList'. Either include them or remove the dependency array.", ["1530"], "React Hook useEffect has a missing dependency: 'initialValues'. Either include it or remove the dependency array.", ["1531"], "'LoadingContext' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSearchKeywords'. Either include it or remove the dependency array.", ["1532"], "React Hook useEffect has a missing dependency: 'fetchMoreData'. Either include it or remove the dependency array.", ["1533"], "'SAVE_SCHEDULED' is defined but never used.", "'IGoogleCreatePost' is defined but never used.", "'DialogContent' is defined but never used.", "'ListItemButton' is defined but never used.", "'ArrowBackIcon' is defined but never used.", "'ChevronRightIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Formik' is defined but never used.", "'Form' is defined but never used.", "'Category' is defined but never used.", "'DialogTitle' is defined but never used.", "'AdapterDayjs' is defined but never used.", "'LocalizationProvider' is defined but never used.", "'DatePicker' is defined but never used.", "'ZoomInIcon' is defined but never used.", "'LIST_OF_LOCATIONS' is defined but never used.", "'LIST_OF_ROLE' is defined but never used.", "'useState' is defined but never used.", "'useNavigate' is defined but never used.", "'IUserResponseModel' is defined but never used.", "'IUser' is defined but never used.", "'IAlertDialogConfig' is defined but never used.", "'logOut' is defined but never used.", "'AlertDialog' is defined but never used.", "'isEdit' is assigned a value but never used.", "'setIsEdit' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_userService', 'getBusiness', 'getBusinessGroups', 'getLocationsList', and 'props.editData'. Either include them or remove the dependency array.", ["1534"], ["1535"], "'LIST_OF_BUSINESS' is defined but never used.", "'navigate' is assigned a value but never used.", "'Badge' is defined but never used.", "'Select' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'IRole' is defined but never used.", "'IBusinessGroup' is defined but never used.", "'ILocation' is defined but never used.", "'usersList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1536"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "'CircularProgress' is defined but never used.", "'MediaGallery' is defined but never used.", "'MISSING_INFORMATION' is defined but never used.", "'IconOnAvailability' is defined but never used.", "'LocationOnRoundedIcon' is defined but never used.", "'MovieIcon' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "'NearMeOutlinedIcon' is defined but never used.", "'handleOpenMap' is assigned a value but never used.", "'FunctionComponent' is defined but never used.", "'ThumbUpAltRoundedIcon' is defined but never used.", "'FeedbackTemplate' is defined but never used.", "'FeedbackCard' is defined but never used.", "'CssBaseline' is defined but never used.", "'Container' is defined but never used.", "'ImageBackgroundCard' is defined but never used.", "React Hook useEffect has missing dependencies: 'props.review.review', 'props.review.reviewerName', 'props.review.reviewerProfilePic', and 'props.review.starRating'. Either include them or remove the dependency array. If 'setPostTemplateConfig' needs the current value of 'props.review.review', you can also switch to useReducer instead of useState and read 'props.review.review' in the reducer.", ["1537"], "React Hook useEffect has a missing dependency: 'getAllTags'. Either include it or remove the dependency array.", ["1538"], "'createTag' is assigned a value but never used.", "'updateTagsToReviewResponse' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'size'. Either include it or remove the dependency array.", ["1539"], "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "'EditIcon' is defined but never used.", "'navigationType' is assigned a value but never used.", "'userInfo' is assigned a value but never used.", "'isValidElement' is defined but never used.", "'activeMenuItem' is assigned a value but never used.", "'LogoutIcon' is defined but never used.", "'logoutUser' is assigned a value but never used.", "'openSubMenu' is assigned a value but never used.", "React Hook useEffect has missing dependencies: '_locationService', 'props.mediaItems', and 'setLoading'. Either include them or remove the dependency array.", ["1540"], "React Hook useEffect has a missing dependency: 'props.profileImage'. Either include it or remove the dependency array.", ["1541"], "'fontType' is assigned a value but never used.", "'setFontType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useEffect call and refer to those specific props inside useEffect.", ["1542"], ["1543"], "'ref' is defined but never used.", "'Rating' is defined but never used.", "'StarIcon' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'availableLocations' is assigned a value but never used.", {"desc": "1544", "fix": "1545"}, {"desc": "1546", "fix": "1547"}, {"desc": "1544", "fix": "1548"}, {"desc": "1549", "fix": "1550"}, {"desc": "1551", "fix": "1552"}, {"desc": "1544", "fix": "1553"}, {"desc": "1544", "fix": "1554"}, {"desc": "1544", "fix": "1555"}, {"desc": "1556", "fix": "1557"}, {"desc": "1558", "fix": "1559"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, {"kind": "1564", "justification": "1565"}, {"desc": "1566", "fix": "1567"}, {"desc": "1568", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"desc": "1572", "fix": "1573"}, {"desc": "1574", "fix": "1575"}, {"desc": "1576", "fix": "1577"}, {"desc": "1578", "fix": "1579"}, {"desc": "1580", "fix": "1581"}, {"desc": "1582", "fix": "1583"}, {"desc": "1584", "fix": "1585"}, {"desc": "1586", "fix": "1587"}, {"desc": "1558", "fix": "1588"}, {"desc": "1589", "fix": "1590"}, {"desc": "1591", "fix": "1592"}, {"desc": "1593", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1599", "fix": "1600"}, {"desc": "1599", "fix": "1601"}, {"messageId": "1602", "fix": "1603", "desc": "1604"}, {"messageId": "1605", "fix": "1606", "desc": "1607"}, {"messageId": "1602", "fix": "1608", "desc": "1604"}, {"messageId": "1605", "fix": "1609", "desc": "1607"}, {"desc": "1610", "fix": "1611"}, {"messageId": "1602", "fix": "1612", "desc": "1604"}, {"messageId": "1605", "fix": "1613", "desc": "1607"}, {"messageId": "1602", "fix": "1614", "desc": "1604"}, {"messageId": "1605", "fix": "1615", "desc": "1607"}, {"desc": "1616", "fix": "1617"}, {"desc": "1618", "fix": "1619"}, {"desc": "1620", "fix": "1621"}, {"desc": "1622", "fix": "1623"}, {"desc": "1624", "fix": "1625"}, {"desc": "1626", "fix": "1627"}, {"desc": "1578", "fix": "1628"}, {"desc": "1629", "fix": "1630"}, {"desc": "1631", "fix": "1632"}, {"desc": "1633", "fix": "1634"}, {"desc": "1635", "fix": "1636"}, {"desc": "1637", "fix": "1638"}, {"desc": "1639", "fix": "1640"}, {"desc": "1641", "fix": "1642"}, {"desc": "1641", "fix": "1643"}, "Update the dependencies array to be: []", {"range": "1644", "text": "1645"}, "Update the dependencies array to be: [loading, setLoading]", {"range": "1646", "text": "1647"}, {"range": "1648", "text": "1645"}, "Update the dependencies array to be: [userInfo, success, location.pathname, navigate, loginMessage]", {"range": "1649", "text": "1650"}, "Update the dependencies array to be: [dispatchSessionExpired, isUnAuthorised]", {"range": "1651", "text": "1652"}, {"range": "1653", "text": "1645"}, {"range": "1654", "text": "1645"}, {"range": "1655", "text": "1645"}, "Update the dependencies array to be: [open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", {"range": "1656", "text": "1657"}, "Update the dependencies array to be: [title]", {"range": "1658", "text": "1659"}, "Update the dependencies array to be: [fetchLocationsPaginated, getBusiness, getBusinessGroups]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [fetchLocations, getBusiness, getBusinessGroups]", {"range": "1662", "text": "1663"}, "directive", "", "Update the dependencies array to be: [location.state, title]", {"range": "1664", "text": "1665"}, "Update the dependencies array to be: [fetchLocations]", {"range": "1666", "text": "1667"}, "Update the dependencies array to be: [locationList, selectedLocationId]", {"range": "1668", "text": "1669"}, "Update the dependencies array to be: [selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", {"range": "1670", "text": "1671"}, "Update the dependencies array to be: [loadSavedConfigurations, title, user]", {"range": "1672", "text": "1673"}, "Update the dependencies array to be: [navigate, userInfo.roleId]", {"range": "1674", "text": "1675"}, "Update the dependencies array to be: [getRolesList]", {"range": "1676", "text": "1677"}, "Update the dependencies array to be: [fetchUsersPaginated, paginationModel]", {"range": "1678", "text": "1679"}, "Update the dependencies array to be: [fetchBusinessPaginated, title, userInfo]", {"range": "1680", "text": "1681"}, "Update the dependencies array to be: [accountId, businessId, getLocationSummary, locationId]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [locationSummary, performMissingInformationOperation]", {"range": "1684", "text": "1685"}, {"range": "1686", "text": "1659"}, "Update the dependencies array to be: [fetchLocationsPaginated, paginationModel]", {"range": "1687", "text": "1688"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups]", {"range": "1689", "text": "1690"}, "Update the dependencies array to be: [gmbCallBack, searchParams]", {"range": "1691", "text": "1692"}, "Update the dependencies array to be: [fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", {"range": "1693", "text": "1694"}, "Update the dependencies array to be: [getBusiness, getBusinessGroups, getLocationsList]", {"range": "1695", "text": "1696"}, "Update the dependencies array to be: [logoutUser, navigate]", {"range": "1697", "text": "1698"}, {"range": "1699", "text": "1698"}, "removeEscape", {"range": "1700", "text": "1565"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1701", "text": "1702"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1703", "text": "1565"}, {"range": "1704", "text": "1702"}, "Update the dependencies array to be: [isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", {"range": "1705", "text": "1706"}, {"range": "1707", "text": "1565"}, {"range": "1708", "text": "1702"}, {"range": "1709", "text": "1565"}, {"range": "1710", "text": "1702"}, "Update the dependencies array to be: [onDateChange, selectedDuration]", {"range": "1711", "text": "1712"}, "Update the dependencies array to be: [getBusiness, getLocationsList]", {"range": "1713", "text": "1714"}, "Update the dependencies array to be: [initialValues.locationId, initialValues.accountId, locations, initialValues]", {"range": "1715", "text": "1716"}, "Update the dependencies array to be: [accountId, locationId, from, to, fetchSearchKeywords]", {"range": "1717", "text": "1718"}, "Update the dependencies array to be: [nextPageToken, loading, open, fetchMoreData]", {"range": "1719", "text": "1720"}, "Update the dependencies array to be: [_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", {"range": "1721", "text": "1722"}, {"range": "1723", "text": "1677"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1724", "text": "1725"}, "Update the dependencies array to be: [props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", {"range": "1726", "text": "1727"}, "Update the dependencies array to be: [getAllTags]", {"range": "1728", "text": "1729"}, "Update the dependencies array to be: [size]", {"range": "1730", "text": "1731"}, "Update the dependencies array to be: [_locationService, props.mediaItems, setLoading]", {"range": "1732", "text": "1733"}, "Update the dependencies array to be: [props.profileImage]", {"range": "1734", "text": "1735"}, "Update the dependencies array to be: [postTemplateConfig, props]", {"range": "1736", "text": "1737"}, {"range": "1738", "text": "1737"}, [3556, 3565], "[]", [3669, 3678], "[loading, setLoading]", [3959, 3975], [4634, 4653], "[userInfo, success, location.pathname, navigate, loginMessage]", [4905, 4921], "[dispatchSessionExpired, isUnAuthorised]", [5436, 5442], [5567, 5573], [5756, 5769], [5948, 5976], "[open, setOpen, message, setToastMessage, toastConfig, setToastConfig]", [3057, 3059], "[title]", [5471, 5473], "[fetchLocationsPaginated, getBusiness, getBusinessGroups]", [5424, 5426], "[fetchLocations, getBusiness, getBusinessGroups]", [9218, 9220], "[location.state, title]", [4843, 4845], "[fetchLocations]", [5049, 5069], "[locationList, selectedLocationId]", [5446, 5485], "[selectedLocationId, selectedDateRange, selectedAccountId, fetchAnalyticsData, locationList]", [2046, 2059], "[loadSavedConfigurations, title, user]", [3559, 3561], "[navigate, userInfo.roleId]", [3614, 3616], "[getRolesList]", [5200, 5217], "[fetchUsersPaginated, paginationModel]", [5187, 5189], "[fetchBusinessPaginated, title, userInfo]", [5063, 5065], "[accountId, businessId, getLocationSummary, locationId]", [8294, 8311], "[locationSummary, performMissingInformationOperation]", [5560, 5562], [5626, 5643], "[fetchLocationsPaginated, paginationModel]", [5820, 5822], "[getBusiness, getBusinessGroups]", [1990, 2004], "[gmbCallBack, searchParams]", [7854, 7856], "[fetchLocationsPaginated, getAllTags, getBusiness, getBusinessGroups]", [4079, 4081], "[getBusiness, getBusinessGroups, getLocationsList]", [931, 941], "[logo<PERSON><PERSON><PERSON>, navigate]", [932, 942], [190, 191], [190, 190], "\\", [206, 207], [206, 206], [6396, 6398], "[isMobile, menuOpened, openLeftMenu, rbAccess, userInfo]", [296, 297], [296, 296], [312, 313], [312, 312], [1857, 1859], "[onDate<PERSON><PERSON>e, selectedDuration]", [6900, 6902], "[getBusiness, getLocationsList]", [7650, 7712], "[initialValues.locationId, initialValues.accountId, locations, initialValues]", [1576, 1609], "[accountId, locationId, from, to, fetchSearchKeywords]", [3879, 3909], "[nextPageToken, loading, open, fetchMoreData]", [5857, 5859], "[_userService, getBusiness, getBusinessGroups, getLocationsList, props.editData]", [12623, 12625], [3571, 3573], "[fetchUsers]", [3011, 3013], "[props.review.review, props.review.reviewerName, props.review.reviewerProfilePic, props.review.starRating]", [2469, 2471], "[getAllTags]", [511, 513], "[size]", [1430, 1432], "[_locationService, props.mediaItems, setLoading]", [1159, 1161], "[props.profileImage]", [1250, 1270], "[postTemplateConfig, props]", [6475, 6495]]