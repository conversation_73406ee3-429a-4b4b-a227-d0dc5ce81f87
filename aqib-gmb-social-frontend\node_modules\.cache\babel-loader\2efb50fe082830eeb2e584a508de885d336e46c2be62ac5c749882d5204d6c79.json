{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"breakpoints\", \"palette\", \"spacing\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from './createBreakpoints';\nimport shape from './shape';\nimport createSpacing from './createSpacing';\nimport styleFunctionSx from '../styleFunctionSx/styleFunctionSx';\nimport defaultSxConfig from '../styleFunctionSx/defaultSxConfig';\nimport applyStyles from './applyStyles';\nfunction createTheme(options = {}, ...args) {\n  const {\n      breakpoints: breakpointsInput = {},\n      palette: paletteInput = {},\n      spacing: spacingInput,\n      shape: shapeInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: _extends({\n      mode: 'light'\n    }, paletteInput),\n    spacing,\n    shape: _extends({}, shape, shapeInput)\n  }, other);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "deepmerge", "createBreakpoints", "shape", "createSpacing", "styleFunctionSx", "defaultSxConfig", "applyStyles", "createTheme", "options", "args", "breakpoints", "breakpointsInput", "palette", "paletteInput", "spacing", "spacingInput", "shapeInput", "other", "muiTheme", "direction", "components", "mode", "reduce", "acc", "argument", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/lab/node_modules/@mui/system/esm/createTheme/createTheme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"breakpoints\", \"palette\", \"spacing\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from './createBreakpoints';\nimport shape from './shape';\nimport createSpacing from './createSpacing';\nimport styleFunctionSx from '../styleFunctionSx/styleFunctionSx';\nimport defaultSxConfig from '../styleFunctionSx/defaultSxConfig';\nimport applyStyles from './applyStyles';\nfunction createTheme(options = {}, ...args) {\n  const {\n      breakpoints: breakpointsInput = {},\n      palette: paletteInput = {},\n      spacing: spacingInput,\n      shape: shapeInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: _extends({\n      mode: 'light'\n    }, paletteInput),\n    spacing,\n    shape: _extends({}, shape, shapeInput)\n  }, other);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;AAChE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAGC,IAAI,EAAE;EAC1C,MAAM;MACFC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;MAClCC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;MAC1BC,OAAO,EAAEC,YAAY;MACrBb,KAAK,EAAEc,UAAU,GAAG,CAAC;IACvB,CAAC,GAAGR,OAAO;IACXS,KAAK,GAAGnB,6BAA6B,CAACU,OAAO,EAAET,SAAS,CAAC;EAC3D,MAAMW,WAAW,GAAGT,iBAAiB,CAACU,gBAAgB,CAAC;EACvD,MAAMG,OAAO,GAAGX,aAAa,CAACY,YAAY,CAAC;EAC3C,IAAIG,QAAQ,GAAGlB,SAAS,CAAC;IACvBU,WAAW;IACXS,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,CAAC,CAAC;IACd;IACAR,OAAO,EAAEf,QAAQ,CAAC;MAChBwB,IAAI,EAAE;IACR,CAAC,EAAER,YAAY,CAAC;IAChBC,OAAO;IACPZ,KAAK,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAEc,UAAU;EACvC,CAAC,EAAEC,KAAK,CAAC;EACTC,QAAQ,CAACZ,WAAW,GAAGA,WAAW;EAClCY,QAAQ,GAAGT,IAAI,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKxB,SAAS,CAACuB,GAAG,EAAEC,QAAQ,CAAC,EAAEN,QAAQ,CAAC;EAC7EA,QAAQ,CAACO,iBAAiB,GAAG5B,QAAQ,CAAC,CAAC,CAAC,EAAEQ,eAAe,EAAEY,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACQ,iBAAiB,CAAC;EAC5GP,QAAQ,CAACQ,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAOxB,eAAe,CAAC;MACrBuB,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAOX,QAAQ;AACjB;AACA,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}