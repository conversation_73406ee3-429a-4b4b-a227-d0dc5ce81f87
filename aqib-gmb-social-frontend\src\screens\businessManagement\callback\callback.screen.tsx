import { FunctionComponent, useEffect, useState } from "react";
import PageProps from "../../../models/PageProps.interface";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import "../manageBusiness/manageBusiness.screen.style.css";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import googleLogo from "../../../assets/common/GoogleBusiness.jpg";
import applicationLogo from "../../../assets/common/Logo.png";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  FallingLines,
  RotatingLines,
  ThreeCircles,
} from "react-loader-spinner";
import AuthService from "../../../services/auth/auth.service";
import { useDispatch, useSelector } from "react-redux";

type IDeleteRecord = {
  isShow: boolean;
  data: any;
  businessId: number;
};

const GMBCallback: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _authService = new AuthService(dispatch);
  const [searchParams] = useSearchParams();
  const [authStatus, setAuthStatus] = useState(null);
  const navigate = useNavigate();

  const [countdown, setCountdown] = useState<number>(0); // Initially null (no countdown running)
  const [isRunning, setIsRunning] = useState(false); // Tracks if timer is active

  useEffect(() => {
    let interval: any;
    let timeout: any;

    if (isRunning && countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (isRunning && countdown === 0) {
      clearInterval(interval);
      navigate("/home");
    }

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [countdown, isRunning, navigate]);

  useEffect(() => {
    gmbCallBack(searchParams.get("code"), searchParams.get("state"));
  }, [searchParams]);

  const startCountdown = () => {
    setCountdown(5); // Set countdown to 5 seconds
    setIsRunning(true); // Start countdown
  };

  const gmbCallBack = async (code: any, state: any) => {
    try {
      const result = await _authService.gmbcallback(code, state, userInfo.id);
      if (result.data) {
        startCountdown();
      }
      setAuthStatus(result.data);
      return;
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "white",
        padding: 4,
        borderRadius: 2,
        boxShadow: 3,
        width: "400px",
        margin: "auto",
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <img alt="AQIB Softech" src={applicationLogo} style={{ height: 50 }} />
        <Typography variant="h6">+</Typography>
        <img alt="AQIB Softech" src={googleLogo} style={{ height: 50 }} />
      </Box>
      {authStatus != null && authStatus ? (
        <Box>
          <Box sx={{ display: "flex", alignItems: "center", marginTop: 2 }}>
            <ErrorOutlineIcon color="success" sx={{ fontSize: 40 }} />
            <Typography variant="body1" color="success" sx={{ marginLeft: 1 }}>
              Authentication Success
            </Typography>
          </Box>
          <Typography variant="body1" color="success" sx={{ marginLeft: 1 }}>
            Redirecting you in {countdown} Seconds
          </Typography>
        </Box>
      ) : authStatus === null ? (
        <Box sx={{ display: "flex", alignItems: "center", margin: 10 }}>
          <ThreeCircles
            visible={true}
            height="100"
            width="100"
            color="#467fea"
            ariaLabel="three-circles-loading"
            wrapperStyle={{}}
            wrapperClass=""
          />
        </Box>
      ) : (
        <Box sx={{ display: "flex", alignItems: "center", marginTop: 2 }}>
          <ErrorOutlineIcon color="error" sx={{ fontSize: 40 }} />
          <Typography variant="body1" color="error" sx={{ marginLeft: 1 }}>
            Authentication Failure
          </Typography>
        </Box>
      )}

      <Button
        variant="contained"
        color="secondary"
        sx={{ marginTop: 3 }}
        onClick={() => navigate("/home")}
      >
        Go To Dashboard
      </Button>
    </Box>
  );
};

export default GMBCallback;
