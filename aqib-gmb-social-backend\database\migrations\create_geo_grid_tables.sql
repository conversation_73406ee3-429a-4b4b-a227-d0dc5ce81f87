-- Create geo_grid_configurations table
CREATE TABLE IF NOT EXISTS geo_grid_configurations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    center_lat DECIMAL(10, 8) NOT NULL,
    center_lng DECIMAL(11, 8) NOT NULL,
    grid_size VARCHAR(10) NOT NULL DEFAULT '3x3',
    distance DECIMAL(8, 2) NOT NULL DEFAULT 500,
    distance_unit ENUM('meters', 'miles', 'kilometers') NOT NULL DEFAULT 'meters',
    search_type ENUM('name', 'coordinates', 'mapUrl') NOT NULL DEFAULT 'name',
    search_query TEXT,
    is_schedule_enabled BOOLEAN DEFAULT FALSE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- Create geo_grid_points table
CREATE TABLE IF NOT EXISTS geo_grid_points (
    id INT AUTO_INCREMENT PRIMARY KEY,
    grid_config_id INT NOT NULL,
    point_index INT NOT NULL,
    lat DECIMAL(10, 8) NOT NULL,
    lng DECIMAL(11, 8) NOT NULL,
    address TEXT,
    place_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (grid_config_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE,
    INDEX idx_grid_config_id (grid_config_id),
    INDEX idx_point_index (point_index),
    INDEX idx_coordinates (lat, lng)
);

-- Create geo_grid_schedules table (for future scheduling functionality)
CREATE TABLE IF NOT EXISTS geo_grid_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    grid_config_id INT NOT NULL,
    schedule_name VARCHAR(255) NOT NULL,
    schedule_type ENUM('one_time', 'recurring') NOT NULL DEFAULT 'one_time',
    schedule_time DATETIME NOT NULL,
    recurrence_pattern JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (grid_config_id) REFERENCES geo_grid_configurations(id) ON DELETE CASCADE,
    INDEX idx_grid_config_id (grid_config_id),
    INDEX idx_schedule_time (schedule_time),
    INDEX idx_is_active (is_active)
);
