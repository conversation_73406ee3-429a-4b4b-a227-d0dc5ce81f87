import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  CREATE_BUSINESS,
  DELETE_BUSINESS,
  EDIT_BUSINESS,
  ENABLE_DISABLE_BUSINESS,
  GMB_ACCOUNTS,
  LIST_OF_BUSINESS,
  LIST_OF_BUSINESS_PAGINATED,
  LIST_OF_LOCATIONS,
} from "../../constants/endPoints.constant";
import { IAddBusinessRequestModel } from "../../interfaces/request/IAddBusinessRequestModel";
import { IPaginationModel } from "../../interfaces/IPaginationModel";
import { Action } from "redux";

class BusinessService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  getBusiness = async (userId: number) => {
    return await this._httpHelperService.get(`${LIST_OF_BUSINESS}/${userId}`);
  };

  getBusinessPaginated = async (
    userId: number,
    paginationModel: IPaginationModel
  ) => {
    return await this._httpHelperService.get(
      `${LIST_OF_BUSINESS_PAGINATED}/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}`
    );
  };

  addBusiness = async (request: IAddBusinessRequestModel) => {
    return await this._httpHelperService.post(`${CREATE_BUSINESS}`, request);
  };

  updateBusiness = async (request: IAddBusinessRequestModel, id: number) => {
    return await this._httpHelperService.put(`${EDIT_BUSINESS}/${id}`, request);
  };

  getBusinessGroups = async (userId: number) => {
    return await this._httpHelperService.get(`${GMB_ACCOUNTS}/${userId}`);
  };

  getLocations = async (userId: number) => {
    return await this._httpHelperService.get(`${LIST_OF_LOCATIONS}/${userId}`);
  };

  deleteBusniess = async (businessId: number) => {
    return await this._httpHelperService.delete(
      `${DELETE_BUSINESS}/${businessId}`
    );
  };

  enableDisableBusniess = async (businessId: number, request: any) => {
    return await this._httpHelperService.post(
      `${ENABLE_DISABLE_BUSINESS}/${businessId}`,
      request
    );
  };
}

export default BusinessService;
