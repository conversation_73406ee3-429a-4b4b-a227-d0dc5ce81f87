{"ast": null, "code": "/**\n * Google Maps API utilities\n */\n\nlet isGoogleMapsLoaded = false;\nlet isGoogleMapsLoading = false;\nconst loadPromises = [];\n\n/**\n * Dynamically load Google Maps API\n */\nexport const loadGoogleMapsAPI = () => {\n  return new Promise((resolve, reject) => {\n    // If already loaded, resolve immediately\n    if (isGoogleMapsLoaded && window.google && window.google.maps) {\n      resolve();\n      return;\n    }\n\n    // If currently loading, wait for existing promise\n    if (isGoogleMapsLoading) {\n      const existingPromise = loadPromises[loadPromises.length - 1];\n      if (existingPromise) {\n        existingPromise.then(resolve).catch(reject);\n        return;\n      }\n    }\n    isGoogleMapsLoading = true;\n    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;\n\n    // Check if API key is available\n    if (!apiKey || apiKey === 'YOUR_GOOGLE_MAPS_API_KEY') {\n      console.warn('Google Maps API key not configured. Using fallback location suggestions.');\n      isGoogleMapsLoading = false;\n      resolve(); // Resolve anyway to allow fallback functionality\n      return;\n    }\n\n    // Check if script already exists\n    const existingScript = document.querySelector('script[src*=\"maps.googleapis.com\"]');\n    if (existingScript) {\n      // Wait for existing script to load\n      existingScript.addEventListener('load', () => {\n        isGoogleMapsLoaded = true;\n        isGoogleMapsLoading = false;\n        resolve();\n      });\n      existingScript.addEventListener('error', () => {\n        isGoogleMapsLoading = false;\n        reject(new Error('Failed to load Google Maps API'));\n      });\n      return;\n    }\n\n    // Create and load the script\n    const script = document.createElement('script');\n    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;\n    script.async = true;\n    script.defer = true;\n    script.onload = () => {\n      isGoogleMapsLoaded = true;\n      isGoogleMapsLoading = false;\n      resolve();\n    };\n    script.onerror = () => {\n      isGoogleMapsLoading = false;\n      reject(new Error('Failed to load Google Maps API'));\n    };\n    document.head.appendChild(script);\n  });\n};\n\n/**\n * Check if Google Maps API is available\n */\nexport const isGoogleMapsAPIAvailable = () => {\n  return !!(window.google && window.google.maps && window.google.maps.places);\n};\n\n/**\n * Get place details using Google Places API\n */\nexport const getPlaceDetails = placeId => {\n  return new Promise((resolve, reject) => {\n    if (!isGoogleMapsAPIAvailable()) {\n      reject(new Error('Google Maps API not available'));\n      return;\n    }\n    const service = new window.google.maps.places.PlacesService(document.createElement('div'));\n    service.getDetails({\n      placeId: placeId,\n      fields: ['place_id', 'name', 'geometry', 'formatted_address']\n    }, (place, status) => {\n      if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {\n        var _place$geometry, _place$geometry2;\n        resolve({\n          placeId: place.place_id,\n          name: place.name,\n          address: place.formatted_address,\n          lat: (_place$geometry = place.geometry) === null || _place$geometry === void 0 ? void 0 : _place$geometry.location.lat(),\n          lng: (_place$geometry2 = place.geometry) === null || _place$geometry2 === void 0 ? void 0 : _place$geometry2.location.lng()\n        });\n      } else {\n        reject(new Error(`Place details request failed: ${status}`));\n      }\n    });\n  });\n};\n\n/**\n * Get autocomplete predictions using Google Places API\n */\nexport const getAutocompletePredictions = (input, options = {}) => {\n  return new Promise((resolve, reject) => {\n    if (!isGoogleMapsAPIAvailable()) {\n      reject(new Error('Google Maps API not available'));\n      return;\n    }\n    const service = new window.google.maps.places.AutocompleteService();\n    service.getPlacePredictions({\n      input,\n      types: options.types || ['(cities)'],\n      componentRestrictions: options.componentRestrictions || {\n        country: 'us'\n      }\n    }, (predictions, status) => {\n      if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {\n        const suggestions = predictions.map(prediction => ({\n          id: prediction.place_id,\n          name: prediction.description,\n          placeId: prediction.place_id,\n          types: prediction.types\n        }));\n        resolve(suggestions);\n      } else if (status === window.google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {\n        resolve([]);\n      } else {\n        reject(new Error(`Autocomplete request failed: ${status}`));\n      }\n    });\n  });\n};", "map": {"version": 3, "names": ["isGoogleMapsLoaded", "isGoogleMapsLoading", "loadPromises", "loadGoogleMapsAPI", "Promise", "resolve", "reject", "window", "google", "maps", "existingPromise", "length", "then", "catch", "<PERSON><PERSON><PERSON><PERSON>", "import", "meta", "env", "VITE_GOOGLE_MAPS_API_KEY", "console", "warn", "existingScript", "document", "querySelector", "addEventListener", "Error", "script", "createElement", "src", "async", "defer", "onload", "onerror", "head", "append<PERSON><PERSON><PERSON>", "isGoogleMapsAPIAvailable", "places", "getPlaceDetails", "placeId", "service", "PlacesService", "getDetails", "fields", "place", "status", "PlacesServiceStatus", "OK", "_place$geometry", "_place$geometry2", "place_id", "name", "address", "formatted_address", "lat", "geometry", "location", "lng", "getAutocompletePredictions", "input", "options", "AutocompleteService", "getPlacePredictions", "types", "componentRestrictions", "country", "predictions", "suggestions", "map", "prediction", "id", "description", "ZERO_RESULTS"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/utils/googleMaps.utils.ts"], "sourcesContent": ["/**\n * Google Maps API utilities\n */\n\nlet isGoogleMapsLoaded = false;\nlet isGoogleMapsLoading = false;\nconst loadPromises: Promise<void>[] = [];\n\n/**\n * Dynamically load Google Maps API\n */\nexport const loadGoogleMapsAPI = (): Promise<void> => {\n  return new Promise((resolve, reject) => {\n    // If already loaded, resolve immediately\n    if (isGoogleMapsLoaded && window.google && window.google.maps) {\n      resolve();\n      return;\n    }\n\n    // If currently loading, wait for existing promise\n    if (isGoogleMapsLoading) {\n      const existingPromise = loadPromises[loadPromises.length - 1];\n      if (existingPromise) {\n        existingPromise.then(resolve).catch(reject);\n        return;\n      }\n    }\n\n    isGoogleMapsLoading = true;\n\n    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;\n    \n    // Check if API key is available\n    if (!apiKey || apiKey === 'YOUR_GOOGLE_MAPS_API_KEY') {\n      console.warn('Google Maps API key not configured. Using fallback location suggestions.');\n      isGoogleMapsLoading = false;\n      resolve(); // Resolve anyway to allow fallback functionality\n      return;\n    }\n\n    // Check if script already exists\n    const existingScript = document.querySelector('script[src*=\"maps.googleapis.com\"]');\n    if (existingScript) {\n      // Wait for existing script to load\n      existingScript.addEventListener('load', () => {\n        isGoogleMapsLoaded = true;\n        isGoogleMapsLoading = false;\n        resolve();\n      });\n      existingScript.addEventListener('error', () => {\n        isGoogleMapsLoading = false;\n        reject(new Error('Failed to load Google Maps API'));\n      });\n      return;\n    }\n\n    // Create and load the script\n    const script = document.createElement('script');\n    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;\n    script.async = true;\n    script.defer = true;\n\n    script.onload = () => {\n      isGoogleMapsLoaded = true;\n      isGoogleMapsLoading = false;\n      resolve();\n    };\n\n    script.onerror = () => {\n      isGoogleMapsLoading = false;\n      reject(new Error('Failed to load Google Maps API'));\n    };\n\n    document.head.appendChild(script);\n  });\n};\n\n/**\n * Check if Google Maps API is available\n */\nexport const isGoogleMapsAPIAvailable = (): boolean => {\n  return !!(window.google && window.google.maps && window.google.maps.places);\n};\n\n/**\n * Get place details using Google Places API\n */\nexport const getPlaceDetails = (placeId: string): Promise<any> => {\n  return new Promise((resolve, reject) => {\n    if (!isGoogleMapsAPIAvailable()) {\n      reject(new Error('Google Maps API not available'));\n      return;\n    }\n\n    const service = new window.google.maps.places.PlacesService(\n      document.createElement('div')\n    );\n\n    service.getDetails(\n      {\n        placeId: placeId,\n        fields: ['place_id', 'name', 'geometry', 'formatted_address']\n      },\n      (place, status) => {\n        if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {\n          resolve({\n            placeId: place.place_id,\n            name: place.name,\n            address: place.formatted_address,\n            lat: place.geometry?.location.lat(),\n            lng: place.geometry?.location.lng()\n          });\n        } else {\n          reject(new Error(`Place details request failed: ${status}`));\n        }\n      }\n    );\n  });\n};\n\n/**\n * Get autocomplete predictions using Google Places API\n */\nexport const getAutocompletePredictions = (\n  input: string,\n  options: {\n    types?: string[];\n    componentRestrictions?: { country?: string };\n  } = {}\n): Promise<any[]> => {\n  return new Promise((resolve, reject) => {\n    if (!isGoogleMapsAPIAvailable()) {\n      reject(new Error('Google Maps API not available'));\n      return;\n    }\n\n    const service = new window.google.maps.places.AutocompleteService();\n\n    service.getPlacePredictions(\n      {\n        input,\n        types: options.types || ['(cities)'],\n        componentRestrictions: options.componentRestrictions || { country: 'us' }\n      },\n      (predictions, status) => {\n        if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {\n          const suggestions = predictions.map((prediction) => ({\n            id: prediction.place_id,\n            name: prediction.description,\n            placeId: prediction.place_id,\n            types: prediction.types\n          }));\n          resolve(suggestions);\n        } else if (status === window.google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {\n          resolve([]);\n        } else {\n          reject(new Error(`Autocomplete request failed: ${status}`));\n        }\n      }\n    );\n  });\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,MAAMC,YAA6B,GAAG,EAAE;;AAExC;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAqB;EACpD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,IAAIN,kBAAkB,IAAIO,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;MAC7DJ,OAAO,CAAC,CAAC;MACT;IACF;;IAEA;IACA,IAAIJ,mBAAmB,EAAE;MACvB,MAAMS,eAAe,GAAGR,YAAY,CAACA,YAAY,CAACS,MAAM,GAAG,CAAC,CAAC;MAC7D,IAAID,eAAe,EAAE;QACnBA,eAAe,CAACE,IAAI,CAACP,OAAO,CAAC,CAACQ,KAAK,CAACP,MAAM,CAAC;QAC3C;MACF;IACF;IAEAL,mBAAmB,GAAG,IAAI;IAE1B,MAAMa,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,wBAAwB;;IAEvD;IACA,IAAI,CAACJ,MAAM,IAAIA,MAAM,KAAK,0BAA0B,EAAE;MACpDK,OAAO,CAACC,IAAI,CAAC,0EAA0E,CAAC;MACxFnB,mBAAmB,GAAG,KAAK;MAC3BI,OAAO,CAAC,CAAC,CAAC,CAAC;MACX;IACF;;IAEA;IACA,MAAMgB,cAAc,GAAGC,QAAQ,CAACC,aAAa,CAAC,oCAAoC,CAAC;IACnF,IAAIF,cAAc,EAAE;MAClB;MACAA,cAAc,CAACG,gBAAgB,CAAC,MAAM,EAAE,MAAM;QAC5CxB,kBAAkB,GAAG,IAAI;QACzBC,mBAAmB,GAAG,KAAK;QAC3BI,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;MACFgB,cAAc,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAM;QAC7CvB,mBAAmB,GAAG,KAAK;QAC3BK,MAAM,CAAC,IAAImB,KAAK,CAAC,gCAAgC,CAAC,CAAC;MACrD,CAAC,CAAC;MACF;IACF;;IAEA;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACK,aAAa,CAAC,QAAQ,CAAC;IAC/CD,MAAM,CAACE,GAAG,GAAG,+CAA+Cd,MAAM,mBAAmB;IACrFY,MAAM,CAACG,KAAK,GAAG,IAAI;IACnBH,MAAM,CAACI,KAAK,GAAG,IAAI;IAEnBJ,MAAM,CAACK,MAAM,GAAG,MAAM;MACpB/B,kBAAkB,GAAG,IAAI;MACzBC,mBAAmB,GAAG,KAAK;MAC3BI,OAAO,CAAC,CAAC;IACX,CAAC;IAEDqB,MAAM,CAACM,OAAO,GAAG,MAAM;MACrB/B,mBAAmB,GAAG,KAAK;MAC3BK,MAAM,CAAC,IAAImB,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACrD,CAAC;IAEDH,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACR,MAAM,CAAC;EACnC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,wBAAwB,GAAGA,CAAA,KAAe;EACrD,OAAO,CAAC,EAAE5B,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIF,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAAC;AAC7E,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAIC,OAAe,IAAmB;EAChE,OAAO,IAAIlC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,CAAC6B,wBAAwB,CAAC,CAAC,EAAE;MAC/B7B,MAAM,CAAC,IAAImB,KAAK,CAAC,+BAA+B,CAAC,CAAC;MAClD;IACF;IAEA,MAAMc,OAAO,GAAG,IAAIhC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAACI,aAAa,CACzDlB,QAAQ,CAACK,aAAa,CAAC,KAAK,CAC9B,CAAC;IAEDY,OAAO,CAACE,UAAU,CAChB;MACEH,OAAO,EAAEA,OAAO;MAChBI,MAAM,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,mBAAmB;IAC9D,CAAC,EACD,CAACC,KAAK,EAAEC,MAAM,KAAK;MACjB,IAAIA,MAAM,KAAKrC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAACS,mBAAmB,CAACC,EAAE,IAAIH,KAAK,EAAE;QAAA,IAAAI,eAAA,EAAAC,gBAAA;QACxE3C,OAAO,CAAC;UACNiC,OAAO,EAAEK,KAAK,CAACM,QAAQ;UACvBC,IAAI,EAAEP,KAAK,CAACO,IAAI;UAChBC,OAAO,EAAER,KAAK,CAACS,iBAAiB;UAChCC,GAAG,GAAAN,eAAA,GAAEJ,KAAK,CAACW,QAAQ,cAAAP,eAAA,uBAAdA,eAAA,CAAgBQ,QAAQ,CAACF,GAAG,CAAC,CAAC;UACnCG,GAAG,GAAAR,gBAAA,GAAEL,KAAK,CAACW,QAAQ,cAAAN,gBAAA,uBAAdA,gBAAA,CAAgBO,QAAQ,CAACC,GAAG,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLlD,MAAM,CAAC,IAAImB,KAAK,CAAC,iCAAiCmB,MAAM,EAAE,CAAC,CAAC;MAC9D;IACF,CACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,0BAA0B,GAAGA,CACxCC,KAAa,EACbC,OAGC,GAAG,CAAC,CAAC,KACa;EACnB,OAAO,IAAIvD,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,IAAI,CAAC6B,wBAAwB,CAAC,CAAC,EAAE;MAC/B7B,MAAM,CAAC,IAAImB,KAAK,CAAC,+BAA+B,CAAC,CAAC;MAClD;IACF;IAEA,MAAMc,OAAO,GAAG,IAAIhC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAACwB,mBAAmB,CAAC,CAAC;IAEnErB,OAAO,CAACsB,mBAAmB,CACzB;MACEH,KAAK;MACLI,KAAK,EAAEH,OAAO,CAACG,KAAK,IAAI,CAAC,UAAU,CAAC;MACpCC,qBAAqB,EAAEJ,OAAO,CAACI,qBAAqB,IAAI;QAAEC,OAAO,EAAE;MAAK;IAC1E,CAAC,EACD,CAACC,WAAW,EAAErB,MAAM,KAAK;MACvB,IAAIA,MAAM,KAAKrC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAACS,mBAAmB,CAACC,EAAE,IAAImB,WAAW,EAAE;QAC9E,MAAMC,WAAW,GAAGD,WAAW,CAACE,GAAG,CAAEC,UAAU,KAAM;UACnDC,EAAE,EAAED,UAAU,CAACnB,QAAQ;UACvBC,IAAI,EAAEkB,UAAU,CAACE,WAAW;UAC5BhC,OAAO,EAAE8B,UAAU,CAACnB,QAAQ;UAC5Ba,KAAK,EAAEM,UAAU,CAACN;QACpB,CAAC,CAAC,CAAC;QACHzD,OAAO,CAAC6D,WAAW,CAAC;MACtB,CAAC,MAAM,IAAItB,MAAM,KAAKrC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,MAAM,CAACS,mBAAmB,CAAC0B,YAAY,EAAE;QAChFlE,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,MAAM;QACLC,MAAM,CAAC,IAAImB,KAAK,CAAC,gCAAgCmB,MAAM,EAAE,CAAC,CAAC;MAC7D;IACF,CACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}