{"ast": null, "code": "import * as React from 'react';\nexport const ListContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ListContext.displayName = 'ListContext';\n}", "map": {"version": 3, "names": ["React", "ListContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useList/ListContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const ListContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ListContext.displayName = 'ListContext';\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACjE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,WAAW,CAACK,WAAW,GAAG,aAAa;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}