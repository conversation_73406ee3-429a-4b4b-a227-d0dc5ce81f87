{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\screens\\\\geoGrid\\\\geoGrid.screen.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Container, Typography, Paper, Grid, Alert, Snackbar } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService from \"../../services/geoGrid/geoGrid.service\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GeoGridScreen = ({\n  title\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [gridPoints, setGridPoints] = useState([]);\n  const [gridConfiguration, setGridConfiguration] = useState({\n    name: \"\",\n    centerLat: 0,\n    centerLng: 0,\n    gridSize: \"3x3\",\n    distance: 500,\n    distanceUnit: \"meters\",\n    searchType: \"name\",\n    searchQuery: \"\",\n    isScheduleEnabled: false,\n    settings: {}\n  });\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState([]);\n  const user = useSelector(state => {\n    var _state$authReducer;\n    return (_state$authReducer = state.authReducer) === null || _state$authReducer === void 0 ? void 0 : _state$authReducer.userInfo;\n  });\n  useEffect(() => {\n    document.title = title;\n    if (user !== null && user !== void 0 && user.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n  const loadSavedConfigurations = async () => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error) {\n      setError(\"Failed to load saved configurations\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLocationSearch = async searchRequest => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.searchLocation(searchRequest);\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration(prev => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\"\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateGrid = async (centerLat, centerLng) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit\n      });\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleConfigurationChange = updates => {\n    setGridConfiguration(prev => ({\n      ...prev,\n      ...updates\n    }));\n  };\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (!(user !== null && user !== void 0 && user.id)) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints\n      };\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoadConfiguration = async config => {\n    try {\n      setLoading(true);\n      setError(null);\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const {\n            configuration,\n            gridPoints: loadedPoints\n          } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\"\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteConfiguration = async configId => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await geoGridService.deleteGridConfiguration(configId.toString());\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!(user !== null && user !== void 0 && user.id)) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        py: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: \"Google Geo Grid\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: \"Please log in to access the Google Geo Grid functionality. You can still explore the basic features, but saving configurations requires authentication.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 3,\n          mt: 2\n        },\n        children: \"Create and manage location-based grids for your business analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: \"fit-content\"\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n              onLocationSearch: handleLocationSearch,\n              onGenerateGrid: generateGrid,\n              onSaveConfiguration: () => setError(\"Please log in to save configurations\"),\n              loading: loading,\n              currentLocation: currentLocation,\n              savedConfigurations: [],\n              onLoadConfiguration: () => {},\n              onDeleteConfiguration: () => {}\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              height: 600\n            },\n            children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n              center: currentLocation ? {\n                lat: currentLocation.lat,\n                lng: currentLocation.lng\n              } : null,\n              gridPoints: gridPoints,\n              loading: loading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!error,\n        autoHideDuration: 6000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"error\",\n          sx: {\n            width: \"100%\"\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: !!success,\n        autoHideDuration: 4000,\n        onClose: handleCloseSnackbar,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"right\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseSnackbar,\n          severity: \"success\",\n          sx: {\n            width: \"100%\"\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"Google Geo Grid\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 3\n      },\n      children: \"Create and manage location-based grids for your business analysis\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: \"fit-content\"\n          },\n          children: /*#__PURE__*/_jsxDEV(GeoGridControls, {\n            onLocationSearch: handleLocationSearch,\n            onGenerateGrid: generateGrid,\n            onSaveConfiguration: handleSaveConfiguration,\n            loading: loading,\n            currentLocation: currentLocation,\n            savedConfigurations: savedConfigurations,\n            onLoadConfiguration: handleLoadConfiguration,\n            onDeleteConfiguration: handleDeleteConfiguration\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 5,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 600\n          },\n          children: /*#__PURE__*/_jsxDEV(GeoGridMap, {\n            center: currentLocation ? {\n              lat: currentLocation.lat,\n              lng: currentLocation.lng\n            } : null,\n            gridPoints: gridPoints,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: \"fit-content\"\n          },\n          children: /*#__PURE__*/_jsxDEV(GeoGridSettings, {\n            configuration: gridConfiguration,\n            onConfigurationChange: handleConfigurationChange,\n            onGenerateGrid: generateGrid,\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: !!error,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: \"error\",\n        sx: {\n          width: \"100%\"\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: !!success,\n      autoHideDuration: 4000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"right\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: \"success\",\n        sx: {\n          width: \"100%\"\n        },\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 332,\n    columnNumber: 5\n  }, this);\n};\n_s(GeoGridScreen, \"tEorPfkABF2zEJPnS4zwntqisSU=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = GeoGridScreen;\nexport default GeoGridScreen;\nvar _c;\n$RefreshReg$(_c, \"GeoGridScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Paper", "Grid", "<PERSON><PERSON>", "Snackbar", "useSelector", "useDispatch", "GeoGridControls", "GeoGridMap", "GeoGridSettings", "GeoGridService", "jsxDEV", "_jsxDEV", "GeoGridScreen", "title", "_s", "dispatch", "geoGridService", "loading", "setLoading", "error", "setError", "success", "setSuccess", "currentLocation", "setCurrentLocation", "gridPoints", "setGridPoints", "gridConfiguration", "setGridConfiguration", "name", "centerLat", "centerLng", "gridSize", "distance", "distanceUnit", "searchType", "searchQuery", "isScheduleEnabled", "settings", "activeTab", "setActiveTab", "savedConfigurations", "setSavedConfigurations", "user", "state", "_state$authReducer", "authReducer", "userInfo", "document", "id", "loadSavedConfigurations", "response", "getGridConfigurations", "data", "handleLocationSearch", "searchRequest", "searchLocation", "locationData", "prev", "lat", "lng", "query", "generateGrid", "message", "Error", "handleConfigurationChange", "updates", "handleSaveConfiguration", "trim", "configToSave", "userId", "saveGridConfiguration", "handleLoadConfiguration", "config", "getGridData", "toString", "configuration", "loadedPoints", "address", "placeId", "handleDeleteConfiguration", "configId", "deleteGridConfiguration", "handleCloseSnackbar", "max<PERSON><PERSON><PERSON>", "sx", "py", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mt", "color", "mb", "container", "spacing", "item", "xs", "md", "p", "height", "onLocationSearch", "onGenerateGrid", "onSaveConfiguration", "onLoadConfiguration", "onDeleteConfiguration", "center", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "onConfigurationChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/screens/geoGrid/geoGrid.screen.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport {\n  Box,\n  Container,\n  Typography,\n  Paper,\n  Grid,\n  Button,\n  Alert,\n  Snackbar,\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport GeoGridControls from \"../../components/geoGrid/GeoGridControls.component\";\nimport GeoGridMap from \"../../components/geoGrid/GeoGridMap.component\";\nimport GeoGridSettings from \"../../components/geoGrid/GeoGridSettings.component\";\nimport GeoGridService, {\n  GridConfiguration,\n  GridPoint,\n  LocationSearchRequest,\n} from \"../../services/geoGrid/geoGrid.service\";\n\ninterface GeoGridScreenProps {\n  title: string;\n}\n\ninterface LocationData {\n  name: string;\n  lat: number;\n  lng: number;\n  address: string;\n  placeId: string;\n}\n\nconst GeoGridScreen: React.FC<GeoGridScreenProps> = ({ title }) => {\n  const dispatch = useDispatch();\n  const [geoGridService] = useState(new GeoGridService(dispatch));\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  // Grid state\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(\n    null\n  );\n  const [gridPoints, setGridPoints] = useState<GridPoint[]>([]);\n  const [gridConfiguration, setGridConfiguration] = useState<GridConfiguration>(\n    {\n      name: \"\",\n      centerLat: 0,\n      centerLng: 0,\n      gridSize: \"3x3\",\n      distance: 500,\n      distanceUnit: \"meters\",\n      searchType: \"name\",\n      searchQuery: \"\",\n      isScheduleEnabled: false,\n      settings: {},\n    }\n  );\n\n  // UI state\n  const [activeTab, setActiveTab] = useState(0);\n  const [savedConfigurations, setSavedConfigurations] = useState<\n    GridConfiguration[]\n  >([]);\n\n  const user = useSelector((state: any) => state.authReducer?.userInfo);\n\n  useEffect(() => {\n    document.title = title;\n    if (user?.id) {\n      loadSavedConfigurations();\n    }\n  }, [title, user]);\n\n  const loadSavedConfigurations = async () => {\n    if (!user?.id) return;\n\n    try {\n      setLoading(true);\n      const response = await geoGridService.getGridConfigurations(user.id);\n      if (response.success) {\n        setSavedConfigurations(response.data);\n      }\n    } catch (error: any) {\n      setError(\"Failed to load saved configurations\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLocationSearch = async (searchRequest: LocationSearchRequest) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.searchLocation(searchRequest);\n\n      if (response.success) {\n        const locationData = response.data;\n        setCurrentLocation(locationData);\n        setGridConfiguration((prev) => ({\n          ...prev,\n          centerLat: locationData.lat,\n          centerLng: locationData.lng,\n          searchType: searchRequest.searchType,\n          searchQuery: searchRequest.query || \"\",\n        }));\n\n        // Auto-generate grid when location is found\n        await generateGrid(locationData.lat, locationData.lng);\n        setSuccess(\"Location found and grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to search location\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateGrid = async (centerLat?: number, centerLng?: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const lat = centerLat || gridConfiguration.centerLat;\n      const lng = centerLng || gridConfiguration.centerLng;\n\n      if (!lat || !lng) {\n        throw new Error(\"Center coordinates are required\");\n      }\n\n      const response = await geoGridService.generateGrid({\n        centerLat: lat,\n        centerLng: lng,\n        gridSize: gridConfiguration.gridSize,\n        distance: gridConfiguration.distance,\n        distanceUnit: gridConfiguration.distanceUnit,\n      });\n\n      if (response.success) {\n        setGridPoints(response.data.gridPoints);\n        setSuccess(\"Grid generated successfully!\");\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to generate grid\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleConfigurationChange = (updates: Partial<GridConfiguration>) => {\n    setGridConfiguration((prev) => ({ ...prev, ...updates }));\n  };\n\n  const handleSaveConfiguration = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (!user?.id) {\n        throw new Error(\"Please log in to save configurations\");\n      }\n\n      if (!gridConfiguration.name.trim()) {\n        throw new Error(\"Configuration name is required\");\n      }\n\n      const configToSave = {\n        ...gridConfiguration,\n        userId: user.id,\n        gridPoints,\n      };\n\n      const response = await geoGridService.saveGridConfiguration(configToSave);\n\n      if (response.success) {\n        setSuccess(\"Configuration saved successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to save configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoadConfiguration = async (config: GridConfiguration) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      if (config.id) {\n        const response = await geoGridService.getGridData(config.id.toString());\n        if (response.success) {\n          const { configuration, gridPoints: loadedPoints } = response.data;\n          setGridConfiguration(configuration);\n          setGridPoints(loadedPoints || []);\n          setCurrentLocation({\n            name: configuration.searchQuery || \"Loaded Location\",\n            lat: configuration.centerLat,\n            lng: configuration.centerLng,\n            address: \"\",\n            placeId: \"\",\n          });\n          setSuccess(\"Configuration loaded successfully!\");\n        }\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to load configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteConfiguration = async (configId: number) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await geoGridService.deleteGridConfiguration(\n        configId.toString()\n      );\n\n      if (response.success) {\n        setSuccess(\"Configuration deleted successfully!\");\n        await loadSavedConfigurations();\n      }\n    } catch (error: any) {\n      setError(error.message || \"Failed to delete configuration\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCloseSnackbar = () => {\n    setError(null);\n    setSuccess(null);\n  };\n\n  // Show login message if user is not authenticated\n  if (!user?.id) {\n    return (\n      <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Google Geo Grid\n        </Typography>\n\n        <Alert severity=\"warning\" sx={{ mt: 2 }}>\n          Please log in to access the Google Geo Grid functionality. You can\n          still explore the basic features, but saving configurations requires\n          authentication.\n        </Alert>\n\n        <Typography\n          variant=\"body1\"\n          color=\"text.secondary\"\n          sx={{ mb: 3, mt: 2 }}\n        >\n          Create and manage location-based grids for your business analysis\n        </Typography>\n\n        {/* Show limited functionality for non-authenticated users */}\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 2, height: \"fit-content\" }}>\n              <GeoGridControls\n                onLocationSearch={handleLocationSearch}\n                onGenerateGrid={generateGrid}\n                onSaveConfiguration={() =>\n                  setError(\"Please log in to save configurations\")\n                }\n                loading={loading}\n                currentLocation={currentLocation}\n                savedConfigurations={[]}\n                onLoadConfiguration={() => {}}\n                onDeleteConfiguration={() => {}}\n              />\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 2, height: 600 }}>\n              <GeoGridMap\n                center={\n                  currentLocation\n                    ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                    : null\n                }\n                gridPoints={gridPoints}\n                loading={loading}\n              />\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Snackbar for notifications */}\n        <Snackbar\n          open={!!error}\n          autoHideDuration={6000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"error\"\n            sx={{ width: \"100%\" }}\n          >\n            {error}\n          </Alert>\n        </Snackbar>\n\n        <Snackbar\n          open={!!success}\n          autoHideDuration={4000}\n          onClose={handleCloseSnackbar}\n          anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n        >\n          <Alert\n            onClose={handleCloseSnackbar}\n            severity=\"success\"\n            sx={{ width: \"100%\" }}\n          >\n            {success}\n          </Alert>\n        </Snackbar>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        Google Geo Grid\n      </Typography>\n\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n        Create and manage location-based grids for your business analysis\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Controls Panel */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 2, height: \"fit-content\" }}>\n            <GeoGridControls\n              onLocationSearch={handleLocationSearch}\n              onGenerateGrid={generateGrid}\n              onSaveConfiguration={handleSaveConfiguration}\n              loading={loading}\n              currentLocation={currentLocation}\n              savedConfigurations={savedConfigurations}\n              onLoadConfiguration={handleLoadConfiguration}\n              onDeleteConfiguration={handleDeleteConfiguration}\n            />\n          </Paper>\n        </Grid>\n\n        {/* Map Panel */}\n        <Grid item xs={12} md={5}>\n          <Paper sx={{ p: 2, height: 600 }}>\n            <GeoGridMap\n              center={\n                currentLocation\n                  ? { lat: currentLocation.lat, lng: currentLocation.lng }\n                  : null\n              }\n              gridPoints={gridPoints}\n              loading={loading}\n            />\n          </Paper>\n        </Grid>\n\n        {/* Settings Panel */}\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 2, height: \"fit-content\" }}>\n            <GeoGridSettings\n              configuration={gridConfiguration}\n              onConfigurationChange={handleConfigurationChange}\n              onGenerateGrid={generateGrid}\n              loading={loading}\n            />\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Snackbar for notifications */}\n      <Snackbar\n        open={!!error}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity=\"error\"\n          sx={{ width: \"100%\" }}\n        >\n          {error}\n        </Alert>\n      </Snackbar>\n\n      <Snackbar\n        open={!!success}\n        autoHideDuration={4000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: \"bottom\", horizontal: \"right\" }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity=\"success\"\n          sx={{ width: \"100%\" }}\n        >\n          {success}\n        </Alert>\n      </Snackbar>\n    </Container>\n  );\n};\n\nexport default GeoGridScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAqB,OAAO;AAC/D,SAEEC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,IAAI,EAEJC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,UAAU,MAAM,+CAA+C;AACtE,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,cAAc,MAId,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAchD,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAIa,cAAc,CAACM,QAAQ,CAAC,CAAC;EAC/D,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAgB,IAAI,CAAC;;EAE3D;EACA,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CACpD,IACF,CAAC;EACD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CACxD;IACEiC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,MAAM;IAClBC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,KAAK;IACxBC,QAAQ,EAAE,CAAC;EACb,CACF,CAAC;;EAED;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAE5D,EAAE,CAAC;EAEL,MAAM+C,IAAI,GAAGvC,WAAW,CAAEwC,KAAU;IAAA,IAAAC,kBAAA;IAAA,QAAAA,kBAAA,GAAKD,KAAK,CAACE,WAAW,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,QAAQ;EAAA,EAAC;EAErElD,SAAS,CAAC,MAAM;IACdmD,QAAQ,CAACnC,KAAK,GAAGA,KAAK;IACtB,IAAI8B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,EAAE;MACZC,uBAAuB,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACrC,KAAK,EAAE8B,IAAI,CAAC,CAAC;EAEjB,MAAMO,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,EAACP,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IAEf,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,QAAQ,GAAG,MAAMnC,cAAc,CAACoC,qBAAqB,CAACT,IAAI,CAACM,EAAE,CAAC;MACpE,IAAIE,QAAQ,CAAC9B,OAAO,EAAE;QACpBqB,sBAAsB,CAACS,QAAQ,CAACE,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOlC,KAAU,EAAE;MACnBC,QAAQ,CAAC,qCAAqC,CAAC;IACjD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAG,MAAOC,aAAoC,IAAK;IAC3E,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM+B,QAAQ,GAAG,MAAMnC,cAAc,CAACwC,cAAc,CAACD,aAAa,CAAC;MAEnE,IAAIJ,QAAQ,CAAC9B,OAAO,EAAE;QACpB,MAAMoC,YAAY,GAAGN,QAAQ,CAACE,IAAI;QAClC7B,kBAAkB,CAACiC,YAAY,CAAC;QAChC7B,oBAAoB,CAAE8B,IAAI,KAAM;UAC9B,GAAGA,IAAI;UACP5B,SAAS,EAAE2B,YAAY,CAACE,GAAG;UAC3B5B,SAAS,EAAE0B,YAAY,CAACG,GAAG;UAC3BzB,UAAU,EAAEoB,aAAa,CAACpB,UAAU;UACpCC,WAAW,EAAEmB,aAAa,CAACM,KAAK,IAAI;QACtC,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,YAAY,CAACL,YAAY,CAACE,GAAG,EAAEF,YAAY,CAACG,GAAG,CAAC;QACtDtC,UAAU,CAAC,iDAAiD,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,2BAA2B,CAAC;IACxD,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAAA,CAAOhC,SAAkB,EAAEC,SAAkB,KAAK;IACrE,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuC,GAAG,GAAG7B,SAAS,IAAIH,iBAAiB,CAACG,SAAS;MACpD,MAAM8B,GAAG,GAAG7B,SAAS,IAAIJ,iBAAiB,CAACI,SAAS;MAEpD,IAAI,CAAC4B,GAAG,IAAI,CAACC,GAAG,EAAE;QAChB,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMb,QAAQ,GAAG,MAAMnC,cAAc,CAAC8C,YAAY,CAAC;QACjDhC,SAAS,EAAE6B,GAAG;QACd5B,SAAS,EAAE6B,GAAG;QACd5B,QAAQ,EAAEL,iBAAiB,CAACK,QAAQ;QACpCC,QAAQ,EAAEN,iBAAiB,CAACM,QAAQ;QACpCC,YAAY,EAAEP,iBAAiB,CAACO;MAClC,CAAC,CAAC;MAEF,IAAIiB,QAAQ,CAAC9B,OAAO,EAAE;QACpBK,aAAa,CAACyB,QAAQ,CAACE,IAAI,CAAC5B,UAAU,CAAC;QACvCH,UAAU,CAAC,8BAA8B,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,yBAAyB,CAAC;IACtD,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,yBAAyB,GAAIC,OAAmC,IAAK;IACzEtC,oBAAoB,CAAE8B,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,GAAGQ;IAAQ,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACFjD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,EAACuB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;QACb,MAAM,IAAIe,KAAK,CAAC,sCAAsC,CAAC;MACzD;MAEA,IAAI,CAACrC,iBAAiB,CAACE,IAAI,CAACuC,IAAI,CAAC,CAAC,EAAE;QAClC,MAAM,IAAIJ,KAAK,CAAC,gCAAgC,CAAC;MACnD;MAEA,MAAMK,YAAY,GAAG;QACnB,GAAG1C,iBAAiB;QACpB2C,MAAM,EAAE3B,IAAI,CAACM,EAAE;QACfxB;MACF,CAAC;MAED,MAAM0B,QAAQ,GAAG,MAAMnC,cAAc,CAACuD,qBAAqB,CAACF,YAAY,CAAC;MAEzE,IAAIlB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,mCAAmC,CAAC;QAC/C,MAAM4B,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,uBAAuB,GAAG,MAAOC,MAAyB,IAAK;IACnE,IAAI;MACFvD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIqD,MAAM,CAACxB,EAAE,EAAE;QACb,MAAME,QAAQ,GAAG,MAAMnC,cAAc,CAAC0D,WAAW,CAACD,MAAM,CAACxB,EAAE,CAAC0B,QAAQ,CAAC,CAAC,CAAC;QACvE,IAAIxB,QAAQ,CAAC9B,OAAO,EAAE;UACpB,MAAM;YAAEuD,aAAa;YAAEnD,UAAU,EAAEoD;UAAa,CAAC,GAAG1B,QAAQ,CAACE,IAAI;UACjEzB,oBAAoB,CAACgD,aAAa,CAAC;UACnClD,aAAa,CAACmD,YAAY,IAAI,EAAE,CAAC;UACjCrD,kBAAkB,CAAC;YACjBK,IAAI,EAAE+C,aAAa,CAACxC,WAAW,IAAI,iBAAiB;YACpDuB,GAAG,EAAEiB,aAAa,CAAC9C,SAAS;YAC5B8B,GAAG,EAAEgB,aAAa,CAAC7C,SAAS;YAC5B+C,OAAO,EAAE,EAAE;YACXC,OAAO,EAAE;UACX,CAAC,CAAC;UACFzD,UAAU,CAAC,oCAAoC,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,8BAA8B,CAAC;IAC3D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8D,yBAAyB,GAAG,MAAOC,QAAgB,IAAK;IAC5D,IAAI;MACF/D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM+B,QAAQ,GAAG,MAAMnC,cAAc,CAACkE,uBAAuB,CAC3DD,QAAQ,CAACN,QAAQ,CAAC,CACpB,CAAC;MAED,IAAIxB,QAAQ,CAAC9B,OAAO,EAAE;QACpBC,UAAU,CAAC,qCAAqC,CAAC;QACjD,MAAM4B,uBAAuB,CAAC,CAAC;MACjC;IACF,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,QAAQ,CAACD,KAAK,CAAC4C,OAAO,IAAI,gCAAgC,CAAC;IAC7D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiE,mBAAmB,GAAGA,CAAA,KAAM;IAChC/D,QAAQ,CAAC,IAAI,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC;;EAED;EACA,IAAI,EAACqB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEM,EAAE,GAAE;IACb,oBACEtC,OAAA,CAACb,SAAS;MAACsF,QAAQ,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACrC5E,OAAA,CAACZ,UAAU;QAACyF,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnF,OAAA,CAACT,KAAK;QAAC6F,QAAQ,EAAC,SAAS;QAACV,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,EAAC;MAIzC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERnF,OAAA,CAACZ,UAAU;QACTyF,OAAO,EAAC,OAAO;QACfS,KAAK,EAAC,gBAAgB;QACtBZ,EAAE,EAAE;UAAEa,EAAE,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,EACtB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbnF,OAAA,CAACV,IAAI;QAACkG,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBACzB5E,OAAA,CAACV,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5E,OAAA,CAACX,KAAK;YAACqF,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAc,CAAE;YAAAlB,QAAA,eACzC5E,OAAA,CAACL,eAAe;cACdoG,gBAAgB,EAAEpD,oBAAqB;cACvCqD,cAAc,EAAE7C,YAAa;cAC7B8C,mBAAmB,EAAEA,CAAA,KACnBxF,QAAQ,CAAC,sCAAsC,CAChD;cACDH,OAAO,EAAEA,OAAQ;cACjBM,eAAe,EAAEA,eAAgB;cACjCkB,mBAAmB,EAAE,EAAG;cACxBoE,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAE;cAC9BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC;YAAE;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPnF,OAAA,CAACV,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvB5E,OAAA,CAACX,KAAK;YAACqF,EAAE,EAAE;cAAEmB,CAAC,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAI,CAAE;YAAAlB,QAAA,eAC/B5E,OAAA,CAACJ,UAAU;cACTwG,MAAM,EACJxF,eAAe,GACX;gBAAEoC,GAAG,EAAEpC,eAAe,CAACoC,GAAG;gBAAEC,GAAG,EAAErC,eAAe,CAACqC;cAAI,CAAC,GACtD,IACL;cACDnC,UAAU,EAAEA,UAAW;cACvBR,OAAO,EAAEA;YAAQ;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPnF,OAAA,CAACR,QAAQ;QACP6G,IAAI,EAAE,CAAC,CAAC7F,KAAM;QACd8F,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAE/B,mBAAoB;QAC7BgC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAA9B,QAAA,eAE1D5E,OAAA,CAACT,KAAK;UACJgH,OAAO,EAAE/B,mBAAoB;UAC7BY,QAAQ,EAAC,OAAO;UAChBV,EAAE,EAAE;YAAEiC,KAAK,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAErBpE;QAAK;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEXnF,OAAA,CAACR,QAAQ;QACP6G,IAAI,EAAE,CAAC,CAAC3F,OAAQ;QAChB4F,gBAAgB,EAAE,IAAK;QACvBC,OAAO,EAAE/B,mBAAoB;QAC7BgC,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAA9B,QAAA,eAE1D5E,OAAA,CAACT,KAAK;UACJgH,OAAO,EAAE/B,mBAAoB;UAC7BY,QAAQ,EAAC,SAAS;UAClBV,EAAE,EAAE;YAAEiC,KAAK,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAErBlE;QAAO;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEhB;EAEA,oBACEnF,OAAA,CAACb,SAAS;IAACsF,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACrC5E,OAAA,CAACZ,UAAU;MAACyF,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAH,QAAA,EAAC;IAErD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnF,OAAA,CAACZ,UAAU;MAACyF,OAAO,EAAC,OAAO;MAACS,KAAK,EAAC,gBAAgB;MAACZ,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EAAC;IAElE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnF,OAAA,CAACV,IAAI;MAACkG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAb,QAAA,gBAEzB5E,OAAA,CAACV,IAAI;QAACoG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvB5E,OAAA,CAACX,KAAK;UAACqF,EAAE,EAAE;YAAEmB,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAAlB,QAAA,eACzC5E,OAAA,CAACL,eAAe;YACdoG,gBAAgB,EAAEpD,oBAAqB;YACvCqD,cAAc,EAAE7C,YAAa;YAC7B8C,mBAAmB,EAAEzC,uBAAwB;YAC7ClD,OAAO,EAAEA,OAAQ;YACjBM,eAAe,EAAEA,eAAgB;YACjCkB,mBAAmB,EAAEA,mBAAoB;YACzCoE,mBAAmB,EAAErC,uBAAwB;YAC7CsC,qBAAqB,EAAE9B;UAA0B;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnF,OAAA,CAACV,IAAI;QAACoG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvB5E,OAAA,CAACX,KAAK;UAACqF,EAAE,EAAE;YAAEmB,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAI,CAAE;UAAAlB,QAAA,eAC/B5E,OAAA,CAACJ,UAAU;YACTwG,MAAM,EACJxF,eAAe,GACX;cAAEoC,GAAG,EAAEpC,eAAe,CAACoC,GAAG;cAAEC,GAAG,EAAErC,eAAe,CAACqC;YAAI,CAAC,GACtD,IACL;YACDnC,UAAU,EAAEA,UAAW;YACvBR,OAAO,EAAEA;UAAQ;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnF,OAAA,CAACV,IAAI;QAACoG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhB,QAAA,eACvB5E,OAAA,CAACX,KAAK;UAACqF,EAAE,EAAE;YAAEmB,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAAlB,QAAA,eACzC5E,OAAA,CAACH,eAAe;YACdoE,aAAa,EAAEjD,iBAAkB;YACjC4F,qBAAqB,EAAEtD,yBAA0B;YACjD0C,cAAc,EAAE7C,YAAa;YAC7B7C,OAAO,EAAEA;UAAQ;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnF,OAAA,CAACR,QAAQ;MACP6G,IAAI,EAAE,CAAC,CAAC7F,KAAM;MACd8F,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE/B,mBAAoB;MAC7BgC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAA9B,QAAA,eAE1D5E,OAAA,CAACT,KAAK;QACJgH,OAAO,EAAE/B,mBAAoB;QAC7BY,QAAQ,EAAC,OAAO;QAChBV,EAAE,EAAE;UAAEiC,KAAK,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAErBpE;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEXnF,OAAA,CAACR,QAAQ;MACP6G,IAAI,EAAE,CAAC,CAAC3F,OAAQ;MAChB4F,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE/B,mBAAoB;MAC7BgC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAA9B,QAAA,eAE1D5E,OAAA,CAACT,KAAK;QACJgH,OAAO,EAAE/B,mBAAoB;QAC7BY,QAAQ,EAAC,SAAS;QAClBV,EAAE,EAAE;UAAEiC,KAAK,EAAE;QAAO,CAAE;QAAA/B,QAAA,EAErBlE;MAAO;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB,CAAC;AAAChF,EAAA,CAhYIF,aAA2C;EAAA,QAC9BP,WAAW,EAgCfD,WAAW;AAAA;AAAAoH,EAAA,GAjCpB5G,aAA2C;AAkYjD,eAAeA,aAAa;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}