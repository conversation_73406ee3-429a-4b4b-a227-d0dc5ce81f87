import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
  InputAdornment,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";

interface AddServicesModalProps {
  open: boolean;
  onClose: () => void;
  categoryName: string;
  isPrimary: boolean;
  onAddService: (serviceName: string) => void;
}

const AddServicesModal: React.FC<AddServicesModalProps> = ({
  open,
  onClose,
  categoryName,
  isPrimary,
  onAddService,
}) => {
  const [showCustomServiceInput, setShowCustomServiceInput] = useState(false);
  const [customServiceName, setCustomServiceName] = useState("");
  const [error, setError] = useState("");

  const handleAddCustomService = () => {
    setShowCustomServiceInput(true);
  };

  const handleCancelCustomService = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
  };

  const handleSubmit = () => {
    if (showCustomServiceInput) {
      if (!customServiceName.trim()) {
        setError("Please enter a service name");
        return;
      }
      onAddService(customServiceName);
      setCustomServiceName("");
      setError("");
      setShowCustomServiceInput(false);
    }
    onClose();
  };

  const handleCancel = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        style: {
          backgroundColor: "#212121",
          color: "white",
          borderRadius: "8px",
        },
      }}
      sx={{
        "& .MuiDialog-paper": {
          margin: { xs: "16px", sm: "32px" },
          width: { xs: "calc(100% - 32px)", sm: "auto" },
          maxHeight: { xs: "calc(100% - 32px)", sm: "auto" },
        },
      }}
    >
      <AppBar position="relative" color="transparent" elevation={0}>
        <Toolbar
          sx={{
            minHeight: { xs: "48px", sm: "56px" },
            px: { xs: 1, sm: 2 },
          }}
        >
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleCancel}
            aria-label="back"
            sx={{ mr: { xs: 0.5, sm: 1 } }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              ml: 1,
              fontSize: { xs: "1rem", sm: "1.25rem" },
            }}
          >
            Add services
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCancel}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: "1rem", sm: "1.25rem" },
              wordBreak: "break-word",
            }}
          >
            {categoryName}
          </Typography>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ fontSize: { xs: "0.7rem", sm: "0.75rem" } }}
          >
            {isPrimary ? "Primary category" : "Additional category"}
          </Typography>
        </Box>

        <Box sx={{ mt: 3, mb: 2 }}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: { xs: "0.8rem", sm: "0.875rem" } }}
          >
            Don't see a service you offer? Create your own
          </Typography>
        </Box>

        {showCustomServiceInput ? (
          <Box sx={{ mb: 2 }}>
            <TextField
              autoFocus
              margin="dense"
              fullWidth
              variant="outlined"
              value={customServiceName}
              onChange={(e) => {
                // Limit to 120 characters
                if (e.target.value.length <= 120) {
                  setCustomServiceName(e.target.value);
                  if (e.target.value.trim()) {
                    setError("");
                  }
                }
              }}
              error={!!error}
              helperText={error}
              placeholder="Enter service name"
              InputProps={{
                style: { color: "white" },
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleCancelCustomService}
                      edge="end"
                      sx={{
                        color: "white",
                        p: { xs: 0.5, sm: 1 },
                      }}
                    >
                      <CancelIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              InputLabelProps={{
                style: { color: "#aaa" },
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.23)",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#90caf9",
                  },
                },
                "& .MuiFormHelperText-root": {
                  color: "#f44336",
                  marginLeft: 0,
                },
              }}
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                display: "block",
                textAlign: "right",
                fontSize: { xs: "0.65rem", sm: "0.75rem" },
                mt: 0.5,
              }}
            >
              {customServiceName.length}/120
            </Typography>
          </Box>
        ) : (
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddCustomService}
            sx={{
              color: "#90caf9",
              textTransform: "none",
              justifyContent: "flex-start",
              padding: "8px 0",
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              minWidth: "auto",
              "& .MuiButton-startIcon": {
                mr: { xs: 0.5, sm: 1 },
              },
            }}
          >
            Add custom service
          </Button>
        )}
      </DialogContent>

      <Box sx={{ flexGrow: 1 }} />

      <DialogActions
        sx={{
          p: { xs: 1.5, sm: 2 },
          justifyContent: "space-between",
        }}
      >
        <Button
          onClick={handleCancel}
          sx={{
            color: "#90caf9",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            "&:hover": {
              backgroundColor: "rgba(144, 202, 249, 0.08)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={showCustomServiceInput && !customServiceName.trim()}
          sx={{
            bgcolor: "#90caf9",
            color: "#000",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            px: { xs: 2, sm: 3 },
            "&:hover": {
              bgcolor: "#64b5f6",
            },
            "&.Mui-disabled": {
              bgcolor: "rgba(144, 202, 249, 0.3)",
              color: "rgba(0, 0, 0, 0.5)",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddServicesModal;
