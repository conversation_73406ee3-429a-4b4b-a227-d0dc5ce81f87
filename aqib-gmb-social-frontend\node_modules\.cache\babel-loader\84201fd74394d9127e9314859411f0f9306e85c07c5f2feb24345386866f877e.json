{"ast": null, "code": "import { DropdownActionTypes } from './useDropdown.types';\nexport function dropdownReducer(state, action) {\n  switch (action.type) {\n    case DropdownActionTypes.blur:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.escapeKeyDown:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.toggle:\n      return {\n        open: !state.open,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.open:\n      return {\n        open: true,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.close:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    default:\n      throw new Error(`Unhandled action`);\n  }\n}", "map": {"version": 3, "names": ["DropdownActionTypes", "dropdownReducer", "state", "action", "type", "blur", "open", "changeReason", "event", "escapeKeyDown", "toggle", "close", "Error"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useDropdown/dropdownReducer.js"], "sourcesContent": ["import { DropdownActionTypes } from './useDropdown.types';\nexport function dropdownReducer(state, action) {\n  switch (action.type) {\n    case DropdownActionTypes.blur:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.escapeKeyDown:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.toggle:\n      return {\n        open: !state.open,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.open:\n      return {\n        open: true,\n        changeReason: action.event\n      };\n    case DropdownActionTypes.close:\n      return {\n        open: false,\n        changeReason: action.event\n      };\n    default:\n      throw new Error(`Unhandled action`);\n  }\n}"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,qBAAqB;AACzD,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7C,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKJ,mBAAmB,CAACK,IAAI;MAC3B,OAAO;QACLC,IAAI,EAAE,KAAK;QACXC,YAAY,EAAEJ,MAAM,CAACK;MACvB,CAAC;IACH,KAAKR,mBAAmB,CAACS,aAAa;MACpC,OAAO;QACLH,IAAI,EAAE,KAAK;QACXC,YAAY,EAAEJ,MAAM,CAACK;MACvB,CAAC;IACH,KAAKR,mBAAmB,CAACU,MAAM;MAC7B,OAAO;QACLJ,IAAI,EAAE,CAACJ,KAAK,CAACI,IAAI;QACjBC,YAAY,EAAEJ,MAAM,CAACK;MACvB,CAAC;IACH,KAAKR,mBAAmB,CAACM,IAAI;MAC3B,OAAO;QACLA,IAAI,EAAE,IAAI;QACVC,YAAY,EAAEJ,MAAM,CAACK;MACvB,CAAC;IACH,KAAKR,mBAAmB,CAACW,KAAK;MAC5B,OAAO;QACLL,IAAI,EAAE,KAAK;QACXC,YAAY,EAAEJ,MAAM,CAACK;MACvB,CAAC;IACH;MACE,MAAM,IAAII,KAAK,CAAC,kBAAkB,CAAC;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}