import React from "react";
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Grid,
  CardMedia,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { TOPIC_TYPES } from "../../../constants/application.constant";
import { useSelector } from "react-redux";

const UpdateCard = (props: {
  title: string;
  description: string;
  buttonText: string;
  buttonColor: string;
  bgColor: string;
  imagePath: string;
  navigationType: string;
  isDisabled: boolean;
}) => {
  const navigate = useNavigate();
  const {
    title,
    description,
    buttonText,
    buttonColor,
    bgColor,
    imagePath,
    navigationType,
    isDisabled,
  } = props;

  return (
    <Card sx={{ backgroundColor: bgColor, borderRadius: 2, boxShadow: 3 }}>
      <CardMedia component="img" height="250" image={imagePath} alt={title} />
      <CardContent>
        <Typography variant="h6" fontWeight="bold">
          {title}
        </Typography>
        <Typography variant="body2" sx={{ marginBottom: 2 }}>
          {description}
        </Typography>
        <Button
          className="updatesShapeBtn"
          variant="contained"
          fullWidth
          color={
            buttonColor === "primary"
              ? "primary"
              : buttonColor === "success"
              ? "success"
              : "secondary"
          }
          onClick={() => navigate(`/post-management/create-social-post`)}
          disabled={!isDisabled}
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
};

const UpdatesSection = () => {
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);

  const updatesData = [
    {
      id: 1,
      title: "Post Updates",
      description:
        "Update your customers with the latest information about your Brand.",
      buttonText: "Create New Post",
      buttonColor: "secondary",
      bgColor: "#E8F0FE",
      imagePath: require("../../../assets/common/create-new-post.jpg"),
      navigationType: TOPIC_TYPES.Event,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
    {
      id: 2,
      title: "Offers & Deals Updates",
      description:
        "Add Offers & Deals to generate more customer interest & impressions",
      buttonText: "Create New Offer",
      buttonColor: "success",
      bgColor: "#E0F7FA",
      imagePath: require("../../../assets/common/offers-deals.jpg"),
      navigationType: TOPIC_TYPES.Offer,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
    {
      id: 3,
      title: "Event Updates",
      description:
        "Tell customers about upcoming Events & Occasions at your Location",
      buttonText: "Update Event Updates",
      buttonColor: "primary",
      bgColor: "#FCE4EC",
      imagePath: require("../../../assets/common/event-updates.jpg"),
      navigationType: TOPIC_TYPES.Event,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
  ];

  return (
    <Box sx={{ flexGrow: 1, padding: 3 }}>
      <Grid container spacing={2}>
        {updatesData.map((update) => (
          <Grid item xs={12} sm={6} md={4} key={update.id} sx={{ padding: 2 }}>
            <UpdateCard {...update} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default UpdatesSection;
