const pool = require("../config/db");
const fs = require("fs");
const path = require("path");
const logger = require("../utils/logger");

/**
 * Initialize Geo Grid database tables
 * This script creates the necessary tables for the Geo Grid functionality
 */

const initializeGeoGridTables = async () => {
  try {
    logger.info("Starting Geo Grid database initialization...");

    // Read the SQL migration file
    const migrationPath = path.join(__dirname, "../database/migrations/create_geo_grid_tables.sql");
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, "utf8");
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    logger.info(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      logger.info(`Executing statement ${i + 1}/${statements.length}`);
      
      try {
        await pool.query(statement);
        logger.info(`Statement ${i + 1} executed successfully`);
      } catch (error) {
        logger.error(`Error executing statement ${i + 1}:`, {
          error: error.message,
          statement: statement.substring(0, 100) + "...",
        });
        throw error;
      }
    }

    // Verify tables were created
    const verificationQueries = [
      "SHOW TABLES LIKE 'geo_grid_configurations'",
      "SHOW TABLES LIKE 'geo_grid_points'",
      "SHOW TABLES LIKE 'geo_grid_schedules'"
    ];

    for (const query of verificationQueries) {
      const result = await pool.query(query);
      if (result.length === 0) {
        throw new Error(`Table verification failed for query: ${query}`);
      }
    }

    logger.info("Geo Grid database initialization completed successfully!");
    
    // Log table information
    const configsTable = await pool.query("DESCRIBE geo_grid_configurations");
    const pointsTable = await pool.query("DESCRIBE geo_grid_points");
    const schedulesTable = await pool.query("DESCRIBE geo_grid_schedules");

    logger.info("Created tables:", {
      geo_grid_configurations: configsTable.length + " columns",
      geo_grid_points: pointsTable.length + " columns",
      geo_grid_schedules: schedulesTable.length + " columns",
    });

    return {
      success: true,
      message: "Geo Grid database initialized successfully",
      tables: ["geo_grid_configurations", "geo_grid_points", "geo_grid_schedules"]
    };

  } catch (error) {
    logger.error("Failed to initialize Geo Grid database:", {
      error: error.message,
      stack: error.stack,
    });

    return {
      success: false,
      message: "Failed to initialize Geo Grid database",
      error: error.message
    };
  }
};

/**
 * Check if Geo Grid tables exist
 */
const checkGeoGridTables = async () => {
  try {
    const tables = [
      "geo_grid_configurations",
      "geo_grid_points", 
      "geo_grid_schedules"
    ];

    const results = {};

    for (const table of tables) {
      const result = await pool.query(`SHOW TABLES LIKE '${table}'`);
      results[table] = result.length > 0;
    }

    return results;
  } catch (error) {
    logger.error("Error checking Geo Grid tables:", error);
    throw error;
  }
};

/**
 * Drop Geo Grid tables (for development/testing)
 */
const dropGeoGridTables = async () => {
  try {
    logger.info("Dropping Geo Grid tables...");

    const dropStatements = [
      "DROP TABLE IF EXISTS geo_grid_schedules",
      "DROP TABLE IF EXISTS geo_grid_points",
      "DROP TABLE IF EXISTS geo_grid_configurations"
    ];

    for (const statement of dropStatements) {
      await pool.query(statement);
      logger.info(`Executed: ${statement}`);
    }

    logger.info("Geo Grid tables dropped successfully");
    return { success: true, message: "Tables dropped successfully" };
  } catch (error) {
    logger.error("Error dropping Geo Grid tables:", error);
    return { success: false, error: error.message };
  }
};

// Export functions for use in other scripts
module.exports = {
  initializeGeoGridTables,
  checkGeoGridTables,
  dropGeoGridTables,
};

// Run initialization if this script is executed directly
if (require.main === module) {
  (async () => {
    try {
      const result = await initializeGeoGridTables();
      console.log(JSON.stringify(result, null, 2));
      process.exit(result.success ? 0 : 1);
    } catch (error) {
      console.error("Script execution failed:", error);
      process.exit(1);
    }
  })();
}
