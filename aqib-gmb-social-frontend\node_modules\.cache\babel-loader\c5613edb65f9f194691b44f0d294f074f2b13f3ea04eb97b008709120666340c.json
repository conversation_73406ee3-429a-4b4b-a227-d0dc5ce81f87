{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\n/**\n * The basic building block for creating custom switches.\n *\n * Demos:\n *\n * - [Switch](https://mui.com/base-ui/react-switch/#hook)\n *\n * API:\n *\n * - [useSwitch API](https://mui.com/base-ui/react-switch/hooks-api/#use-switch)\n */\nexport function useSwitch(props) {\n  const {\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible,\n    readOnly,\n    required\n  } = props;\n  const [checked, setCheckedState] = useControlled({\n    controlled: checkedProp,\n    default: Boolean(defaultChecked),\n    name: 'Switch',\n    state: 'checked'\n  });\n  const createHandleInputChange = otherProps => event => {\n    var _otherProps$onChange;\n    // Workaround for https://github.com/facebook/react/issues/9023\n    if (event.nativeEvent.defaultPrevented) {\n      return;\n    }\n    setCheckedState(event.target.checked);\n    onChange == null || onChange(event);\n    (_otherProps$onChange = otherProps.onChange) == null || _otherProps$onChange.call(otherProps, event);\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const inputRef = React.useRef(null);\n  const createHandleFocus = otherProps => event => {\n    var _otherProps$onFocus;\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!inputRef.current) {\n      inputRef.current = event.currentTarget;\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n      onFocusVisible == null || onFocusVisible(event);\n    }\n    onFocus == null || onFocus(event);\n    (_otherProps$onFocus = otherProps.onFocus) == null || _otherProps$onFocus.call(otherProps, event);\n  };\n  const createHandleBlur = otherProps => event => {\n    var _otherProps$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    onBlur == null || onBlur(event);\n    (_otherProps$onBlur = otherProps.onBlur) == null || _otherProps$onBlur.call(otherProps, event);\n  };\n  const handleInputRef = useForkRef(focusVisibleRef, inputRef);\n  const getInputProps = (otherProps = {}) => _extends({\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    readOnly,\n    ref: handleInputRef,\n    required,\n    type: 'checkbox',\n    role: 'switch',\n    'aria-checked': checkedProp\n  }, otherProps, {\n    onChange: createHandleInputChange(otherProps),\n    onFocus: createHandleFocus(otherProps),\n    onBlur: createHandleBlur(otherProps)\n  });\n  return {\n    checked,\n    disabled: Boolean(disabled),\n    focusVisible,\n    getInputProps,\n    inputRef: handleInputRef,\n    readOnly: Boolean(readOnly)\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useControlled", "useControlled", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "useSwitch", "props", "checked", "checkedProp", "defaultChecked", "disabled", "onBlur", "onChange", "onFocus", "onFocusVisible", "readOnly", "required", "setCheckedState", "controlled", "default", "Boolean", "name", "state", "createHandleInputChange", "otherProps", "event", "_otherProps$onChange", "nativeEvent", "defaultPrevented", "target", "call", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "ref", "focusVisibleRef", "focusVisible", "setFocusVisible", "useState", "useEffect", "current", "inputRef", "useRef", "createHandleFocus", "_otherProps$onFocus", "currentTarget", "createHandleBlur", "_otherProps$onBlur", "handleInputRef", "getInputProps", "type", "role"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useSwitch/useSwitch.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\n/**\n * The basic building block for creating custom switches.\n *\n * Demos:\n *\n * - [Switch](https://mui.com/base-ui/react-switch/#hook)\n *\n * API:\n *\n * - [useSwitch API](https://mui.com/base-ui/react-switch/hooks-api/#use-switch)\n */\nexport function useSwitch(props) {\n  const {\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible,\n    readOnly,\n    required\n  } = props;\n  const [checked, setCheckedState] = useControlled({\n    controlled: checkedProp,\n    default: Boolean(defaultChecked),\n    name: 'Switch',\n    state: 'checked'\n  });\n  const createHandleInputChange = otherProps => event => {\n    var _otherProps$onChange;\n    // Workaround for https://github.com/facebook/react/issues/9023\n    if (event.nativeEvent.defaultPrevented) {\n      return;\n    }\n    setCheckedState(event.target.checked);\n    onChange == null || onChange(event);\n    (_otherProps$onChange = otherProps.onChange) == null || _otherProps$onChange.call(otherProps, event);\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const inputRef = React.useRef(null);\n  const createHandleFocus = otherProps => event => {\n    var _otherProps$onFocus;\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!inputRef.current) {\n      inputRef.current = event.currentTarget;\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n      onFocusVisible == null || onFocusVisible(event);\n    }\n    onFocus == null || onFocus(event);\n    (_otherProps$onFocus = otherProps.onFocus) == null || _otherProps$onFocus.call(otherProps, event);\n  };\n  const createHandleBlur = otherProps => event => {\n    var _otherProps$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    onBlur == null || onBlur(event);\n    (_otherProps$onBlur = otherProps.onBlur) == null || _otherProps$onBlur.call(otherProps, event);\n  };\n  const handleInputRef = useForkRef(focusVisibleRef, inputRef);\n  const getInputProps = (otherProps = {}) => _extends({\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    readOnly,\n    ref: handleInputRef,\n    required,\n    type: 'checkbox',\n    role: 'switch',\n    'aria-checked': checkedProp\n  }, otherProps, {\n    onChange: createHandleInputChange(otherProps),\n    onFocus: createHandleFocus(otherProps),\n    onBlur: createHandleBlur(otherProps)\n  });\n  return {\n    checked,\n    disabled: Boolean(disabled),\n    focusVisible,\n    getInputProps,\n    inputRef: handleInputRef,\n    readOnly: Boolean(readOnly)\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AACxJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,MAAM;IACJC,OAAO,EAAEC,WAAW;IACpBC,cAAc;IACdC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,GAAGV,KAAK;EACT,MAAM,CAACC,OAAO,EAAEU,eAAe,CAAC,GAAGjB,aAAa,CAAC;IAC/CkB,UAAU,EAAEV,WAAW;IACvBW,OAAO,EAAEC,OAAO,CAACX,cAAc,CAAC;IAChCY,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,uBAAuB,GAAGC,UAAU,IAAIC,KAAK,IAAI;IACrD,IAAIC,oBAAoB;IACxB;IACA,IAAID,KAAK,CAACE,WAAW,CAACC,gBAAgB,EAAE;MACtC;IACF;IACAX,eAAe,CAACQ,KAAK,CAACI,MAAM,CAACtB,OAAO,CAAC;IACrCK,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACa,KAAK,CAAC;IACnC,CAACC,oBAAoB,GAAGF,UAAU,CAACZ,QAAQ,KAAK,IAAI,IAAIc,oBAAoB,CAACI,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EACtG,CAAC;EACD,MAAM;IACJM,iBAAiB;IACjBpB,MAAM,EAAEqB,iBAAiB;IACzBnB,OAAO,EAAEoB,kBAAkB;IAC3BC,GAAG,EAAEC;EACP,CAAC,GAAG/B,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAC,KAAK,CAAC;EAC7D,IAAI5B,QAAQ,IAAI0B,YAAY,EAAE;IAC5BC,eAAe,CAAC,KAAK,CAAC;EACxB;EACAvC,KAAK,CAACyC,SAAS,CAAC,MAAM;IACpBR,iBAAiB,CAACS,OAAO,GAAGJ,YAAY;EAC1C,CAAC,EAAE,CAACA,YAAY,EAAEL,iBAAiB,CAAC,CAAC;EACrC,MAAMU,QAAQ,GAAG3C,KAAK,CAAC4C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMC,iBAAiB,GAAGnB,UAAU,IAAIC,KAAK,IAAI;IAC/C,IAAImB,mBAAmB;IACvB;IACA,IAAI,CAACH,QAAQ,CAACD,OAAO,EAAE;MACrBC,QAAQ,CAACD,OAAO,GAAGf,KAAK,CAACoB,aAAa;IACxC;IACAZ,kBAAkB,CAACR,KAAK,CAAC;IACzB,IAAIM,iBAAiB,CAACS,OAAO,KAAK,IAAI,EAAE;MACtCH,eAAe,CAAC,IAAI,CAAC;MACrBvB,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACW,KAAK,CAAC;IACjD;IACAZ,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACY,KAAK,CAAC;IACjC,CAACmB,mBAAmB,GAAGpB,UAAU,CAACX,OAAO,KAAK,IAAI,IAAI+B,mBAAmB,CAACd,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EACnG,CAAC;EACD,MAAMqB,gBAAgB,GAAGtB,UAAU,IAAIC,KAAK,IAAI;IAC9C,IAAIsB,kBAAkB;IACtBf,iBAAiB,CAACP,KAAK,CAAC;IACxB,IAAIM,iBAAiB,CAACS,OAAO,KAAK,KAAK,EAAE;MACvCH,eAAe,CAAC,KAAK,CAAC;IACxB;IACA1B,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACc,KAAK,CAAC;IAC/B,CAACsB,kBAAkB,GAAGvB,UAAU,CAACb,MAAM,KAAK,IAAI,IAAIoC,kBAAkB,CAACjB,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EAChG,CAAC;EACD,MAAMuB,cAAc,GAAG9C,UAAU,CAACiC,eAAe,EAAEM,QAAQ,CAAC;EAC5D,MAAMQ,aAAa,GAAGA,CAACzB,UAAU,GAAG,CAAC,CAAC,KAAK3B,QAAQ,CAAC;IAClDU,OAAO,EAAEC,WAAW;IACpBC,cAAc;IACdC,QAAQ;IACRK,QAAQ;IACRmB,GAAG,EAAEc,cAAc;IACnBhC,QAAQ;IACRkC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,QAAQ;IACd,cAAc,EAAE3C;EAClB,CAAC,EAAEgB,UAAU,EAAE;IACbZ,QAAQ,EAAEW,uBAAuB,CAACC,UAAU,CAAC;IAC7CX,OAAO,EAAE8B,iBAAiB,CAACnB,UAAU,CAAC;IACtCb,MAAM,EAAEmC,gBAAgB,CAACtB,UAAU;EACrC,CAAC,CAAC;EACF,OAAO;IACLjB,OAAO;IACPG,QAAQ,EAAEU,OAAO,CAACV,QAAQ,CAAC;IAC3B0B,YAAY;IACZa,aAAa;IACbR,QAAQ,EAAEO,cAAc;IACxBjC,QAAQ,EAAEK,OAAO,CAACL,QAAQ;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}