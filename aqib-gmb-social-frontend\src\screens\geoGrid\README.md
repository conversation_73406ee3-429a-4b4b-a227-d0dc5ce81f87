# Google Geo Grid

A comprehensive location-based grid generation system for business analysis and location management.

## Features

### 🔍 Location Search
- **Search by Name**: Find locations using place names (e.g., "New York, NY")
- **Search by Coordinates**: Input exact latitude and longitude coordinates
- **Search by Map URL**: Extract location from Google Maps URLs

### 🗺️ Grid Generation
- **Configurable Grid Sizes**: 3x3, 4x4, 5x5, 6x6 grids
- **Distance Control**: Set distance between grid points
- **Multiple Units**: Support for meters, kilometers, and miles
- **Real-time Visualization**: Interactive map display with grid overlay

### ⚙️ Grid Settings
- **Automatic/Manual Mode**: Choose between automatic grid generation or manual point placement
- **Distance Configuration**: Customize spacing between grid points
- **Grid Size Selection**: Choose from predefined grid sizes
- **Quick Presets**: Pre-configured settings for common use cases

### 📅 Schedule Management
- **Schedule Check**: Enable/disable scheduling functionality
- **Future Enhancement**: Automated grid checking at specified intervals

### 💾 Configuration Management
- **Save Configurations**: Store grid setups for future use
- **Load Saved Grids**: Quickly restore previously saved configurations
- **Delete Configurations**: Remove unwanted saved setups
- **Configuration List**: View all saved configurations with details

## Components

### Main Screen
- **GeoGridScreen**: Main container component that orchestrates all functionality

### Child Components
- **GeoGridControls**: Search interface and configuration management
- **GeoGridMap**: Interactive map display with grid visualization
- **GeoGridSettings**: Grid configuration and settings panel
- **LocationMarkers**: Individual location markers with tooltips

## API Endpoints

### Backend Routes (`/geo-grid`)
- `POST /search-location` - Search for locations
- `POST /generate-grid` - Generate grid points
- `GET /grid-data/:gridId` - Retrieve grid data
- `POST /save-configuration` - Save grid configuration
- `GET /configurations/:userId` - Get user configurations
- `PUT /configuration/:gridId` - Update configuration
- `DELETE /configuration/:gridId` - Delete configuration
- `GET /location-suggestions` - Get location autocomplete suggestions
- `POST /validate-coordinates` - Validate coordinate inputs

## Database Schema

### Tables
1. **geo_grid_configurations**: Store grid configurations
2. **geo_grid_points**: Store individual grid points
3. **geo_grid_schedules**: Store scheduling information (future use)

## Usage

### Basic Workflow
1. **Search Location**: Use any of the three search methods to find your target location
2. **Configure Grid**: Adjust grid size, distance, and units in the settings panel
3. **Generate Grid**: The system automatically generates grid points around your location
4. **Save Configuration**: Store your setup for future use
5. **Manage Saved Grids**: Load, update, or delete saved configurations

### Search Methods

#### By Name
```typescript
{
  searchType: "name",
  query: "New York, NY"
}
```

#### By Coordinates
```typescript
{
  searchType: "coordinates",
  coordinates: {
    lat: 40.7128,
    lng: -74.0060
  }
}
```

#### By Map URL
```typescript
{
  searchType: "mapUrl",
  query: "https://maps.google.com/@40.7128,-74.0060,15z"
}
```

### Grid Configuration
```typescript
{
  name: "Downtown Analysis",
  centerLat: 40.7128,
  centerLng: -74.0060,
  gridSize: "3x3",
  distance: 500,
  distanceUnit: "meters",
  isScheduleEnabled: false
}
```

## Installation & Setup

### Frontend Dependencies
- React 18+
- Material-UI (MUI)
- TypeScript
- React Router

### Backend Dependencies
- Node.js
- Express.js
- MySQL
- Google APIs (for enhanced location services)

### Environment Variables
```env
# Google Maps API Key (for enhanced functionality)
GOOGLE_MAPS_API_KEY=your_api_key_here

# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
```

## Future Enhancements

### Planned Features
- **Real Google Maps Integration**: Replace static maps with interactive Google Maps
- **Advanced Scheduling**: Automated grid monitoring and alerts
- **Export Functionality**: Export grid data to CSV/Excel
- **Batch Operations**: Process multiple locations simultaneously
- **Analytics Dashboard**: Visualize grid performance metrics
- **Mobile Responsiveness**: Optimize for mobile devices

### Google Maps Integration
To enable full Google Maps functionality:
1. Obtain a Google Maps API key
2. Enable the following APIs:
   - Maps JavaScript API
   - Places API
   - Geocoding API
3. Replace static map URLs with interactive Google Maps components
4. Implement real-time location search and autocomplete

## Troubleshooting

### Common Issues
1. **Map not loading**: Check Google Maps API key configuration
2. **Location search failing**: Verify network connectivity and API endpoints
3. **Grid not generating**: Ensure valid center coordinates are provided
4. **Save functionality not working**: Check user authentication and database connectivity

### Error Handling
The system includes comprehensive error handling for:
- Invalid coordinates
- Network failures
- Database errors
- Authentication issues
- API rate limiting

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.
