{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { ListActionTypes } from './listActions.types';\nimport { ListContext } from './ListContext';\n\n/**\n * Contains the logic for an item of a list-like component (for example Select, Menu, etc.).\n * It handles the item's mouse events and tab index.\n *\n * @template ItemValue The type of the item's value. This should be consistent with the type of useList's `items` parameter.\n * @ignore - internal hook.\n */\nexport function useListItem(parameters) {\n  const {\n    handlePointerOverEvents = false,\n    item\n  } = parameters;\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('useListItem must be used within a ListProvider');\n  }\n  const {\n    dispatch,\n    getItemState\n  } = listContext;\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = getItemState(item);\n  const createHandleClick = React.useCallback(externalHandlers => event => {\n    var _externalHandlers$onC;\n    (_externalHandlers$onC = externalHandlers.onClick) == null || _externalHandlers$onC.call(externalHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (item === undefined) {\n        throw new Error(['MUI: The `item` provided to useListItem() is undefined.', 'This should happen only during server-side rendering under React 17.'].join('\\n'));\n      }\n    }\n    dispatch({\n      type: ListActionTypes.itemClick,\n      item: item,\n      event\n    });\n  }, [dispatch, item]);\n  const createHandlePointerOver = React.useCallback(externalHandlers => event => {\n    var _externalHandlers$onM;\n    (_externalHandlers$onM = externalHandlers.onMouseOver) == null || _externalHandlers$onM.call(externalHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (item === undefined) {\n        throw new Error(['MUI: The `item` provided to useListItem() is undefined.', 'This should happen only during server-side rendering under React 17.'].join('\\n'));\n      }\n    }\n    dispatch({\n      type: ListActionTypes.itemHover,\n      item: item,\n      event\n    });\n  }, [dispatch, item]);\n  let tabIndex;\n  if (focusable) {\n    tabIndex = highlighted ? 0 : -1;\n  }\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({}, externalProps, {\n      onClick: createHandleClick(externalEventHandlers),\n      onPointerOver: handlePointerOverEvents ? createHandlePointerOver(externalEventHandlers) : undefined,\n      tabIndex\n    });\n  };\n  return {\n    getRootProps,\n    highlighted,\n    selected\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "extractEventHandlers", "ListActionTypes", "ListContext", "useListItem", "parameters", "handlePointerOverEvents", "item", "listContext", "useContext", "Error", "dispatch", "getItemState", "highlighted", "selected", "focusable", "createHandleClick", "useCallback", "externalHandlers", "event", "_externalHandlers$onC", "onClick", "call", "defaultPrevented", "process", "env", "NODE_ENV", "undefined", "join", "type", "itemClick", "createHandlePointerOver", "_externalHandlers$onM", "onMouseOver", "itemHover", "tabIndex", "getRootProps", "externalProps", "externalEventHandlers", "onPointerOver"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/node_modules/@mui/base/useList/useListItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { ListActionTypes } from './listActions.types';\nimport { ListContext } from './ListContext';\n\n/**\n * Contains the logic for an item of a list-like component (for example Select, Menu, etc.).\n * It handles the item's mouse events and tab index.\n *\n * @template ItemValue The type of the item's value. This should be consistent with the type of useList's `items` parameter.\n * @ignore - internal hook.\n */\nexport function useListItem(parameters) {\n  const {\n    handlePointerOverEvents = false,\n    item\n  } = parameters;\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('useListItem must be used within a ListProvider');\n  }\n  const {\n    dispatch,\n    getItemState\n  } = listContext;\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = getItemState(item);\n  const createHandleClick = React.useCallback(externalHandlers => event => {\n    var _externalHandlers$onC;\n    (_externalHandlers$onC = externalHandlers.onClick) == null || _externalHandlers$onC.call(externalHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (item === undefined) {\n        throw new Error(['MUI: The `item` provided to useListItem() is undefined.', 'This should happen only during server-side rendering under React 17.'].join('\\n'));\n      }\n    }\n    dispatch({\n      type: ListActionTypes.itemClick,\n      item: item,\n      event\n    });\n  }, [dispatch, item]);\n  const createHandlePointerOver = React.useCallback(externalHandlers => event => {\n    var _externalHandlers$onM;\n    (_externalHandlers$onM = externalHandlers.onMouseOver) == null || _externalHandlers$onM.call(externalHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (item === undefined) {\n        throw new Error(['MUI: The `item` provided to useListItem() is undefined.', 'This should happen only during server-side rendering under React 17.'].join('\\n'));\n      }\n    }\n    dispatch({\n      type: ListActionTypes.itemHover,\n      item: item,\n      event\n    });\n  }, [dispatch, item]);\n  let tabIndex;\n  if (focusable) {\n    tabIndex = highlighted ? 0 : -1;\n  }\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({}, externalProps, {\n      onClick: createHandleClick(externalEventHandlers),\n      onPointerOver: handlePointerOverEvents ? createHandlePointerOver(externalEventHandlers) : undefined,\n      tabIndex\n    });\n  };\n  return {\n    getRootProps,\n    highlighted,\n    selected\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,QAAQ,eAAe;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,UAAU,EAAE;EACtC,MAAM;IACJC,uBAAuB,GAAG,KAAK;IAC/BC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,WAAW,GAAGR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;EACjD,IAAI,CAACK,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGJ,WAAW;EACf,MAAM;IACJK,WAAW;IACXC,QAAQ;IACRC;EACF,CAAC,GAAGH,YAAY,CAACL,IAAI,CAAC;EACtB,MAAMS,iBAAiB,GAAGhB,KAAK,CAACiB,WAAW,CAACC,gBAAgB,IAAIC,KAAK,IAAI;IACvE,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,gBAAgB,CAACG,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;IACjH,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAInB,IAAI,KAAKoB,SAAS,EAAE;QACtB,MAAM,IAAIjB,KAAK,CAAC,CAAC,yDAAyD,EAAE,sEAAsE,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;MACjK;IACF;IACAjB,QAAQ,CAAC;MACPkB,IAAI,EAAE3B,eAAe,CAAC4B,SAAS;MAC/BvB,IAAI,EAAEA,IAAI;MACVY;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EACpB,MAAMwB,uBAAuB,GAAG/B,KAAK,CAACiB,WAAW,CAACC,gBAAgB,IAAIC,KAAK,IAAI;IAC7E,IAAIa,qBAAqB;IACzB,CAACA,qBAAqB,GAAGd,gBAAgB,CAACe,WAAW,KAAK,IAAI,IAAID,qBAAqB,CAACV,IAAI,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;IACrH,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAInB,IAAI,KAAKoB,SAAS,EAAE;QACtB,MAAM,IAAIjB,KAAK,CAAC,CAAC,yDAAyD,EAAE,sEAAsE,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;MACjK;IACF;IACAjB,QAAQ,CAAC;MACPkB,IAAI,EAAE3B,eAAe,CAACgC,SAAS;MAC/B3B,IAAI,EAAEA,IAAI;MACVY;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACR,QAAQ,EAAEJ,IAAI,CAAC,CAAC;EACpB,IAAI4B,QAAQ;EACZ,IAAIpB,SAAS,EAAE;IACboB,QAAQ,GAAGtB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EACjC;EACA,MAAMuB,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,qBAAqB,GAAGrC,oBAAoB,CAACoC,aAAa,CAAC;IACjE,OAAOtC,QAAQ,CAAC,CAAC,CAAC,EAAEsC,aAAa,EAAE;MACjChB,OAAO,EAAEL,iBAAiB,CAACsB,qBAAqB,CAAC;MACjDC,aAAa,EAAEjC,uBAAuB,GAAGyB,uBAAuB,CAACO,qBAAqB,CAAC,GAAGX,SAAS;MACnGQ;IACF,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLC,YAAY;IACZvB,WAAW;IACXC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}