{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\NabiBaig\\\\Applications\\\\aqib-gmb-social-frontend\\\\src\\\\components\\\\geoGrid\\\\GeoGridControls.component.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, TextField, Button, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio, Divider, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Chip, InputAdornment } from \"@mui/material\";\nimport { Search as SearchIcon, LocationOn as LocationOnIcon, Delete as DeleteIcon, Download as DownloadIcon, Save as SaveIcon, Refresh as RefreshIcon } from \"@mui/icons-material\";\nimport { LoadingButton } from \"@mui/lab\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GeoGridControls = ({\n  onLocationSearch,\n  onGenerateGrid,\n  onSaveConfiguration,\n  loading,\n  currentLocation,\n  savedConfigurations,\n  onLoadConfiguration,\n  onDeleteConfiguration\n}) => {\n  _s();\n  const [searchType, setSearchType] = useState(\"name\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [coordinates, setCoordinates] = useState({\n    lat: \"\",\n    lng: \"\"\n  });\n  const [configName, setConfigName] = useState(\"\");\n  const handleSearch = () => {\n    if (searchType === \"name\" && !searchQuery.trim()) {\n      return;\n    }\n    if (searchType === \"coordinates\" && (!coordinates.lat || !coordinates.lng)) {\n      return;\n    }\n    if (searchType === \"mapUrl\" && !searchQuery.trim()) {\n      return;\n    }\n    const searchRequest = {\n      searchType,\n      query: searchQuery,\n      coordinates: searchType === \"coordinates\" ? {\n        lat: parseFloat(coordinates.lat),\n        lng: parseFloat(coordinates.lng)\n      } : undefined\n    };\n    onLocationSearch(searchRequest);\n  };\n  const handleKeyPress = event => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n  const renderSearchInput = () => {\n    switch (searchType) {\n      case \"name\":\n        return /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Search Location\",\n          placeholder: \"e.g., New York, NY\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          onKeyPress: handleKeyPress,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this);\n      case \"coordinates\":\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Latitude\",\n            placeholder: \"40.7128\",\n            value: coordinates.lat,\n            onChange: e => setCoordinates(prev => ({\n              ...prev,\n              lat: e.target.value\n            })),\n            type: \"number\",\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Longitude\",\n            placeholder: \"-74.0060\",\n            value: coordinates.lng,\n            onChange: e => setCoordinates(prev => ({\n              ...prev,\n              lng: e.target.value\n            })),\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this);\n      case \"mapUrl\":\n        return /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Google Maps URL\",\n          placeholder: \"https://maps.google.com/...\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          onKeyPress: handleKeyPress,\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Search Location\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      component: \"fieldset\",\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n        component: \"legend\",\n        children: \"Search Method\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        row: true,\n        value: searchType,\n        onChange: e => setSearchType(e.target.value),\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"name\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 51\n          }, this),\n          label: \"By Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"coordinates\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 22\n          }, this),\n          label: \"Coordinates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"mapUrl\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 22\n          }, this),\n          label: \"Map URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), renderSearchInput(), /*#__PURE__*/_jsxDEV(LoadingButton, {\n      fullWidth: true,\n      variant: \"contained\",\n      onClick: handleSearch,\n      loading: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 20\n      }, this),\n      sx: {\n        mb: 3\n      },\n      children: \"Search & Generate Grid\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), currentLocation && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(LocationOnIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 19\n        }, this),\n        label: `${currentLocation.name} (${currentLocation.lat.toFixed(4)}, ${currentLocation.lng.toFixed(4)})`,\n        color: \"primary\",\n        variant: \"outlined\",\n        sx: {\n          mb: 2,\n          maxWidth: \"100%\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          gap: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          onClick: onGenerateGrid,\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Regenerate\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Save Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n      fullWidth: true,\n      label: \"Configuration Name\",\n      placeholder: \"My Grid Configuration\",\n      value: configName,\n      onChange: e => setConfigName(e.target.value),\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n      fullWidth: true,\n      variant: \"contained\",\n      onClick: onSaveConfiguration,\n      loading: loading,\n      startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 20\n      }, this),\n      disabled: !configName.trim() || !currentLocation,\n      sx: {\n        mb: 3\n      },\n      children: \"Save Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Saved Configurations\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), savedConfigurations.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      children: \"No saved configurations yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: savedConfigurations.map(config => /*#__PURE__*/_jsxDEV(ListItem, {\n        divider: true,\n        children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: config.name,\n          secondary: `${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            onClick: () => onLoadConfiguration(config),\n            disabled: loading,\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"end\",\n            onClick: () => config.id && onDeleteConfiguration(config.id),\n            disabled: loading,\n            size: \"small\",\n            color: \"error\",\n            children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 15\n        }, this)]\n      }, config.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(GeoGridControls, \"1l0nFVWSFRu+XNBc7Wnvi9PFzkk=\");\n_c = GeoGridControls;\nexport default GeoGridControls;\nvar _c;\n$RefreshReg$(_c, \"GeoGridControls\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "<PERSON><PERSON>", "FormControl", "FormLabel", "RadioGroup", "FormControlLabel", "Radio", "Divider", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Chip", "InputAdornment", "Search", "SearchIcon", "LocationOn", "LocationOnIcon", "Delete", "DeleteIcon", "Download", "DownloadIcon", "Save", "SaveIcon", "Refresh", "RefreshIcon", "LoadingButton", "jsxDEV", "_jsxDEV", "GeoGridControls", "onLocationSearch", "onGenerateGrid", "onSaveConfiguration", "loading", "currentLocation", "savedConfigurations", "onLoadConfiguration", "onDeleteConfiguration", "_s", "searchType", "setSearchType", "searchQuery", "setSearch<PERSON>uery", "coordinates", "setCoordinates", "lat", "lng", "config<PERSON><PERSON>", "setConfigName", "handleSearch", "trim", "searchRequest", "query", "parseFloat", "undefined", "handleKeyPress", "event", "key", "renderSearchInput", "fullWidth", "label", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "InputProps", "startAdornment", "position", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "prev", "type", "variant", "gutterBottom", "component", "row", "control", "onClick", "startIcon", "icon", "name", "toFixed", "color", "max<PERSON><PERSON><PERSON>", "display", "gap", "size", "disabled", "my", "length", "dense", "map", "config", "divider", "primary", "secondary", "gridSize", "distance", "distanceUnit", "edge", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/NabiBaig/Applications/aqib-gmb-social-frontend/src/components/geoGrid/GeoGridControls.component.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport {\n  <PERSON>,\n  Typography,\n  TextField,\n  Button,\n  FormControl,\n  FormLabel,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Chip,\n  InputAdornment,\n  Autocomplete,\n  CircularProgress,\n} from \"@mui/material\";\nimport {\n  Search as SearchIcon,\n  LocationOn as LocationOnIcon,\n  Delete as DeleteIcon,\n  Download as DownloadIcon,\n  Save as SaveIcon,\n  Refresh as RefreshIcon,\n} from \"@mui/icons-material\";\nimport { LoadingButton } from \"@mui/lab\";\nimport {\n  LocationSearchRequest,\n  GridConfiguration,\n} from \"../../services/geoGrid/geoGrid.service\";\n\ninterface GeoGridControlsProps {\n  onLocationSearch: (searchRequest: LocationSearchRequest) => void;\n  onGenerateGrid: () => void;\n  onSaveConfiguration: () => void;\n  loading: boolean;\n  currentLocation: any;\n  savedConfigurations: GridConfiguration[];\n  onLoadConfiguration: (config: GridConfiguration) => void;\n  onDeleteConfiguration: (configId: number) => void;\n}\n\nconst GeoGridControls: React.FC<GeoGridControlsProps> = ({\n  onLocationSearch,\n  onGenerateGrid,\n  onSaveConfiguration,\n  loading,\n  currentLocation,\n  savedConfigurations,\n  onLoadConfiguration,\n  onDeleteConfiguration,\n}) => {\n  const [searchType, setSearchType] = useState<\n    \"name\" | \"coordinates\" | \"mapUrl\"\n  >(\"name\");\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [coordinates, setCoordinates] = useState({ lat: \"\", lng: \"\" });\n  const [configName, setConfigName] = useState(\"\");\n\n  const handleSearch = () => {\n    if (searchType === \"name\" && !searchQuery.trim()) {\n      return;\n    }\n\n    if (\n      searchType === \"coordinates\" &&\n      (!coordinates.lat || !coordinates.lng)\n    ) {\n      return;\n    }\n\n    if (searchType === \"mapUrl\" && !searchQuery.trim()) {\n      return;\n    }\n\n    const searchRequest: LocationSearchRequest = {\n      searchType,\n      query: searchQuery,\n      coordinates:\n        searchType === \"coordinates\"\n          ? {\n              lat: parseFloat(coordinates.lat),\n              lng: parseFloat(coordinates.lng),\n            }\n          : undefined,\n    };\n\n    onLocationSearch(searchRequest);\n  };\n\n  const handleKeyPress = (event: React.KeyboardEvent) => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n\n  const renderSearchInput = () => {\n    switch (searchType) {\n      case \"name\":\n        return (\n          <TextField\n            fullWidth\n            label=\"Search Location\"\n            placeholder=\"e.g., New York, NY\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={handleKeyPress}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n            }}\n            sx={{ mb: 2 }}\n          />\n        );\n\n      case \"coordinates\":\n        return (\n          <Box sx={{ mb: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Latitude\"\n              placeholder=\"40.7128\"\n              value={coordinates.lat}\n              onChange={(e) =>\n                setCoordinates((prev) => ({ ...prev, lat: e.target.value }))\n              }\n              type=\"number\"\n              sx={{ mb: 1 }}\n            />\n            <TextField\n              fullWidth\n              label=\"Longitude\"\n              placeholder=\"-74.0060\"\n              value={coordinates.lng}\n              onChange={(e) =>\n                setCoordinates((prev) => ({ ...prev, lng: e.target.value }))\n              }\n              type=\"number\"\n            />\n          </Box>\n        );\n\n      case \"mapUrl\":\n        return (\n          <TextField\n            fullWidth\n            label=\"Google Maps URL\"\n            placeholder=\"https://maps.google.com/...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyPress={handleKeyPress}\n            sx={{ mb: 2 }}\n          />\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom>\n        Search Location\n      </Typography>\n\n      <FormControl component=\"fieldset\" sx={{ mb: 2 }}>\n        <FormLabel component=\"legend\">Search Method</FormLabel>\n        <RadioGroup\n          row\n          value={searchType}\n          onChange={(e) => setSearchType(e.target.value as any)}\n        >\n          <FormControlLabel value=\"name\" control={<Radio />} label=\"By Name\" />\n          <FormControlLabel\n            value=\"coordinates\"\n            control={<Radio />}\n            label=\"Coordinates\"\n          />\n          <FormControlLabel\n            value=\"mapUrl\"\n            control={<Radio />}\n            label=\"Map URL\"\n          />\n        </RadioGroup>\n      </FormControl>\n\n      {renderSearchInput()}\n\n      <LoadingButton\n        fullWidth\n        variant=\"contained\"\n        onClick={handleSearch}\n        loading={loading}\n        startIcon={<SearchIcon />}\n        sx={{ mb: 3 }}\n      >\n        Search & Generate Grid\n      </LoadingButton>\n\n      {currentLocation && (\n        <Box sx={{ mb: 3 }}>\n          <Chip\n            icon={<LocationOnIcon />}\n            label={`${currentLocation.name} (${currentLocation.lat.toFixed(\n              4\n            )}, ${currentLocation.lng.toFixed(4)})`}\n            color=\"primary\"\n            variant=\"outlined\"\n            sx={{ mb: 2, maxWidth: \"100%\" }}\n          />\n\n          <Box sx={{ display: \"flex\", gap: 1 }}>\n            <Button\n              variant=\"outlined\"\n              size=\"small\"\n              onClick={onGenerateGrid}\n              startIcon={<RefreshIcon />}\n              disabled={loading}\n            >\n              Regenerate\n            </Button>\n          </Box>\n        </Box>\n      )}\n\n      <Divider sx={{ my: 2 }} />\n\n      <Typography variant=\"h6\" gutterBottom>\n        Save Configuration\n      </Typography>\n\n      <TextField\n        fullWidth\n        label=\"Configuration Name\"\n        placeholder=\"My Grid Configuration\"\n        value={configName}\n        onChange={(e) => setConfigName(e.target.value)}\n        sx={{ mb: 2 }}\n      />\n\n      <LoadingButton\n        fullWidth\n        variant=\"contained\"\n        onClick={onSaveConfiguration}\n        loading={loading}\n        startIcon={<SaveIcon />}\n        disabled={!configName.trim() || !currentLocation}\n        sx={{ mb: 3 }}\n      >\n        Save Configuration\n      </LoadingButton>\n\n      <Divider sx={{ my: 2 }} />\n\n      <Typography variant=\"h6\" gutterBottom>\n        Saved Configurations\n      </Typography>\n\n      {savedConfigurations.length === 0 ? (\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          No saved configurations yet\n        </Typography>\n      ) : (\n        <List dense>\n          {savedConfigurations.map((config) => (\n            <ListItem key={config.id} divider>\n              <ListItemText\n                primary={config.name}\n                secondary={`${config.gridSize} grid • ${config.distance} ${config.distanceUnit}`}\n              />\n              <ListItemSecondaryAction>\n                <IconButton\n                  edge=\"end\"\n                  onClick={() => onLoadConfiguration(config)}\n                  disabled={loading}\n                  size=\"small\"\n                >\n                  <DownloadIcon />\n                </IconButton>\n                <IconButton\n                  edge=\"end\"\n                  onClick={() => config.id && onDeleteConfiguration(config.id)}\n                  disabled={loading}\n                  size=\"small\"\n                  color=\"error\"\n                >\n                  <DeleteIcon />\n                </IconButton>\n              </ListItemSecondaryAction>\n            </ListItem>\n          ))}\n        </List>\n      )}\n    </Box>\n  );\n};\n\nexport default GeoGridControls;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAA2B,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,UAAU,EACVC,IAAI,EACJC,cAAc,QAGT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,aAAa,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBzC,MAAMC,eAA+C,GAAGA,CAAC;EACvDC,gBAAgB;EAChBC,cAAc;EACdC,mBAAmB;EACnBC,OAAO;EACPC,eAAe;EACfC,mBAAmB;EACnBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAE1C,MAAM,CAAC;EACT,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC;IAAEiD,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC;EACpE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMqD,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIV,UAAU,KAAK,MAAM,IAAI,CAACE,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MAChD;IACF;IAEA,IACEX,UAAU,KAAK,aAAa,KAC3B,CAACI,WAAW,CAACE,GAAG,IAAI,CAACF,WAAW,CAACG,GAAG,CAAC,EACtC;MACA;IACF;IAEA,IAAIP,UAAU,KAAK,QAAQ,IAAI,CAACE,WAAW,CAACS,IAAI,CAAC,CAAC,EAAE;MAClD;IACF;IAEA,MAAMC,aAAoC,GAAG;MAC3CZ,UAAU;MACVa,KAAK,EAAEX,WAAW;MAClBE,WAAW,EACTJ,UAAU,KAAK,aAAa,GACxB;QACEM,GAAG,EAAEQ,UAAU,CAACV,WAAW,CAACE,GAAG,CAAC;QAChCC,GAAG,EAAEO,UAAU,CAACV,WAAW,CAACG,GAAG;MACjC,CAAC,GACDQ;IACR,CAAC;IAEDxB,gBAAgB,CAACqB,aAAa,CAAC;EACjC,CAAC;EAED,MAAMI,cAAc,GAAIC,KAA0B,IAAK;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBR,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQnB,UAAU;MAChB,KAAK,MAAM;QACT,oBACEX,OAAA,CAAC7B,SAAS;UACR4D,SAAS;UACTC,KAAK,EAAC,iBAAiB;UACvBC,WAAW,EAAC,oBAAoB;UAChCC,KAAK,EAAErB,WAAY;UACnBsB,QAAQ,EAAGC,CAAC,IAAKtB,cAAc,CAACsB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDI,UAAU,EAAEX,cAAe;UAC3BY,UAAU,EAAE;YACVC,cAAc,eACZxC,OAAA,CAACf,cAAc;cAACwD,QAAQ,EAAC,OAAO;cAAAC,QAAA,eAC9B1C,OAAA,CAACb,UAAU;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAGN,KAAK,aAAa;QAChB,oBACE9C,OAAA,CAAC/B,GAAG;UAAC8E,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACjB1C,OAAA,CAAC7B,SAAS;YACR4D,SAAS;YACTC,KAAK,EAAC,UAAU;YAChBC,WAAW,EAAC,SAAS;YACrBC,KAAK,EAAEnB,WAAW,CAACE,GAAI;YACvBkB,QAAQ,EAAGC,CAAC,IACVpB,cAAc,CAAEiC,IAAI,KAAM;cAAE,GAAGA,IAAI;cAAEhC,GAAG,EAAEmB,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAC5D;YACDgB,IAAI,EAAC,QAAQ;YACbH,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF9C,OAAA,CAAC7B,SAAS;YACR4D,SAAS;YACTC,KAAK,EAAC,WAAW;YACjBC,WAAW,EAAC,UAAU;YACtBC,KAAK,EAAEnB,WAAW,CAACG,GAAI;YACvBiB,QAAQ,EAAGC,CAAC,IACVpB,cAAc,CAAEiC,IAAI,KAAM;cAAE,GAAGA,IAAI;cAAE/B,GAAG,EAAEkB,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAC5D;YACDgB,IAAI,EAAC;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAGV,KAAK,QAAQ;QACX,oBACE9C,OAAA,CAAC7B,SAAS;UACR4D,SAAS;UACTC,KAAK,EAAC,iBAAiB;UACvBC,WAAW,EAAC,6BAA6B;UACzCC,KAAK,EAAErB,WAAY;UACnBsB,QAAQ,EAAGC,CAAC,IAAKtB,cAAc,CAACsB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAChDI,UAAU,EAAEX,cAAe;UAC3BoB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAGN;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE9C,OAAA,CAAC/B,GAAG;IAAAyE,QAAA,gBACF1C,OAAA,CAAC9B,UAAU;MAACiF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb9C,OAAA,CAAC3B,WAAW;MAACgF,SAAS,EAAC,UAAU;MAACN,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAC9C1C,OAAA,CAAC1B,SAAS;QAAC+E,SAAS,EAAC,QAAQ;QAAAX,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACvD9C,OAAA,CAACzB,UAAU;QACT+E,GAAG;QACHpB,KAAK,EAAEvB,UAAW;QAClBwB,QAAQ,EAAGC,CAAC,IAAKxB,aAAa,CAACwB,CAAC,CAACC,MAAM,CAACH,KAAY,CAAE;QAAAQ,QAAA,gBAEtD1C,OAAA,CAACxB,gBAAgB;UAAC0D,KAAK,EAAC,MAAM;UAACqB,OAAO,eAAEvD,OAAA,CAACvB,KAAK;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACd,KAAK,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrE9C,OAAA,CAACxB,gBAAgB;UACf0D,KAAK,EAAC,aAAa;UACnBqB,OAAO,eAAEvD,OAAA,CAACvB,KAAK;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBd,KAAK,EAAC;QAAa;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACF9C,OAAA,CAACxB,gBAAgB;UACf0D,KAAK,EAAC,QAAQ;UACdqB,OAAO,eAAEvD,OAAA,CAACvB,KAAK;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBd,KAAK,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEbhB,iBAAiB,CAAC,CAAC,eAEpB9B,OAAA,CAACF,aAAa;MACZiC,SAAS;MACToB,OAAO,EAAC,WAAW;MACnBK,OAAO,EAAEnC,YAAa;MACtBhB,OAAO,EAAEA,OAAQ;MACjBoD,SAAS,eAAEzD,OAAA,CAACb,UAAU;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,EACf;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,EAEfxC,eAAe,iBACdN,OAAA,CAAC/B,GAAG;MAAC8E,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACjB1C,OAAA,CAAChB,IAAI;QACH0E,IAAI,eAAE1D,OAAA,CAACX,cAAc;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBd,KAAK,EAAE,GAAG1B,eAAe,CAACqD,IAAI,KAAKrD,eAAe,CAACW,GAAG,CAAC2C,OAAO,CAC5D,CACF,CAAC,KAAKtD,eAAe,CAACY,GAAG,CAAC0C,OAAO,CAAC,CAAC,CAAC,GAAI;QACxCC,KAAK,EAAC,SAAS;QACfV,OAAO,EAAC,UAAU;QAClBJ,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEc,QAAQ,EAAE;QAAO;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEF9C,OAAA,CAAC/B,GAAG;QAAC8E,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAtB,QAAA,eACnC1C,OAAA,CAAC5B,MAAM;UACL+E,OAAO,EAAC,UAAU;UAClBc,IAAI,EAAC,OAAO;UACZT,OAAO,EAAErD,cAAe;UACxBsD,SAAS,eAAEzD,OAAA,CAACH,WAAW;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BoB,QAAQ,EAAE7D,OAAQ;UAAAqC,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9C,OAAA,CAACtB,OAAO;MAACqE,EAAE,EAAE;QAAEoB,EAAE,EAAE;MAAE;IAAE;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B9C,OAAA,CAAC9B,UAAU;MAACiF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb9C,OAAA,CAAC7B,SAAS;MACR4D,SAAS;MACTC,KAAK,EAAC,oBAAoB;MAC1BC,WAAW,EAAC,uBAAuB;MACnCC,KAAK,EAAEf,UAAW;MAClBgB,QAAQ,EAAGC,CAAC,IAAKhB,aAAa,CAACgB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;MAC/Ca,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEF9C,OAAA,CAACF,aAAa;MACZiC,SAAS;MACToB,OAAO,EAAC,WAAW;MACnBK,OAAO,EAAEpD,mBAAoB;MAC7BC,OAAO,EAAEA,OAAQ;MACjBoD,SAAS,eAAEzD,OAAA,CAACL,QAAQ;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBoB,QAAQ,EAAE,CAAC/C,UAAU,CAACG,IAAI,CAAC,CAAC,IAAI,CAAChB,eAAgB;MACjDyC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,EACf;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAEhB9C,OAAA,CAACtB,OAAO;MAACqE,EAAE,EAAE;QAAEoB,EAAE,EAAE;MAAE;IAAE;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1B9C,OAAA,CAAC9B,UAAU;MAACiF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAV,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZvC,mBAAmB,CAAC6D,MAAM,KAAK,CAAC,gBAC/BpE,OAAA,CAAC9B,UAAU;MAACiF,OAAO,EAAC,OAAO;MAACU,KAAK,EAAC,gBAAgB;MAAAnB,QAAA,EAAC;IAEnD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEb9C,OAAA,CAACrB,IAAI;MAAC0F,KAAK;MAAA3B,QAAA,EACRnC,mBAAmB,CAAC+D,GAAG,CAAEC,MAAM,iBAC9BvE,OAAA,CAACpB,QAAQ;QAAiB4F,OAAO;QAAA9B,QAAA,gBAC/B1C,OAAA,CAACnB,YAAY;UACX4F,OAAO,EAAEF,MAAM,CAACZ,IAAK;UACrBe,SAAS,EAAE,GAAGH,MAAM,CAACI,QAAQ,WAAWJ,MAAM,CAACK,QAAQ,IAAIL,MAAM,CAACM,YAAY;QAAG;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC,eACF9C,OAAA,CAAClB,uBAAuB;UAAA4D,QAAA,gBACtB1C,OAAA,CAACjB,UAAU;YACT+F,IAAI,EAAC,KAAK;YACVtB,OAAO,EAAEA,CAAA,KAAMhD,mBAAmB,CAAC+D,MAAM,CAAE;YAC3CL,QAAQ,EAAE7D,OAAQ;YAClB4D,IAAI,EAAC,OAAO;YAAAvB,QAAA,eAEZ1C,OAAA,CAACP,YAAY;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACb9C,OAAA,CAACjB,UAAU;YACT+F,IAAI,EAAC,KAAK;YACVtB,OAAO,EAAEA,CAAA,KAAMe,MAAM,CAACQ,EAAE,IAAItE,qBAAqB,CAAC8D,MAAM,CAACQ,EAAE,CAAE;YAC7Db,QAAQ,EAAE7D,OAAQ;YAClB4D,IAAI,EAAC,OAAO;YACZJ,KAAK,EAAC,OAAO;YAAAnB,QAAA,eAEb1C,OAAA,CAACT,UAAU;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA,GAvBbyB,MAAM,CAACQ,EAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBd,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpC,EAAA,CAjQIT,eAA+C;AAAA+E,EAAA,GAA/C/E,eAA+C;AAmQrD,eAAeA,eAAe;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}