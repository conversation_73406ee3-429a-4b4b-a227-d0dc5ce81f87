//TOKEN
export const directToken = localStorage.getItem("access_token");
// E<PERSON> POINTS FOR LOGIN IN
export const LOGIN = "auth/login";

//END POINTS FOR PERFORMNANCE
export const GET_PERFORMANCE = "performance/performance-locationMetrics";

// END POINTS FOR USER MANAGEMENT
export const LIST_OF_USERS = "user/user-list";
export const CREATE_USER = "user/create-user";
export const EDIT_USER = "user/edit-user/";
export const EDIT_USERLOCATIONS = "user/edit-userlocation";
export const DELETE_USER = "user/delete-user";
export const ASSIGN_USER = "user/assign-user";
export const GET_ASSIGNUSER = "user/user-locationlist";
export const ENABLE_DISABLE_USER = "user/enable-disable";
export const UPDATE_PASSWORD_USER = "user/update-password";
export const UPDATE_USER_COMBINED = "user/update-user-combined";

// E<PERSON> POINTS FOR BUSINESS MANAGEMENT
export const LIST_OF_BUSINESS = "business/business-list";
export const LIST_OF_BUSINESS_PAGINATED = "business/business-list-paginated";
export const CREATE_BUSINESS = "business/add-business";
export const EDIT_BUSINESS = "business/edit-business";
export const DELETE_BUSINESS = "business/delete-business";
export const ENABLE_DISABLE_BUSINESS = "business/enable-disable";

// END POINTS FOR ROLE MANAGEMENT
export const LIST_OF_ROLE = "role/role-list";
export const USER_ROLES = (userId: number) => `role/user-role/${userId}`;
export const UPDATE_ROLE = `role/update-role`;

// END POINTS FOR LOCATION MANAGEMENT
export const LIST_OF_LOCATIONS = "locations/locations-list";
export const REFRESH_LOCATIONS = "locations/locations-list-refresh";
export const ACCOUNTS_COUNT = "locations/accounts-count";
export const LOCATION_SUMMARY = "locations/location-summary";
export const IMAGE_PROXY = (url: string) => `locations/image-proxy?url=${url}`;

// END POINTS FOR REVIEW MANAGEMENT
export const LIST_OF_REVIEWS = (userId: number) =>
  `gmb-reviews/reviews-list/${userId}`;

export const LIST_OF_REVIEWS_EXTENDED = (userId: number) =>
  `gmb-reviews/extended-reviews-list/${userId}`;
export const REFRESH_REVIEWS = "gmb-reviews/reviews-list";
export const REPLY_TO_REVIEW = "gmb-reviews/reviews-comment";
export const REPLY_WITH_AI = "gmb-reviews/reply-using-ai";
export const CREATE_REVIEW_TAGS = "gmb-reviews/create-review-tags";
export const GET_ALL_TAGS = "gmb-reviews/get-all-tags";
export const UPDATE_TAGS_TO_REVIEW = "gmb-reviews/review-update-tags";

// END POINTS FOR REVIEW MANAGEMENT
export const LIST_OF_QANDA = (userId: number) =>
  `gmb-QandA/QandA-list/${userId}`;
export const REFRESH_QandA = "gmb-QandA/QandA-list";
export const REPLY_TO_QandA = "gmb-QandA/reply";

// END POINTS FOR GMB SERVICES
export const GOOGLE_AUTHENTICATION = "gmb/authenticate";
export const GMB_ACCOUNTS = "gmb/gmb-accounts";
export const GMB_CALLBACK = "gmb/callback";

// END POINTS FOR POSTS SERVICES
export const RETRIEVE_POSTS = "post/get-google-posts-location";
export const UPLOAD_IMAGE_FILES = "post/upload-image-local";
export const CREATE_POST = "post/create-post";
export const DELETE_POST = "post/delete-post";

export const SAVE_SCHEDULED = "post/save-scheduled";

// END POINTS LOCATION METRICS
export const PERFORMANCE_LOCATIONMETRICS =
  "performance/performance-locationMetrics";

export const PERFORMANCE_SEARCHKEYWORDS = "performance/search-keywords";

// END POINTS FOR GEO GRID SERVICES
export const GEO_GRID_SEARCH_LOCATION = "geo-grid/search-location";
export const GEO_GRID_GENERATE_GRID = "geo-grid/generate-grid";
export const GEO_GRID_GET_DATA = (gridId: string) =>
  `geo-grid/grid-data/${gridId}`;
export const GEO_GRID_SAVE_CONFIG = "geo-grid/save-configuration";
export const GEO_GRID_GET_CONFIGS = (userId: number) =>
  `geo-grid/configurations/${userId}`;
export const GEO_GRID_UPDATE_CONFIG = (gridId: string) =>
  `geo-grid/configuration/${gridId}`;
export const GEO_GRID_DELETE_CONFIG = (gridId: string) =>
  `geo-grid/configuration/${gridId}`;
export const GEO_GRID_LOCATION_SUGGESTIONS = "geo-grid/location-suggestions";
export const GEO_GRID_VALIDATE_COORDINATES = "geo-grid/validate-coordinates";
