import { useContext, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import UserAvatarWithName from "../userAvatarWIthName/userAvatarWIthName.component";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import moment from "moment";
import ReviewService from "../../services/review/review.service";
import { LoadingContext } from "../../context/loading.context";
import { IQuestionAnswer } from "../../interfaces/response/IQuestionAnswersResponseModel";
import { Divider } from "@mui/material";
import ReplyTwoToneIcon from "@mui/icons-material/ReplyTwoTone";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useDispatch } from "react-redux";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";
import QandAService from "../../services/qanda/qanda.service";

const ReplyQuestionAnswerComponent = (props: {
  questionAnswer: IQuestionAnswer;
  closeDrawer: () => null | void | undefined;
}) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const _applicationHelperService = new ApplicationHelperService({});
  const _qandAService = new QandAService(dispatch);
  const [reviewReply, setReviewReply] = useState<string>("");
  const { setToastConfig, setOpen } = useContext(ToastContext);

  const reply = async () => {
    try {
      setLoading(true);
      const headerData = {
        "x-gmb-question-id": props.questionAnswer.gmbQuestionId,
        "x-gmb-location-id": props.questionAnswer.gmbLocationId,
        "x-gmb-account-id": props.questionAnswer.gmbAccountId,
      };
      var response = await _qandAService.replyQandA(headerData, {
        answer: reviewReply,
      });
    } catch (error: any) {
      if (error?.response?.data?.error) {
        setToastConfig(ToastSeverity.Error, error?.response?.data?.error, true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.ApiErrorStandardMessage,
          true
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box className="commonModal">
      <Typography
        id="modal-modal-title"
        variant="h6"
        component="h2"
        className="modal-modal-title"
      >
        Reply To Query
      </Typography>

      <Box id="modal-modal-description" className="modal-modal-description">
        <Box>
          <Box>
            <Typography>
              {_applicationHelperService.getFormatedDate(
                moment(
                  props.questionAnswer.questionCreatedTime,
                  "YYYY-MM-DD"
                ).toDate(),
                "MMM DD, YYYY"
              )}
            </Typography>
            <Box>
              <UserAvatarWithName fullname={props.questionAnswer.userName} />
            </Box>
          </Box>
        </Box>
        <Box>
          <Grid container>
            <Grid item xs={12}>
              <Typography fontWeight={"bold"}>Question :</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography>{props.questionAnswer.question}</Typography>
            </Grid>
          </Grid>
          <Divider style={{ margin: 10, height: 5 }} />
          <Grid container>
            <Grid item xs={6}>
              <Typography>Reply :</Typography>
            </Grid>
          </Grid>
          <Box>
            <TextField
              value={reviewReply}
              label=""
              multiline
              rows={4} // Number of visible rows
              variant="outlined"
              fullWidth
              placeholder="Type your answer here..."
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                setReviewReply(event.target.value)
              }
            />
          </Box>
        </Box>
      </Box>

      <Box className="">
        <Stack direction="row" className="commonFooter">
          <Button
            onClick={props.closeDrawer}
            variant="outlined"
            className="secondaryOutlineBtn"
            startIcon={<CancelOutlinedIcon />}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            className="primaryFillBtn"
            startIcon={<ReplyTwoToneIcon />}
            onClick={() => reply()}
          >
            Reply
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default ReplyQuestionAnswerComponent;
