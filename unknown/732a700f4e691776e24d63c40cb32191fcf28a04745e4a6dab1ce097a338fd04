import { FunctionComponent, ReactNode } from "react";
import Box from "@mui/material/Box";
import CssBaseline from "@mui/material/CssBaseline";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import InboxIcon from "@mui/icons-material/MoveToInbox";

interface Props {
  title: string;
  icon?: ReactNode;
  navigateTo: string;
  open: boolean;
  // any props that come into the component
}

const MenuListItemComponent: FunctionComponent<Props> = ({
  title,
  icon,
  navigateTo,
  open,
}) => {
  return (
    <div>
      <Box sx={{ display: "flex" }}>
        <CssBaseline />

        <ListItem key={title} disablePadding sx={{ display: "block" }}>
          <ListItemButton
            sx={[
              {
                minHeight: 48,
                px: 2.5,
              },
              open
                ? {
                    justifyContent: "initial",
                  }
                : {
                    justifyContent: "center",
                  },
            ]}
          >
            <ListItemIcon
              sx={[
                {
                  minWidth: 0,
                  justifyContent: "center",
                },
                open
                  ? {
                      mr: 3,
                    }
                  : {
                      mr: "auto",
                    },
              ]}
            >
              {<InboxIcon />}
            </ListItemIcon>
            <ListItemText
              primary={title}
              sx={[
                open
                  ? {
                      opacity: 1,
                    }
                  : {
                      opacity: 0,
                    },
              ]}
            />
          </ListItemButton>
        </ListItem>
      </Box>
    </div>
  );
};

export default MenuListItemComponent;
