import Avatar from "@mui/material/Avatar";
import { useEffect, useState } from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

const IconOnAvailability = (props: { isAvailable: boolean }) => {
  return props.isAvailable ? (
    <CheckCircleIcon
      sx={{
        color: "var(--positive)",
        fontSize: 36,
      }}
    />
  ) : (
    <CancelOutlinedIcon
      sx={{
        color: "var(--negative)",
        fontSize: 36,
      }}
    />
  );
};

export default IconOnAvailability;
