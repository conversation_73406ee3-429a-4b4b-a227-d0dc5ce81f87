import { FunctionComponent, useContext } from "react";
import { isValidElement, cloneElement } from "react";

//Widgets
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { Box, Collapse, Tooltip } from "@mui/material";
// import Accordion from '@material-ui/core/Accordion';
// import AccordionSummary from '@material-ui/core/AccordionSummary';
// import AccordionDetails from '@material-ui/core/AccordionDetails';
// import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { NestedMenuItems } from "../../interfaces/nestedMenuItems";
import { useLocation, useNavigate } from "react-router-dom";
import { PreferencesContext } from "../../context/preferences.context";
import React from "react";
import { SvgIconProps } from "@mui/material/SvgIcon";

const MenuListItemNestedComponent: FunctionComponent<{
  props: NestedMenuItems;
}> = ({ props }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { setActiveMenuItem, activeMenuItem } = useContext(PreferencesContext);
  // console.log(location.pathname);
  // console.log(props);

  return (
    <div>
      <ListItemButton
        className={location.pathname.includes(props.id) ? "selectedMenu" : ""}
        key={props.title + "0"}
        sx={[
          {
            minHeight: 48,
            px: 2.5,
          },
          props.open
            ? {
                justifyContent: "initial",
              }
            : {
                justifyContent: "center",
              },
        ]}
        onClick={() => {
          props.onToggle?.(); // <-- collapse/expand logic
          if (props.navigateTo) {
            setActiveMenuItem(props.navigateTo);
            navigate(props.navigateTo);
          }
        }}
      >
        <Tooltip title={!props.open ? props.title : ""} placement="right">
          <ListItemIcon
            sx={{
              minWidth: 0,
              justifyContent: "center",
              mr: props.open ? 3 : "auto",
            }}
            className={
              location.pathname.includes(props.id) ? "selectedIcon" : ""
            }
          >
            {cloneElement(props.icon as React.ReactElement<SvgIconProps>, {
              sx: {
                color: location.pathname.includes(props.id)
                  ? "var(--secondaryColor)"
                  : "inherir",
              },
            })}
          </ListItemIcon>
        </Tooltip>

        <ListItemText
          sx={{ opacity: props.open ? 1 : 0, transition: "opacity 0.3s" }}
          primary={props.title}
          primaryTypographyProps={{ fontWeight: 600 }}
        />
        {props.nested != null && props.nested.length > 0 && props.open ? (
          props.isToggled ? (
            <ExpandLess />
          ) : (
            <ExpandMore />
          )
        ) : (
          <></>
        )}
      </ListItemButton>
      {props.nested != null &&
        props.nested.length > 0 &&
        props.nested.map(
          (nestedMenu: NestedMenuItems, index: number) =>
            nestedMenu.isAccessible && (
              <Collapse
                in={props.isToggled}
                timeout="auto"
                unmountOnExit
                key={nestedMenu.title + "Collapse" + index}
              >
                <List
                  component="div"
                  disablePadding
                  key={nestedMenu.title + "CollapseDiv" + index}
                >
                  <ListItemButton
                    sx={{ pl: 4 }}
                    onClick={() => {
                      if (nestedMenu.navigateTo) {
                        setActiveMenuItem(nestedMenu.navigateTo);
                        navigate(nestedMenu.navigateTo);
                      }
                    }}
                    // className={
                    //   nestedMenu.navigateTo === location.pathname
                    //     ? "selectedMenu"
                    //     : ""
                    // }
                  >
                    {nestedMenu.icon && (
                      <Tooltip
                        title={!props.open ? props.title : ""}
                        placement="right"
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 0,
                            justifyContent: "center",
                            mr: props.open ? 3 : "auto",
                          }}
                        >
                          {cloneElement(
                            nestedMenu.icon as React.ReactElement<SvgIconProps>,
                            {
                              sx: {
                                color: location.pathname.includes(
                                  nestedMenu.navigateTo
                                )
                                  ? "var(--secondaryColor)"
                                  : "inherit",
                              },
                            }
                          )}
                        </ListItemIcon>
                      </Tooltip>
                    )}

                    <ListItemText
                      sx={{
                        opacity: props.open ? 1 : 0,
                        transition: "opacity 0.3s",
                        color: location.pathname.includes(nestedMenu.navigateTo)
                          ? "var(--secondaryColor)"
                          : "inherit",
                      }}
                      primaryTypographyProps={{ fontWeight: 600 }}
                      primary={nestedMenu.title}
                    />
                  </ListItemButton>
                </List>
              </Collapse>
            )
        )}
    </div>
  );
};

export default MenuListItemNestedComponent;
