import React from "react";
import { Box, Typography, Avatar } from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import StarIcon from "@mui/icons-material/Star";
import UserAvatar from "../../../../userAvatar/userAvatar.component";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";

const TestimonialCard2 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      ref={props.divRef}
      sx={{
        height: "100%",
        width: "100%",
        padding: 3,
        borderRadius: 3,
        boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
        background: `${props.templateConfig.backgroundColor}`,
        textAlign: "center",
        margin: "auto",
      }}
    >
      {/* Stars */}

      {props.templateConfig.showRating && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            mb: 2,
          }}
        >
          <RatingsStar starRating={props.templateConfig.starRating} />
        </Box>
      )}

      {/* Testimonial Text */}
      <Typography
        sx={{
          fontSize: "0.9rem",
          mb: 3,
          lineHeight: 1.6,
          color: `${props.templateConfig.fontColor}`,
        }}
      >
        {props.templateConfig.comment}
      </Typography>

      {/* Avatar */}
      {props.templateConfig.showAvatar && (
        <UserAvatar
          profileImage={props.templateConfig.reviewerImage}
          fullname={props.templateConfig.reviewerName}
          style={{
            width: 60,
            height: 60,
            margin: "0 auto 10px",
            background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
          }}
        />
      )}

      {/* Name and Designation */}
      <Typography
        sx={{
          fontWeight: 600,
          fontSize: "1rem",
          color: `${props.templateConfig.fontColor}`,
        }}
      >
        {props.templateConfig.reviewerName}
      </Typography>
    </Box>
  );
};

export default TestimonialCard2;
