import React from "react";
import {
  <PERSON>,
  CardContent,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const ServiceItemsDisplay = (props: { serviceItems: any }) => {
  const formatServiceType = (id: string) => {
    const raw = id.split(":")[1] || "";
    return (
      raw &&
      raw
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  };

  return (
    <Card variant="outlined" sx={{ p: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Service Items
        </Typography>
        <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
          {props.serviceItems &&
            props.serviceItems.map((item: any, idx: number) => {
              let formatted = "";
              if (item.structuredServiceItem) {
                formatted = formatServiceType(
                  item.structuredServiceItem.serviceTypeId
                );
                return <Chip key={idx} label={formatted} variant="outlined" />;
              }

              if (item.freeFormServiceItem) {
                formatted = item.freeFormServiceItem.label.displayName;
                return (
                  <Accordion expanded className="commonBorderCard">
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>{formatted}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: "8px",
                        }}
                      >
                        <Typography>
                          {item.freeFormServiceItem.label.description}
                        </Typography>
                      </div>
                    </AccordionDetails>
                  </Accordion>
                );
              }
            })}
        </div>
      </CardContent>
    </Card>
  );
};

export default ServiceItemsDisplay;
