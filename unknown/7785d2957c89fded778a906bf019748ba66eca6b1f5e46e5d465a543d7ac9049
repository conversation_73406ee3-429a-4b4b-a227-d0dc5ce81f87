import Avatar from "@mui/material/Avatar";
import React, { useEffect, useState } from "react";
import { Box, Typography, Grid, Button } from "@mui/material";
import Grid2 from "@mui/material/Grid2";
import { IPostTemplateConfig } from "../../types/IPostTemplateConfig";

var deepEqual = require("deep-equal");

const solidColors = [
  "#B2C8C0",
  "#004E92",
  "#607D8B",
  "#1E88E5",
  "#F44336",
  "#E91E63",
  "#00BCD4",
  "#FB8C00",
  "#673AB7",
  "#37474F",
  "#00ACC1",
  "#FF5252",
  "#5D4037",
  "#FFD740",
  "#8E24AA",
  "#D50000",
  "#F3F781",
  "#009688",
  "#8E24AA",
  "#43A047",
  "#E1BEE7",
  "#F3E5F5",
  "#B2EBF2",
];

const gradientColors = [
  "linear-gradient(to right, #12C2E9, #C471ED)", // Aurora purple
  "linear-gradient(to right, #F7971E, #FFD200)", // Golden sunshine
  "linear-gradient(to right, #8E2DE2, #4A00E0)", // Rich purple depth
  "linear-gradient(to right, #FFE000, #799F0C)", // Lime splash
  "linear-gradient(to right, #1FA2FF, #12D8FA)", // Fresh blue blend
  "linear-gradient(to right, #FF5F6D, #FFC371)", // Tropical sunrise
  "linear-gradient(to right, #8e2de2, #4a00e0)",
  "linear-gradient(to right, #ff512f, #dd2476)",
  "linear-gradient(to right, #00c9ff, #92fe9d)",
  "linear-gradient(to right, #f12711, #f5af19)",
  "linear-gradient(to right, #396afc, #2948ff)",
  "linear-gradient(to right, #00b09b, #96c93d)",
  "linear-gradient(to right, #7f00ff, #e100ff)",
  "linear-gradient(to right, #4e54c8, #8f94fb)",
  "linear-gradient(to right, #ff9a9e, #fad0c4)",
  "linear-gradient(to right, #d4fc79, #96e6a1)",
  "linear-gradient(to right, #e8cbc0, #636fa4)",
  "linear-gradient(to right, #00c6ff, #0072ff)",
  "linear-gradient(to right, #11998e, #38ef7d)",
  "linear-gradient(to right, #f7971e, #ffd200)",
  "linear-gradient(to right, #DA4453, #89216B)", // Velvet red
  "linear-gradient(to right, #6EE7B7, #3B82F6)", // Aqua calm
  "linear-gradient(to right, #FF4E50, #F9D423)", // Vibrant sunrise
  "linear-gradient(to right, #C33764, #1D2671)", // Deep raspberry
];

const gradientColorsDiagonal = [
  "linear-gradient(45deg, #FF5733, #FFC300, #DAF7A6)" /* Vibrant Sunset */,
  "linear-gradient(45deg, #6A11CB, #2575FC, #A7F3D0)" /* Purple-Blue-Green */,
  "linear-gradient(45deg, #FF6A88, #FFC3A0, #FFF6A3)" /* Soft Pink Glow */,
  "linear-gradient(45deg, #4CA1AF, #C4E0E5, #F8F9FA)" /* Aqua Mist */,
  "linear-gradient(45deg, #FBD3E9, #BB377D, #9D50BB)" /* Rosy Purple */,
  "linear-gradient(45deg, #FF9A8B, #FF6A88, #FFEBB7)" /* Peachy Gradient */,
  "linear-gradient(45deg, #667EEA, #764BA2, #D4FC79)" /* Cool Indigo */,
  "linear-gradient(45deg, #36D1DC, #5B86E5, #92FE9D)" /* Ocean Blues */,
  "linear-gradient(45deg, #FF512F, #DD2476, #FCEE21)" /* Fiery Coral */,
  "linear-gradient(45deg, #FF9966, #FF5E62, #FDEB71)" /* Vibrant Coral */,
  "linear-gradient(45deg, #00C9FF, #92FE9D, #FAFFD1)" /* Fresh Aqua */,
  "linear-gradient(45deg, #FC466B, #3F5EFB, #FFC371)" /* Electric Sunset */,
  "linear-gradient(45deg, #12C2E9, #C471ED, #F64F59)" /* Aurora Blend */,
  "linear-gradient(45deg, #F7971E, #FFD200, #C3FFD8)" /* Golden Sunshine */,
  "linear-gradient(45deg, #8E2DE2, #4A00E0, #F8FFAE)" /* Vibrant Violet */,
  "linear-gradient(45deg, #FFE000, #799F0C, #56AB2F)" /* Lime Freshness */,
  "linear-gradient(45deg, #1FA2FF, #12D8FA, #A1FFCE)" /* Sky Blue Blend */,
  "linear-gradient(45deg, #FF5F6D, #FFC371, #FFF3B0)" /* Tropical Sunrise */,
  "linear-gradient(45deg, #DA4453, #89216B, #F9D423)" /* Velvet Red */,
  "linear-gradient(45deg, #6EE7B7, #3B82F6, #E0E7FF)" /* Aqua Calmness */,
  "linear-gradient(45deg, #FF4E50, #F9D423, #FFC3A0)" /* Vibrant Sunrise */,
  "linear-gradient(45deg, #C33764, #1D2671, #6DD5FA)" /* Deep Raspberry */,
  "linear-gradient(45deg, #FAD961, #F76B1C, #F8F9F9)" /* Orange Blaze */,
  "linear-gradient(45deg, #FC5C7D, #6A82FB, #FFE29F)" /* Passion Blend */,
  "linear-gradient(45deg, #ED4264, #FFEDBC, #FAF4D3)" /* Cherry Burst */,
  "linear-gradient(45deg, #0093E9, #80D0C7, #D9FAE3)" /* Ocean Breeze */,
  "linear-gradient(45deg, #FBAB7E, #F7CE68, #FCEBEB)" /* Peach Gold */,
  "linear-gradient(45deg, #2193B0, #6DD5ED, #D4FC79)" /* Calm Ocean */,
  "linear-gradient(45deg, #A770EF, #FDB99B, #FFF6E9)" /* Dreamy Sunset */,
  "linear-gradient(45deg, #74EBD5, #9FACE6, #EBF8FF)" /* Soft Teal */,
];

const randomGradients = [
  "linear-gradient(45deg, #9AE63D, #6A6E93, #AA65FD, #74192C)",
  "linear-gradient(45deg, #6B4BDB, #023CD8, #A125C4, #7F421B)",
  "linear-gradient(45deg, #4BDADF, #9D6E11, #E4BA64, #7A4105)",
  "linear-gradient(45deg, #FB2238, #47ED31, #520FCA, #4532CA)",
  "linear-gradient(45deg, #5AD402, #689A0F, #ACF41B, #4BDC56)",
  "linear-gradient(45deg, #F0F7EA, #F416DE, #D4100E, #F36D1C)",
  "linear-gradient(45deg, #E77EC9, #C8B55C, #FE469F, #96CE0D)",
  "linear-gradient(45deg, #8E25E7, #A108A2, #299124, #5DBA2F)",
  "linear-gradient(45deg, #10ADF3, #20F47E, #37ECF7, #EC695C)",
  "linear-gradient(45deg, #FA88FE, #DC828F, #BDEE81, #80C704)",
  "linear-gradient(45deg, #215D18, #CB144D, #5B08F2, #539EED)",
  "linear-gradient(45deg, #4DB4B6, #978214, #299088, #3EFC63)",
  "linear-gradient(45deg, #103E1F, #6AB5C4, #627CFE, #CCCF8A)",
  "linear-gradient(45deg, #9541AA, #A14897, #898A5A, #81DE74)",
  "linear-gradient(45deg, #1FE656, #2707C3, #099F57, #293074)",
  "linear-gradient(45deg, #57D2DA, #701F8D, #0D2B9F, #B91084)",
  "linear-gradient(45deg, #44905D, #0FEDF7, #CB9EB4, #1B1ED8)",
  "linear-gradient(45deg, #4EB34B, #FC1850, #404C83, #8A9D0E)",
  "linear-gradient(45deg, #35E9D6, #9D1D56, #8E278B, #42882E)",
  "linear-gradient(45deg, #CAA79E, #670175, #84F58A, #A300D6)",
  "linear-gradient(45deg, #52ADC6, #5A9377, #73EFD4, #6B0085)",
  "linear-gradient(45deg, #9F86FC, #8C79B7, #2E3D4C, #417AE9)",
  "linear-gradient(45deg, #DE2238, #651B0D, #EB4602, #66C77E)",
  "linear-gradient(45deg, #89250C, #BFC917, #938956, #DC99E9)",
  "linear-gradient(45deg, #E918F2, #5C9468, #D3B745, #C8AD9C)",
];

const ColorPalette = (props: {
  templateConfig: IPostTemplateConfig;
  callBack: (
    postTemplateConfig: IPostTemplateConfig
  ) => undefined | void | null;
}) => {
  const [postTemplateConfig, setPostTemplateConfig] =
    useState<IPostTemplateConfig>(props.templateConfig);

  useEffect(() => {
    if (!deepEqual(postTemplateConfig, props.templateConfig)) {
      props.callBack(postTemplateConfig);
    }
  }, [postTemplateConfig]);

  return (
    <Box sx={{ padding: 2, border: "1px solid #ccc", borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom>
        Edit Background
      </Typography>

      {/* Solid Colors Section */}
      <Typography variant="subtitle1" gutterBottom>
        Solid Color
      </Typography>
      <Grid2 container spacing={2}>
        {solidColors.map((color, index) => (
          <Grid2 key={index}>
            <Button
              sx={{
                width: "40px",
                height: "40px",
                backgroundColor: color,
                borderRadius: "4px",
                cursor: "pointer",
              }}
              onClick={() =>
                setPostTemplateConfig({
                  ...props.templateConfig,
                  backgroundColor: color,
                })
              }
            />
          </Grid2>
        ))}
      </Grid2>

      {/* Gradient Colors Section */}
      <Typography variant="subtitle1" gutterBottom sx={{ marginTop: 3 }}>
        Gradient Color
      </Typography>
      <Grid2 container spacing={2}>
        {gradientColors.map((gradient, index) => (
          <Grid2 key={index}>
            <Button
              sx={{
                width: "40px",
                height: "40px",
                background: gradient,
                borderRadius: "4px",
                cursor: "pointer",
              }}
              onClick={() =>
                setPostTemplateConfig({
                  ...props.templateConfig,
                  backgroundColor: gradient,
                })
              }
            />
          </Grid2>
        ))}
      </Grid2>

      <Typography variant="subtitle1" gutterBottom sx={{ marginTop: 3 }}>
        Diagonal Tri Color
      </Typography>
      <Grid2 container spacing={2}>
        {gradientColorsDiagonal.map((gradient, index) => (
          <Grid2 key={index}>
            <Button
              sx={{
                width: "40px",
                height: "40px",
                background: gradient,
                borderRadius: "4px",
                cursor: "pointer",
              }}
              onClick={() =>
                setPostTemplateConfig({
                  ...props.templateConfig,
                  backgroundColor: gradient,
                })
              }
            />
          </Grid2>
        ))}
      </Grid2>

      <Typography variant="subtitle1" gutterBottom sx={{ marginTop: 3 }}>
        Random Gradients
      </Typography>
      <Grid2 container spacing={2}>
        {randomGradients.map((gradient, index) => (
          <Grid2 key={index}>
            <Button
              sx={{
                width: "40px",
                height: "40px",
                background: gradient,
                borderRadius: "4px",
                cursor: "pointer",
              }}
              onClick={() =>
                setPostTemplateConfig({
                  ...props.templateConfig,
                  backgroundColor: gradient,
                })
              }
            />
          </Grid2>
        ))}
      </Grid2>
    </Box>
  );
};

export default ColorPalette;
