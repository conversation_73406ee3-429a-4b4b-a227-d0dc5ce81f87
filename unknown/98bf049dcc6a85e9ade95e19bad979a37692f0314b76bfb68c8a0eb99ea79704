import Avatar from "@mui/material/Avatar";
import { useEffect, useState } from "react";

const UserAvatar = (props: {
  fullname: string;
  style?: React.CSSProperties;
  profileImage?: string;
}) => {
  const [avatarSrc, setAvatarSrc] = useState<string>("");

  const getAvatarText = (fullname: string): string => {
    const nameParts = fullname.trim().split(/\s+/);
    const firstInitial = nameParts[0]?.charAt(0).toUpperCase() || "";
    const lastInitial =
      nameParts[nameParts.length - 1]?.charAt(0).toUpperCase() || "";
    return `${firstInitial}${lastInitial}` || "NA";
  };

  useEffect(() => {
    const fetchImageAsBase64 = async () => {
      try {
        if (props.profileImage) {
          const response = await fetch(props.profileImage, { mode: "no-cors" });
          const blob = await response.blob();
          const reader = new FileReader();
          reader.onloadend = () => {
            setAvatarSrc(reader.result as string);
          };
          reader.readAsDataURL(blob);
        }
      } catch (error) {}
    };

    if (props.profileImage) {
      fetchImageAsBase64();
    }
  }, []);

  return avatarSrc ? (
    <Avatar src={avatarSrc} sx={{ ...props.style }} alt="Avatar"></Avatar>
  ) : (
    <Avatar sx={{ ...props.style }}>{getAvatarText(props.fullname)}</Avatar>
  );
};

export default UserAvatar;
