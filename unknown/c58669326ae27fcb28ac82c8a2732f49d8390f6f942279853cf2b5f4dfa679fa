import React, { useEffect, useState, FunctionComponent } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import PageProps from "../../models/PageProps.interface";
import { useDispatch } from "react-redux";
import { logOut } from "../../actions/auth.actions";

const UnAuthorized: FunctionComponent<PageProps> = ({ title }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const logoutUser = () => dispatch<any>(logOut());
  const [seconds, setSeconds] = useState<number>(5);

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds((prev) => prev - 1);
    }, 1000);

    const timeout = setTimeout(() => {
      logoutUser();
    }, 5 * 1000);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [navigate]);

  return (
    <div
      style={{ display: "flex", height: "100vh", backgroundColor: "#f8f9fa" }}
    >
      {/* Main content */}
      <div
        style={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <ErrorOutlineIcon
          style={{ fontSize: 80, color: "var(--secondaryColor)" }}
        />
        <h1 style={{ color: "var(--secondaryColor)", margin: "1rem 0" }}>
          Un-Authorized / No Access
        </h1>
        <p style={{ color: "#777" }}>
          You will be logged out in <strong>{seconds}</strong> second
          {seconds !== 1 ? "s" : ""}...
        </p>
        <Button className="tableActionBtn" onClick={() => logoutUser()}>
          Go to Login Now
        </Button>
      </div>
    </div>
  );
};

export default UnAuthorized;
