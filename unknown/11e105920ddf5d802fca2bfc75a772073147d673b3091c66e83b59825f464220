import React from "react";
import { Box, Typography, Avatar, Rating } from "@mui/material";

const FeedbackCard = () => {
  return (
    <Box
      sx={{
        position: "relative",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: 4,
        borderRadius: 3,
        boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.2)",
        backgroundColor: "#fff",
        width: 250,
        height: 250,
        margin: "auto",
      }}
    >
      {/* Background Text */}
      {/* <Typography
        variant="h1"
        sx={{
          position: "absolute",
          top: "-60px",
          left: "-20px",
          fontSize: "8rem",
          fontWeight: "bold",
          color: "rgba(0, 0, 0, 0.1)",
          zIndex: 1,
        }}
      >
        feedback
      </Typography> */}

      {/* Main Content */}
      <Box
        sx={{
          zIndex: 2,
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
        }}
      >
        {/* Avatar and Name */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Avatar sx={{ width: 50, height: 50, mr: 2 }}>L</Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Lorem Ipsum
            </Typography>
          </Box>
        </Box>

        {/* Rating */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Rating value={5} readOnly size="small" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            5 stars
          </Typography>
        </Box>

        {/* Review Text */}
        <Typography variant="body2" color="text.secondary" mb={2}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed diam
          nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat
          volutpat.
        </Typography>

        {/* Like Icon */}
        {/* <Box
          sx={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography variant="body2" sx={{ mr: 1 }}>
            👍
          </Typography>
          <Typography variant="body2">Liked by others</Typography>
        </Box> */}
      </Box>

      {/* Floating Heart Icon */}
      <Box
        sx={{
          position: "absolute",
          top: "-20px",
          right: "-20px",
          width: 50,
          height: 50,
          backgroundColor: "#4caf50",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "#fff",
          boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.2)",
        }}
      >
        ❤️
      </Box>
    </Box>
  );
};

export default FeedbackCard;
