import React from "react";
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
  Box,
} from "@mui/material";
import { Grid, Avatar, CircularProgress, Badge } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import MediaGallery from "../mediaGallery/mediaGallery.component";
import { IMissingInformationContent } from "../../screens/businessManagement/businessSummary/businessSummary.screen";
import { MISSING_INFORMATION } from "../../constants/application.constant";
import IconOnAvailability from "../iconOnAvailability/iconOnAvailability.component";
import LocationOnRoundedIcon from "@mui/icons-material/LocationOnRounded";
import MovieIcon from "@mui/icons-material/Movie";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";

const RegularHoursTable = (props: {
  regularHours: any;
  missingInformation: IMissingInformationContent[];
}) => {
  const toTitleCase = (str: string): string => {
    if (!str) return "";
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const getCustomOpenCloseHours = (period: any) => {
    let customTime = "";
    if (Object.keys(period.openTime).length > 0) {
      customTime = `${period.openTime.hours}:${
        period.openTime.minutes || "00"
      } - `;
    } else {
      customTime = `00:00 - `;
    }

    if (Object.keys(period.closeTime).length > 0) {
      customTime += `${period.closeTime.hours}:${
        period.closeTime.minutes || "00"
      }`;
    } else {
      customTime += `00:00`;
    }

    return customTime;
  };

  return (
    <Card className="boxShadowNone">
      <CardContent>
        <Grid container spacing={2}>
          {/* Storefront Photos Section */}
          <Grid item xs={12} md={7}>
            <Card variant="outlined">
              <Box sx={{ overflowX: "auto" }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <strong>Day</strong>
                      </TableCell>
                      <TableCell>
                        <strong>Status</strong>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {props.regularHours.periods.map(
                      (period: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>
                            {toTitleCase(period.openDay || "")}
                          </TableCell>
                          <TableCell>
                            {period.closeTime?.hours === 24
                              ? "Open 24 Hours"
                              : getCustomOpenCloseHours(period)}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default RegularHoursTable;
