import Avatar from "@mui/material/Avatar";
import { Box, Drawer, Grid, Grid2, Stack } from "@mui/material";

const GenericDrawer = (props: {
  component: React.ReactNode;
  isShow: boolean;
  callback: () => undefined | null | void;
}) => {
  return (
    <Drawer
      anchor={"right"}
      open={props.isShow}
      ModalProps={
        {
          // onBackdropClick: () => props.callback(),
          // setOpenAddEdit({ isShow: false, data: null, userId: 0 }),
        }
      }
      sx={{
        "& .MuiDrawer-paper": {
          minWidth: "400px",
          maxWidth: "400px", // Set the max width
          width: "100%", // Ensure the drawer does not exceed the max width
        },
        zIndex: (theme) => {
          return theme.zIndex.drawer + 1;
        },
      }}
      // PaperProps={{ style: { width: window.innerWidth * 0.25 } }}
    >
      <Box className="height100">{props.component}</Box>
    </Drawer>
  );
};

export default GenericDrawer;
