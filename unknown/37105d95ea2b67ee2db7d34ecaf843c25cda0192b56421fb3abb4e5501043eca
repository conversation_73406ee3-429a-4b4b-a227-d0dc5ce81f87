import React from "react";
import {
  Box,
  Typography,
  Avatar,
  Rating,
  Container,
  CssBaseline,
} from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import StarIcon from "@mui/icons-material/Star";
import UserAvatar from "../../../../userAvatar/userAvatar.component";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";

//Css Import
import "../testimonialCard5/testimonialCard5.component.style.css";

const TestimonialCard5 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      sx={{
        background: `${
          props.templateConfig.backgroundColor
        } url(${require("../../../../../assets/feedbackBackgrouns/5.png")}) no-repeat center/cover`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        color: "#fff",
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        border: 1,
        borderColor: "black",
      }}
    >
      <Container>
        <CssBaseline />
        <Box>
          {/* Main Content */}
          <Box className="testimonialInnerFive">
            <Box className="quoteMark">
              <img
                src={require("../../../../../assets/feedbackBackgrouns/5Quote.png")}
              />
            </Box>
            {/* Avatar and Name */}
            <Box>
              {/* Rating */}
              <Box className="marB20">
                {props.templateConfig.showRating && (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      mb: 2,
                    }}
                  >
                    <RatingsStar starRating={props.templateConfig.starRating} />
                  </Box>
                )}
              </Box>
              {/* Review Text */}
              <Typography
                variant="body2"
                mb={2}
                sx={{
                  color: `${props.templateConfig.fontColor}`,
                }}
              >
                {props.templateConfig.comment}
              </Typography>
              <Box className="testmonialUserInfo">
                <Box>
                  {props.templateConfig.showAvatar && (
                    <UserAvatar
                      profileImage={props.templateConfig.reviewerImage}
                      fullname={props.templateConfig.reviewerName}
                      style={{
                        width: 60,
                        height: 60,
                        margin: "0 auto 10px",
                        background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
                      }}
                    />
                  )}
                </Box>
                <Box>
                  <Typography className="testmonialUserName">
                    {props.templateConfig.reviewerName}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default TestimonialCard5;
